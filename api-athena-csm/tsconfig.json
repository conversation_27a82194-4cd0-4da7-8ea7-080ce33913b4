{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/config/*": ["config/*"], "@/modules/*": ["modules/*"], "@/shared/*": ["shared/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}