import { PrismaClient, UserRole, UserStatus, TicketStatus, TicketType, TicketPriority, ChannelType, ChannelStatus, SLAStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create a sample company
  const company = await prisma.company.create({
    data: {
      name: 'AthenaFS Demo Company',
      description: 'Demo company for testing CMS system',
      settings: {
        timezone: 'Asia/Ho_Chi_Minh',
        businessHours: {
          start: '08:00',
          end: '17:00',
          days: [1, 2, 3, 4, 5], // Monday to Friday
        },
      },
    },
  });

  console.log('✅ Created company:', company.name);

  // Create departments
  const cskh = await prisma.department.create({
    data: {
      name: 'Chăm sóc khách hàng',
      description: 'Phòng chăm sóc khách hàng',
      companyId: company.id,
    },
  });

  const tech = await prisma.department.create({
    data: {
      name: '<PERSON><PERSON> thuật',
      description: '<PERSON>òng kỹ thuật',
      companyId: company.id,
    },
  });

  console.log('✅ Created departments');

  // Create users
  const hashedPassword = await bcrypt.hash('password123', 10);

  const superAdmin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      role: UserRole.SUPER_ADMIN,
      status: UserStatus.ACTIVE,
      companyId: company.id,
    },
  });

  const csAdmin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'CS',
      lastName: 'Admin',
      role: UserRole.CS_ADMIN,
      status: UserStatus.ACTIVE,
      companyId: company.id,
      departmentId: cskh.id,
    },
  });

  const csAgent = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'CS',
      lastName: 'Agent',
      role: UserRole.CS_AGENT,
      status: UserStatus.ACTIVE,
      companyId: company.id,
      departmentId: cskh.id,
    },
  });

  // Update department leader
  await prisma.department.update({
    where: { id: cskh.id },
    data: { leaderId: csAdmin.id },
  });

  console.log('✅ Created users');

  // Create sample customer
  const customer = await prisma.customer.create({
    data: {
      cif: 'CIF000001',
      firstName: 'Nguyễn',
      lastName: 'Văn A',
      phone: '0901234567',
      email: '<EMAIL>',
      address: 'Hà Nội, Việt Nam',
      idNumber: '*********',
      companyId: company.id,
    },
  });

  console.log('✅ Created customer');

  // Create SLA
  const sla = await prisma.sLA.create({
    data: {
      name: 'Standard SLA',
      description: 'Standard service level agreement',
      ticketType: TicketType.INQUIRY,
      priority: TicketPriority.MEDIUM,
      firstResponseTimeHours: 2,
      resolutionTimeHours: 24,
      businessHoursOnly: true,
      status: SLAStatus.ACTIVE,
      companyId: company.id,
    },
  });

  console.log('✅ Created SLA');

  // Create channel
  const emailChannel = await prisma.channel.create({
    data: {
      name: 'Email Support',
      type: ChannelType.EMAIL,
      status: ChannelStatus.ACTIVE,
      config: {
        email: '<EMAIL>',
        provider: 'gmail',
      },
      companyId: company.id,
    },
  });

  // Assign agent to channel
  await prisma.channelAgent.create({
    data: {
      channelId: emailChannel.id,
      userId: csAgent.id,
    },
  });

  console.log('✅ Created channel and assigned agent');

  // Create sample ticket
  const ticket = await prisma.ticket.create({
    data: {
      ticketNumber: 'EMAIL000001',
      title: 'Yêu cầu hỗ trợ tài khoản',
      description: 'Khách hàng cần hỗ trợ đăng nhập tài khoản',
      type: TicketType.INQUIRY,
      priority: TicketPriority.MEDIUM,
      status: TicketStatus.WAIT,
      source: 'EMAIL',
      customerId: customer.id,
      creatorId: csAgent.id,
      companyId: company.id,
      departmentId: cskh.id,
      channelId: emailChannel.id,
      slaId: sla.id,
      tags: ['account', 'login'],
    },
  });

  // Create SLA tracking for the ticket
  const now = new Date();
  const firstResponseDue = new Date(now.getTime() + sla.firstResponseTimeHours * 60 * 60 * 1000);
  const resolutionDue = new Date(now.getTime() + sla.resolutionTimeHours * 60 * 60 * 1000);

  await prisma.sLATracking.create({
    data: {
      ticketId: ticket.id,
      slaId: sla.id,
      firstResponseDue,
      resolutionDue,
    },
  });

  console.log('✅ Created sample ticket with SLA tracking');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Sample accounts created:');
  console.log('Super Admin: <EMAIL> / password123');
  console.log('CS Admin: <EMAIL> / password123');
  console.log('CS Agent: <EMAIL> / password123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
