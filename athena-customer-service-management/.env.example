# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/athena_cms"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-secret-key-here
JWT_REFRESH_EXPIRES_IN=7d

# Sample Token for Development
SAMPLE_TOKEN=sample-token-for-development

# Email Providers Configuration
# Gmail API
GMAIL_CLIENT_ID=your-gmail-client-id
GMAIL_CLIENT_SECRET=your-gmail-client-secret
GMAIL_REDIRECT_URI=your-gmail-redirect-uri
GMAIL_REFRESH_TOKEN=your-gmail-refresh-token

# Outlook/Microsoft Graph API
OUTLOOK_CLIENT_ID=your-outlook-client-id
OUTLOOK_CLIENT_SECRET=your-outlook-client-secret
OUTLOOK_TENANT_ID=your-outlook-tenant-id
OUTLOOK_REDIRECT_URI=your-outlook-redirect-uri

# Messaging Providers Configuration
# Facebook Messenger
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_PAGE_ACCESS_TOKEN=your-facebook-page-access-token
FACEBOOK_VERIFY_TOKEN=your-facebook-verify-token
FACEBOOK_WEBHOOK_URL=your-facebook-webhook-url

# Facebook Fanpage
FACEBOOK_FANPAGE_ACCESS_TOKEN=your-fanpage-access-token
FACEBOOK_FANPAGE_ID=your-fanpage-id

# Zalo Official Account
ZALO_APP_ID=your-zalo-app-id
ZALO_APP_SECRET=your-zalo-app-secret
ZALO_ACCESS_TOKEN=your-zalo-access-token
ZALO_WEBHOOK_URL=your-zalo-webhook-url

# Telegram Bot
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_WEBHOOK_URL=your-telegram-webhook-url
TELEGRAM_WEBHOOK_SECRET=your-telegram-webhook-secret

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
