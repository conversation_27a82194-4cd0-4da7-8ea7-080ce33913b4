{"name": "athena-customer-service-api", "version": "1.0.0", "description": "Customer Service Management API for AthenaFS", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "keywords": ["customer-service", "cms", "multi-tenant", "nodejs", "typescript", "express", "prisma"], "author": "AthenaFS", "license": "MIT", "dependencies": {"@azure/msal-node": "^2.6.0", "@prisma/client": "^5.7.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.6", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "facebook-nodejs-business-sdk": "^19.0.0", "googleapis": "^128.0.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "microsoft-graph": "^3.0.7", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "node-telegram-bot-api": "^0.64.0", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.13", "@types/node": "^20.10.4", "@types/node-telegram-bot-api": "^0.64.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "prisma": "^5.7.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}