var dataExamine = {
    foodNameListSearch: []
};

// <PERSON><PERSON><PERSON> lấy examine_id từ URL hoặc biến global
function getExamineIdFromUrl() {
    // Thử lấy từ biến global trước
    if (typeof currentExamineId !== 'undefined' && currentExamineId) {
        return currentExamineId;
    }
    
    // N<PERSON>u không có, lấy từ URL
    const pathArray = window.location.pathname.split('/');
    const editIndex = pathArray.indexOf('edit');
    if (editIndex !== -1 && editIndex + 1 < pathArray.length) {
        return pathArray[editIndex + 1];
    }
    return null;
}

function chooseMenuExample() {
    try {
        let id = 1;
        if (menuExamine.length > 0) {
            id = menuExamine[menuExamine.length - 1].id + 1;
        }
        let menu_example_id = parseInt($("#menuExample_id").val());
        if (menu_example_id) {
            for (let menu of menuExample) {
                if (menu.id == menu_example_id) {
                    if(menu.detail && menu.detail.length > 0){
                        for(let meal of menu.detail){
                            if(!meal.courses){
                                meal.courses = [{ id: 1, name: meal.name_course || '' }];
                            }
                            if(meal.listFood && meal.listFood.length > 0){
                                for(let food of meal.listFood){
                                    if(!food.course_id) food.course_id = 1;
                                }
                            }
                        }
                    }
                    let menuNew = {
                        id: id,
                        name: menu.name_menu,
                        detail: JSON.parse(menu.detail),
                        created_at: new Date().toISOString(),
                    };
                    menuExamine.push(menuNew);
                    let newItem = {
                        label: menuNew.name,
                        value: menuNew.id
                    }
                    addNewOptionToVirtualSelect('menu_id', newItem, true);
                    break;
                }
            }
            $('#tb_menu tbody').empty();
            $('#tb_menu').show();
            generateTableMenu(id);
        } else {
            toarstError("Vui lòng chọn mẫu!");
        }
    } catch (error) {
        console.log('error', error);
    }
}

// Danh sách tất cả các trường có thể hiển thị (không bao gồm name vì đã cố định)
const availableColumns = {
    // Thông tin cơ bản
    'ten': { label: 'Tên tiếng Việt', group: 'basic', default: false },
    'code': { label: 'Mã thực phẩm', group: 'basic', default: false },
    'weight': { label: 'Khối lượng (g)', group: 'basic', default: true },
    'edible': { label: 'Phần ăn được (%)', group: 'basic', default: false },

    // Chất dinh dưỡng chính
    'energy': { label: 'Năng lượng (kcal)', group: 'main_nutrients', default: true },
    'water': { label: 'Nước (g)', group: 'main_nutrients', default: false },
    'protein': { label: 'Protein (g)', group: 'main_nutrients', default: true },
    'fat': { label: 'Chất béo (g)', group: 'main_nutrients', default: true },
    'carbohydrate': { label: 'Carbohydrate (g)', group: 'main_nutrients', default: true },
    'fiber': { label: 'Chất xơ (g)', group: 'main_nutrients', default: false },
    'ash': { label: 'Tro (g)', group: 'main_nutrients', default: false },

    // Khoáng chất
    'calci': { label: 'Canxi (mg)', group: 'minerals', default: false },
    'phosphorous': { label: 'Phospho (mg)', group: 'minerals', default: false },
    'fe': { label: 'Sắt (mg)', group: 'minerals', default: false },
    'zinc': { label: 'Kẽm (mg)', group: 'minerals', default: false },
    'sodium': { label: 'Natri (mg)', group: 'minerals', default: false },
    'potassium': { label: 'Kali (mg)', group: 'minerals', default: false },
    'magnesium': { label: 'Magie (mg)', group: 'minerals', default: false },
    'manganese': { label: 'Mangan (mg)', group: 'minerals', default: false },
    'copper': { label: 'Đồng (mg)', group: 'minerals', default: false },
    'selenium': { label: 'Selen (μg)', group: 'minerals', default: false },
    
    // Axit béo
    'total_saturated_fat': { label: 'Axit béo bão hòa (g)', group: 'fatty_acids', default: false },
    'mufa': { label: 'Axit béo không bão hòa đơn (g)', group: 'fatty_acids', default: false },
    'fufa': { label: 'Axit béo không bão hòa đa (g)', group: 'fatty_acids', default: false },
    'pufa': { label: 'PUFA - Axit béo không bão hòa đa (g)', group: 'fatty_acids', default: false },
    'oleic': { label: 'Oleic (g)', group: 'fatty_acids', default: false },
    'linoleic': { label: 'Linoleic (g)', group: 'fatty_acids', default: false },
    'linolenic': { label: 'Linolenic (g)', group: 'fatty_acids', default: false },
    'arachidonic': { label: 'Arachidonic (g)', group: 'fatty_acids', default: false },
    'trans_fatty_acids': { label: 'Trans fat (g)', group: 'fatty_acids', default: false },
    'epa': { label: 'EPA (g)', group: 'fatty_acids', default: false },
    'dha': { label: 'DHA (g)', group: 'fatty_acids', default: false },
    'cholesterol': { label: 'Cholesterol (mg)', group: 'fatty_acids', default: false },
    
    // Protein & Amino acid
    'animal_protein': { label: 'Protein động vật (g)', group: 'amino_acids', default: false },
    'lysin': { label: 'Lysin (mg)', group: 'amino_acids', default: false },
    'methionin': { label: 'Methionin (mg)', group: 'amino_acids', default: false },
    'tryptophan': { label: 'Tryptophan (mg)', group: 'amino_acids', default: false },
    'phenylalanin': { label: 'Phenylalanin (mg)', group: 'amino_acids', default: false },
    'threonin': { label: 'Threonin (mg)', group: 'amino_acids', default: false },
    'isoleucine': { label: 'Isoleucine (mg)', group: 'amino_acids', default: false },
    'leucine': { label: 'Leucine (mg)', group: 'amino_acids', default: false },
    'valine': { label: 'Valine (mg)', group: 'amino_acids', default: false },
    'arginine': { label: 'Arginine (mg)', group: 'amino_acids', default: false },
    'histidine': { label: 'Histidine (mg)', group: 'amino_acids', default: false },
    'alanine': { label: 'Alanine (mg)', group: 'amino_acids', default: false },
    'aspartic_acid': { label: 'Axit aspartic (mg)', group: 'amino_acids', default: false },
    'glutamic_acid': { label: 'Axit glutamic (mg)', group: 'amino_acids', default: false },
    'glycine': { label: 'Glycine (mg)', group: 'amino_acids', default: false },
    'proline': { label: 'Proline (mg)', group: 'amino_acids', default: false },
    'serine': { label: 'Serine (mg)', group: 'amino_acids', default: false },
    'tyrosine': { label: 'Tyrosine (mg)', group: 'amino_acids', default: false },
    'cystine': { label: 'Cystine (mg)', group: 'amino_acids', default: false },
    
    // Vitamin
    'vitamin_a_rae': { label: 'Vitamin A (μg RAE)', group: 'vitamins', default: false },
    'vitamin_b6': { label: 'Vitamin B6 (mg)', group: 'vitamins', default: false },
    'vitamin_b12': { label: 'Vitamin B12 (μg)', group: 'vitamins', default: false },
    'vitamin_c': { label: 'Vitamin C (mg)', group: 'vitamins', default: false },
    'vitamin_e': { label: 'Vitamin E (mg)', group: 'vitamins', default: false },
    'vitamin_k': { label: 'Vitamin K (μg)', group: 'vitamins', default: false },
    'vitamin_d': { label: 'Vitamin D (μg)', group: 'vitamins', default: false },
    'niacin': { label: 'Niacin (mg)', group: 'vitamins', default: false },
    'pantothenic_acid': { label: 'Axit pantothenic (mg)', group: 'vitamins', default: false },
    'biotin': { label: 'Biotin (μg)', group: 'vitamins', default: false },
    'b_carotene': { label: 'Beta-carotene (μg)', group: 'vitamins', default: false },
    'a_carotene': { label: 'Alpha-carotene (μg)', group: 'vitamins', default: false },
    'b_cryptoxanthin': { label: 'Beta-cryptoxanthin (μg)', group: 'vitamins', default: false },
    
    // Đường
    'total_sugar': { label: 'Tổng đường (g)', group: 'sugars', default: false },
    'glucose': { label: 'Glucose (g)', group: 'sugars', default: false },
    'fructose': { label: 'Fructose (g)', group: 'sugars', default: false },
    'sucrose': { label: 'Sucrose (g)', group: 'sugars', default: false },
    'lactose': { label: 'Lactose (g)', group: 'sugars', default: false },
    'maltose': { label: 'Maltose (g)', group: 'sugars', default: false },
    'galactose': { label: 'Galactose (g)', group: 'sugars', default: false },

    // Vi lượng khác
    'fluoride': { label: 'Fluoride (mg)', group: 'minerals', default: false },
    'iodine': { label: 'Iod (μg)', group: 'minerals', default: false },
    
    // Carotenoid và chất chống oxy hóa
    'lycopene': { label: 'Lycopene (μg)', group: 'antioxidants', default: false },
    'lutein_zeaxanthin': { label: 'Lutein + Zeaxanthin (μg)', group: 'antioxidants', default: false },
    'caroten': { label: 'Carotenoid (μg)', group: 'antioxidants', default: false },
    
    // Isoflavone và phytoestrogen
    'total_isoflavone': { label: 'Tổng Isoflavone (mg)', group: 'phytonutrients', default: false },
    'daidzein': { label: 'Daidzein (mg)', group: 'phytonutrients', default: false },
    'genistein': { label: 'Genistein (mg)', group: 'phytonutrients', default: false },
    'glycetin': { label: 'Glycetin (mg)', group: 'phytonutrients', default: false },
    'phytosterol': { label: 'Phytosterol (mg)', group: 'phytonutrients', default: false },
    
    // Purine và chất chuyển hóa
    'purine': { label: 'Purine (mg)', group: 'metabolites', default: false },
    
    // Protein bổ sung
    'unanimal_protein': { label: 'Protein thực vật (g)', group: 'amino_acids', default: false },
    
    // Lipid bổ sung
    'animal_lipid': { label: 'Lipid động vật (g)', group: 'fatty_acids', default: false },
    'unanimal_lipid': { label: 'Lipid thực vật (g)', group: 'fatty_acids', default: false },
    
    // Axit béo bão hòa bổ sung
    'palmitic': { label: 'Axit Palmitic (g)', group: 'fatty_acids', default: false },
    'margaric': { label: 'Axit Margaric (g)', group: 'fatty_acids', default: false },
    'stearic': { label: 'Axit Stearic (g)', group: 'fatty_acids', default: false },
    'arachidic': { label: 'Axit Arachidic (g)', group: 'fatty_acids', default: false },
    'behenic': { label: 'Axit Behenic (g)', group: 'fatty_acids', default: false },
    'lignoceric': { label: 'Axit Lignoceric (g)', group: 'fatty_acids', default: false },
    
    // Axit béo không bão hòa đơn bổ sung
    'myristoleic': { label: 'Axit Myristoleic (g)', group: 'fatty_acids', default: false },
    'palmitoleic': { label: 'Axit Palmitoleic (g)', group: 'fatty_acids', default: false },
    
    // Vitamin bổ sung
    'riboflavin': { label: 'Riboflavin - B2 (mg)', group: 'vitamins', default: false },
    'thiamine': { label: 'Thiamine - B1 (mg)', group: 'vitamins', default: false },
    'folic_acid': { label: 'Axit Folic (μg)', group: 'vitamins', default: false }
};

const columnGroups = {
    'basic': 'Thông tin cơ bản',
    'main_nutrients': 'Chất dinh dưỡng chính',
    'minerals': 'Khoáng chất',
    'fatty_acids': 'Axit béo',
    'amino_acids': 'Protein & Amino acid',
    'vitamins': 'Vitamin',
    'sugars': 'Đường',
    'antioxidants': 'Chất chống oxy hóa',
    'phytonutrients': 'Phytonutrient',
    'metabolites': 'Chất chuyển hóa'
};

// Biến lưu cấu hình hiển thị cột hiện tại
let currentDisplayConfig = {
    visible_columns: ['weight', 'energy', 'protein', 'fat', 'carbohydrate'],
    column_order: ['weight', 'energy', 'protein', 'fat', 'carbohydrate']
};

// Hàm tạo dropdown thực phẩm với API từ benh-nhan
function generateFoodName(elementId) {
    try {
        if (typeof VirtualSelect === 'undefined') {
            console.error('VirtualSelect is not loaded');
            return;
        }

        VirtualSelect.init({
            ele: '#' + elementId,
            options: [],
            placeholder: 'Tìm kiếm thực phẩm...',
            search: true,
            searchPlaceholderText: 'Nhập tên thực phẩm...',
            noOptionsText: 'Không tìm thấy thực phẩm nào',
            noSearchResultsText: 'Không có kết quả tìm kiếm',
            searchByStartsWith: false,
            optionsCount: 10,
            hasOptionDescription: true,
            markSearchResults: true,
            onServerSearch: function(search, virtualSelect) {
                searchFoodFromAPI(search, virtualSelect, elementId);
            }
        });
    } catch (error) {
        console.error('Error initializing food dropdown:', error);
    }
}

// Hàm tìm kiếm thực phẩm từ API
function searchFoodFromAPI(search, virtualSelect, elementId) {
    if (!search || search.length < 2) {
        return;
    }

    const food_type = $('#food_type').val() || '';
    const food_year = $('#food_year').val() || '';

    $.ajax({
        url: '/examine/api/foods',
        method: 'GET',
        data: {
            search: search,
            food_type: food_type,
            food_year: food_year
        },
        success: function(response) {
            if (response.success && response.data) {
                const options = response.data.map(food => ({
                    label: food.name,
                    value: food.id,
                    description: `${food.code || ''} - ${food.ten || ''}`,
                    data: food
                }));
                
                virtualSelect.setServerOptions(options);
            } else {
                virtualSelect.setServerOptions([]);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error searching foods:', error);
            virtualSelect.setServerOptions([]);
        }
    });
}

// Hàm tạo dropdown món ăn với API từ benh-nhan
function generateDishName(elementId) {
    try {
        if (typeof VirtualSelect === 'undefined') {
            console.error('VirtualSelect is not loaded');
            return;
        }

        VirtualSelect.init({
            ele: '#' + elementId,
            options: [],
            placeholder: 'Tìm kiếm món ăn...',
            search: true,
            searchPlaceholderText: 'Nhập tên món ăn...',
            noOptionsText: 'Không tìm thấy món ăn nào',
            noSearchResultsText: 'Không có kết quả tìm kiếm',
            searchByStartsWith: false,
            optionsCount: 10,
            hasOptionDescription: true,
            markSearchResults: true,
            onServerSearch: function(search, virtualSelect) {
                searchDishFromAPI(search, virtualSelect, elementId);
            }
        });
    } catch (error) {
        console.error('Error initializing dish dropdown:', error);
    }
}

// Hàm tìm kiếm món ăn từ API
function searchDishFromAPI(search, virtualSelect, elementId) {
    if (!search || search.length < 2) {
        return;
    }

    $.ajax({
        url: '/examine/api/dishes',
        method: 'GET',
        data: {
            search: search
        },
        success: function(response) {
            if (response.success && response.data) {
                const options = response.data.map(dish => ({
                    label: dish.name,
                    value: dish.id,
                    description: dish.description || dish.category || '',
                    data: dish
                }));

                virtualSelect.setServerOptions(options);
            } else {
                virtualSelect.setServerOptions([]);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error searching dishes:', error);
            virtualSelect.setServerOptions([]);
        }
    });
}

// Hàm cập nhật dropdown thực phẩm khi thay đổi filter
function updateFoodDropdown(elementId) {
    try {
        const virtualSelect = document.querySelector('#' + elementId);
        if (virtualSelect && virtualSelect.virtualSelect) {
            // Reset options
            virtualSelect.virtualSelect.setOptions([]);
        }
    } catch (error) {
        console.error('Error updating food dropdown:', error);
    }
}

// Hàm thêm thực phẩm vào thực đơn
function addFoodToMenu() {
    try {
        const menuTimeId = $('#menuTime_id').val();
        const foodId = $('#food_name').val();
        const weight = parseFloat($('#weight_food').val()) || 0;

        if (!menuTimeId) {
            toarstError('Vui lòng chọn giờ ăn!');
            return;
        }

        if (!foodId) {
            toarstError('Vui lòng chọn thực phẩm!');
            return;
        }

        if (weight <= 0) {
            toarstError('Vui lòng nhập khối lượng hợp lệ!');
            return;
        }

        // Lấy thông tin thực phẩm từ dropdown
        const foodSelect = document.querySelector('#food_name');
        if (!foodSelect || !foodSelect.virtualSelect) {
            toarstError('Không thể lấy thông tin thực phẩm!');
            return;
        }

        const selectedOption = foodSelect.virtualSelect.getSelectedOptions()[0];
        if (!selectedOption || !selectedOption.data) {
            toarstError('Không thể lấy thông tin thực phẩm!');
            return;
        }

        const foodData = selectedOption.data;

        // Tính toán dinh dưỡng theo khối lượng
        const ratio = weight / 100; // Dữ liệu dinh dưỡng tính theo 100g
        const calculatedFood = {
            id: Date.now(), // Tạo ID tạm thời
            id_food: foodData.id,
            name: foodData.name,
            weight: weight,
            energy: (parseFloat(foodData.energy) || 0) * ratio,
            protein: (parseFloat(foodData.protein) || 0) * ratio,
            animal_protein: (parseFloat(foodData.animal_protein) || 0) * ratio,
            fat: (parseFloat(foodData.fat) || 0) * ratio,
            unanimal_lipid: (parseFloat(foodData.unanimal_lipid) || 0) * ratio,
            carbohydrate: (parseFloat(foodData.carbohydrate) || 0) * ratio,
            course_id: 1 // Mặc định course đầu tiên
        };

        // Thêm vào menuExamine
        addFoodToMenuExamine(menuTimeId, calculatedFood);

        // Reset form
        $('#weight_food').val('');
        $('#food_name').val('').trigger('change');

        toarstMessage('Đã thêm thực phẩm vào thực đơn!');

    } catch (error) {
        console.error('Error adding food to menu:', error);
        toarstError('Có lỗi xảy ra khi thêm thực phẩm!');
    }
}

// Hàm thêm món ăn vào thực đơn
function addDishToMenu() {
    try {
        const menuTimeId = $('#dish_menuTime_id').val();
        const dishId = $('#dish_name').val();

        if (!menuTimeId) {
            toarstError('Vui lòng chọn giờ ăn!');
            return;
        }

        if (!dishId) {
            toarstError('Vui lòng chọn món ăn!');
            return;
        }

        // Lấy chi tiết thực phẩm của món ăn từ API
        $.ajax({
            url: `/examine/api/dish-foods/${dishId}`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data && response.data.length > 0) {
                    // Thêm từng thực phẩm của món ăn vào thực đơn
                    response.data.forEach(dishFood => {
                        const calculatedFood = {
                            id: Date.now() + Math.random(), // Tạo ID tạm thời unique
                            id_food: dishFood.food_id,
                            name: dishFood.food_name,
                            weight: parseFloat(dishFood.weight) || 0,
                            energy: parseFloat(dishFood.calculated_energy) || 0,
                            protein: parseFloat(dishFood.calculated_protein) || 0,
                            animal_protein: parseFloat(dishFood.calculated_animal_protein) || 0,
                            fat: parseFloat(dishFood.calculated_fat) || 0,
                            unanimal_lipid: parseFloat(dishFood.calculated_unanimal_lipid) || 0,
                            carbohydrate: parseFloat(dishFood.calculated_carbohydrate) || 0,
                            course_id: 1 // Mặc định course đầu tiên
                        };

                        addFoodToMenuExamine(menuTimeId, calculatedFood);
                    });

                    // Reset form
                    $('#dish_name').val('').trigger('change');

                    toarstMessage('Đã thêm món ăn vào thực đơn!');
                } else {
                    toarstError('Không thể lấy thông tin chi tiết món ăn!');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error getting dish foods:', error);
                toarstError('Có lỗi xảy ra khi lấy thông tin món ăn!');
            }
        });

    } catch (error) {
        console.error('Error adding dish to menu:', error);
        toarstError('Có lỗi xảy ra khi thêm món ăn!');
    }
}

// Hàm thêm thực phẩm vào menuExamine
function addFoodToMenuExamine(menuTimeId, foodData) {
    try {
        // Tìm menu hiện tại
        let currentMenu = getCurrentMenu();
        if (!currentMenu) {
            // Tạo menu mới nếu chưa có
            currentMenu = createNewMenu();
        }

        // Tìm menuTime tương ứng
        let menuTime = currentMenu.detail.find(mt => mt.id == menuTimeId);
        if (!menuTime) {
            toarstError('Không tìm thấy giờ ăn!');
            return;
        }

        // Đảm bảo có courses
        if (!menuTime.courses || menuTime.courses.length === 0) {
            menuTime.courses = [{ id: 1, name: '' }];
        }

        // Đảm bảo có listFood
        if (!menuTime.listFood) {
            menuTime.listFood = [];
        }

        // Thêm thực phẩm vào danh sách
        menuTime.listFood.push(foodData);

        // Cập nhật hiển thị
        updateMenuDisplay(currentMenu);

    } catch (error) {
        console.error('Error adding food to menuExamine:', error);
        throw error;
    }
}

// Hàm lấy menu hiện tại
function getCurrentMenu() {
    try {
        const menuId = $('#menu_id').val();
        if (!menuId || !window.menuExamine || window.menuExamine.length === 0) {
            return null;
        }

        return window.menuExamine.find(menu => menu.id == menuId);
    } catch (error) {
        console.error('Error getting current menu:', error);
        return null;
    }
}

// Hàm tạo menu mới
function createNewMenu() {
    try {
        const newMenu = {
            id: Date.now(),
            name: 'Thực đơn mới',
            detail: window.listMenuTime ? window.listMenuTime.map(time => ({
                id: time.id,
                name: time.name,
                courses: [{ id: 1, name: '' }],
                listFood: []
            })) : [],
            note: '',
            created_at: new Date().toISOString()
        };

        if (!window.menuExamine) {
            window.menuExamine = [];
        }

        window.menuExamine.push(newMenu);

        // Cập nhật dropdown menu
        updateMenuDropdown();

        return newMenu;
    } catch (error) {
        console.error('Error creating new menu:', error);
        return null;
    }
}

// Hàm cập nhật dropdown menu
function updateMenuDropdown() {
    try {
        if (!window.menuExamine || window.menuExamine.length === 0) {
            return;
        }

        const options = window.menuExamine.map(menu => ({
            label: menu.name,
            value: menu.id
        }));

        // Cập nhật VirtualSelect nếu có
        const menuSelect = document.querySelector('#menu_id');
        if (menuSelect && menuSelect.virtualSelect) {
            menuSelect.virtualSelect.setOptions(options);
            // Chọn menu cuối cùng
            const lastMenu = window.menuExamine[window.menuExamine.length - 1];
            menuSelect.virtualSelect.setValue(lastMenu.id);
        } else {
            // Khởi tạo VirtualSelect nếu chưa có
            VirtualSelect.init({
                ele: '#menu_id',
                options: options,
                selectedValue: options.length > 0 ? options[options.length - 1].value : null,
                placeholder: 'Chọn thực đơn'
            });
        }

    } catch (error) {
        console.error('Error updating menu dropdown:', error);
    }
}

// Hàm cập nhật hiển thị menu
function updateMenuDisplay(menu) {
    try {
        if (!menu) {
            $('#tb_menu').hide();
            return;
        }

        // Cập nhật tên menu
        $('#name_menu').val(menu.name);
        $('#name_menu_text').text(menu.name);
        $('#menu_example_note').val(menu.note || '');

        // Hiển thị bảng
        $('#tb_menu').show();

        // Tạo lại bảng
        generateTableMenu(menu.id);

    } catch (error) {
        console.error('Error updating menu display:', error);
    }
}

// Hàm tạo bảng thực đơn
function generateTableMenu(menuId) {
    try {
        const menu = window.menuExamine.find(m => m.id == menuId);
        if (!menu) {
            return;
        }

        // Xóa nội dung cũ
        $('#tb_menu tbody').empty();

        // Cập nhật header
        updateTableHeaderWithConfig();

        // Thêm các hàng dữ liệu
        addTemplateListMenuTime(menu.detail);

    } catch (error) {
        console.error('Error generating table menu:', error);
    }
}

// Hàm cập nhật header bảng
function updateTableHeaderWithConfig() {
    try {
        let headerHtml = `
            <tr>
                <th class="text-center">Bữa ăn</th>
                <th class="text-center">Tên món ăn</th>
        `;

        // Thêm header cho các cột thông tin thực phẩm được chọn
        currentDisplayConfig.visible_columns.forEach(columnKey => {
            const column = availableColumns[columnKey];
            if (column) {
                headerHtml += `<th class="text-center">${column.label}</th>`;
            }
        });

        headerHtml += `
                <th class="text-center">Thao tác</th>
            </tr>
        `;

        // Cập nhật header
        $('#tb_menu thead').html(headerHtml);

    } catch (error) {
        console.error('Error updating table header:', error);
    }
}

// Hàm thêm template cho danh sách menuTime
function addTemplateListMenuTime(listMenuTime) {
    try {
        if (!listMenuTime || listMenuTime.length === 0) {
            return;
        }

        let listFoodTotal = [];

        for (let mt of listMenuTime) {
            const totalRows = computeMenuTimeRowspan(mt);
            const colspanCount = currentDisplayConfig.visible_columns.length + 1;

            if (!mt.courses || mt.courses.length === 0) {
                mt.courses = [{ id: 1, name: mt.name_course || '' }];
                if (mt.listFood) {
                    mt.listFood.forEach(f => {
                        if (!f.course_id) {
                            f.course_id = 1;
                        }
                    });
                }
            }

            const firstCourse = mt.courses[0] || { id: 1, name: '' };
            const $firstRow = $('<tr/>')
                .attr('id', 'menu_time_' + mt.id)
                .addClass('text-center');

            $firstRow.append($('<td/>')
                .css({ "writing-mode": "vertical-rl", "vertical-align": "middle" })
                .attr('rowspan', Math.max(totalRows, 1))
                .text(mt.name)
            );

            $firstRow.append(createCourseHeaderCell(mt.id, firstCourse, colspanCount));
            $firstRow.append($('<td class="text-center"/>').append(
                $('<button type="button" class="btn btn-sm btn-outline-danger" title="Xóa món"/>')
                    .data('menu_time_id', mt.id)
                    .data('course_id', firstCourse.id)
                    .html('×')
                    .click(function(){ deleteCourse($(this).data('menu_time_id'), $(this).data('course_id')); })
            ));

            $("#tb_menu tbody").append($firstRow);

            // Thêm thực phẩm của course đầu tiên
            const foodsCourse0 = (mt.listFood || []).filter(f => f.course_id == firstCourse.id);
            foodsCourse0.forEach(food => {
                const $row = addFoodTemplate(food, mt.id);
                $("#tb_menu tbody").append($row);
            });
            listFoodTotal.push(...foodsCourse0);

            // Thêm các course khác nếu có
            if (mt.courses && mt.courses.length > 1) {
                for (let i = 1; i < mt.courses.length; i++) {
                    const course = mt.courses[i];
                    const $courseRow = $('<tr/>')
                        .attr('id', `course_${mt.id}_${course.id}`)
                        .addClass('text-center')
                        .append(createCourseHeaderCell(mt.id, course, colspanCount))
                        .append($('<td class="text-center"/>').append(
                            $('<button type="button" class="btn btn-sm btn-outline-danger" title="Xóa món"/>')
                                .data('menu_time_id', mt.id)
                                .data('course_id', course.id)
                                .html('×')
                                .click(function(){ deleteCourse($(this).data('menu_time_id'), $(this).data('course_id')); })
                        ));
                    $("#tb_menu tbody").append($courseRow);

                    const foodsByCourse = (mt.listFood || []).filter(f => f.course_id == course.id);
                    foodsByCourse.forEach(food => {
                        const $row = addFoodTemplate(food, mt.id);
                        $("#tb_menu tbody").append($row);
                    });
                    listFoodTotal.push(...foodsByCourse);
                }
            }
        }

        setTotalMenu(listFoodTotal);

    } catch (error) {
        console.error('Error in addTemplateListMenuTime:', error);
    }
}

// Tính tổng số hàng (rowspan) cho 1 menuTime theo nhiều course
function computeMenuTimeRowspan(menuTimeObj) {
    try {
        const courses = Array.isArray(menuTimeObj.courses) ? menuTimeObj.courses : [];
        const listFood = menuTimeObj.listFood || [];
        const coursesLength = courses.length === 0 ? 1 : courses.length;
        const foodsLength = listFood.length;
        const total = coursesLength + foodsLength;
        return Math.max(total, 1);
    } catch (e) {
        return 1;
    }
}

// Tạo ô header course (input tên món) với colspan
function createCourseHeaderCell(menuTimeId, course, colspanCount) {
    const $cell = $("<td/>").attr('colspan', colspanCount);
    const $wrapper = $('<div class="d-flex align-items-center gap-2 justify-content-center"></div>');
    const $input = $("<input/>")
        .attr({ type: 'text', value: course.name || '', placeholder: 'Nhập tên món ăn' })
        .addClass('form-control form-control-title p-1')
        .css({ 'text-align': 'center' })
        .data('menu_time_id', menuTimeId)
        .data('course_id', course.id)
        .change(function () {
            const mtId = $(this).data('menu_time_id');
            const cId = $(this).data('course_id');
            changeCourseName(mtId, cId, $(this).val());
        });
    $wrapper.append($input);
    $cell.append($wrapper);
    return $cell;
}

// Cập nhật tên course theo id
function changeCourseName(menuTimeId, courseId, newName) {
    try {
        const menu_id = parseInt($('#menu_id').val());
        for (let menu of window.menuExamine) {
            if (menu_id == menu.id) {
                for (let mt of menu.detail) {
                    if (mt.id == menuTimeId && Array.isArray(mt.courses)) {
                        for (let c of mt.courses) {
                            if (c.id == courseId) {
                                c.name = newName || '';
                                return;
                            }
                        }
                    }
                }
            }
        }
    } catch (e) {
        console.error('changeCourseName error:', e);
    }
}

// Xóa một món (course) và toàn bộ thực phẩm thuộc món đó
function deleteCourse(menuTimeId, courseId) {
    try {
        const currentMenu = getCurrentMenu();
        if (!currentMenu) return;

        const mt = currentMenu.detail.find(item => item.id == menuTimeId);
        if (!mt) return;

        // Xóa toàn bộ foods thuộc course
        mt.listFood = (mt.listFood || []).filter(f => f.course_id != courseId);

        // Xóa course
        mt.courses = (mt.courses || []).filter(c => c.id != courseId);

        // Nếu không còn course nào, tạo course mặc định rỗng
        if (!mt.courses || mt.courses.length === 0) {
            mt.courses = [{ id: 1, name: '' }];
        }

        // Render lại toàn bộ để cập nhật rowspan/tổng
        updateMenuDisplay(currentMenu);
        toarstMessage('Đã xóa món và các thực phẩm liên quan.');

    } catch (e) {
        console.error('deleteCourse error:', e);
        toarstError('Không thể xóa món.');
    }
}

// Hàm tạo template cho thực phẩm
function addFoodTemplate(food, menuTime_id) {
    try {
        let $row = $('<tr/>').attr("id", "food_" + menuTime_id + "_" + food.id).attr('data-course-id', food.course_id != null ? food.course_id : '');

        // Cột tên món ăn (cố định)
        $row.append($("<td/>").text(food.name || ''));

        // Các cột thông tin thực phẩm theo cấu hình
        currentDisplayConfig.visible_columns.forEach(columnKey => {
            let $cell = $("<td/>").attr("id", "food_" + menuTime_id + "_" + food.id + "_" + columnKey);

            // Xử lý đặc biệt cho cột weight (có input)
            if (columnKey === 'weight') {
                $cell.append($("<input/>")
                    .attr({ "type": "number", "step": "0.01", "min": "0", "value": food[columnKey] || 0 })
                    .addClass("form-control form-control-title p-1")
                    .data("food_id", food.id)
                    .data("menu_time_id", menuTime_id)
                    .change(function () {
                        const idFood = $(this).data('food_id');
                        const idMenuTime = $(this).data('menu_time_id');
                        const rawVal = $(this).val();
                        const parsed = parseFloat(rawVal);
                        const weight = isNaN(parsed) ? 0 : parsed;

                        changeWeightFood(idFood, idMenuTime, weight);
                    })
                );
            } else {
                // Hiển thị giá trị với định dạng phù hợp
                let value = food[columnKey];
                if (value !== null && value !== undefined && value !== '') {
                    // Làm tròn số thập phân nếu là số
                    if (!isNaN(value) && value !== '') {
                        value = parseFloat(value).toFixed(2);
                        // Loại bỏ số 0 thừa ở cuối
                        value = parseFloat(value).toString();
                    }
                    $cell.text(value);
                } else {
                    $cell.text('0');
                }
            }

            $row.append($cell);
        });

        // Cột thao tác (cố định)
        $row.append($("<td/>")
            .append($('<button type="button" class="btn btn-sm btn-outline-danger" title="Xóa thực phẩm"/>')
                .html('×')
                .css({ "cursor": "pointer" })
                .data("food_id", food.id)
                .data("menu_time_id", menuTime_id)
                .click(function () {
                    let idFood = $(this).data('food_id');
                    let idMenuTime = $(this).data('menu_time_id');
                    deleteFood(idFood, idMenuTime);
                })
            )
        );

        return $row;

    } catch (error) {
        console.error('Error in addFoodTemplate:', error);
        return $('<tr/>'); // Trả về row trống nếu có lỗi
    }
}

// Hàm xóa thực phẩm
function deleteFood(id_food, menuTime_id) {
    try {
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of window.menuExamine) {
            if (menu_id == menu.id) {
                let listFoodTotal = [];
                for (let item of menu.detail) {
                    if (menuTime_id == item.id) {
                        // Xóa thực phẩm khỏi danh sách
                        item.listFood = item.listFood.filter(food => food.id !== id_food);

                        // Xóa row khỏi table
                        $('#food_' + menuTime_id + '_' + id_food).remove();

                        // Cập nhật rowspan theo đa course
                        const newRowspan = computeMenuTimeRowspan(item);
                        $('#menu_time_' + menuTime_id + ' td:first-child').attr('rowspan', Math.max(newRowspan, 1));
                    }
                    listFoodTotal.push(...item.listFood);
                }
                setTotalMenu(listFoodTotal);
                break;
            }
        }
    } catch (error) {
        console.error('Error deleting food:', error);
    }
}

// Hàm thay đổi khối lượng thực phẩm
function changeWeightFood(id_food, menuTime_id, value) {
    try {
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of window.menuExamine) {
            if (menu_id == menu.id) {
                let listFoodTotal = [];
                for (let item of menu.detail) {
                    if (menuTime_id == item.id) {
                        for (let food of item.listFood) {
                            if (id_food == food.id) {
                                // Cập nhật object food với các giá trị dinh dưỡng mới
                                const updatedFood = caculateFoodInfo(food, value);
                                Object.assign(food, updatedFood);

                                // Cập nhật tất cả các cột được hiển thị
                                currentDisplayConfig.visible_columns.forEach(columnKey => {
                                    const $cell = $("#food_" + menuTime_id + "_" + food.id + "_" + columnKey);
                                    if ($cell.length > 0) {
                                        if (columnKey === 'weight') {
                                            // Cập nhật input field weight
                                            $cell.find('input').val(food[columnKey]);
                                        } else {
                                            // Cập nhật text content
                                            let displayValue = food[columnKey];
                                            if (displayValue !== null && displayValue !== undefined && displayValue !== '') {
                                                if (!isNaN(displayValue) && displayValue !== '') {
                                                    displayValue = parseFloat(displayValue).toFixed(2);
                                                    displayValue = parseFloat(displayValue).toString();
                                                }
                                                $cell.text(displayValue);
                                            } else {
                                                $cell.text('0');
                                            }
                                        }
                                    }
                                });
                                break;
                            }
                        }
                    }
                    listFoodTotal.push(...item.listFood);
                }
                setTotalMenu(listFoodTotal);
                break;
            }
        }
    } catch (error) {
        console.error('Error changing weight food:', error);
    }
}

// Hàm tính toán thông tin dinh dưỡng theo khối lượng
function caculateFoodInfo(originalFood, newWeight) {
    try {
        const ratio = newWeight / 100; // Dữ liệu gốc tính theo 100g
        const updatedFood = { ...originalFood };

        updatedFood.weight = newWeight;

        // Tính lại các giá trị dinh dưỡng
        Object.keys(availableColumns).forEach(key => {
            if (key !== 'weight' && originalFood[key] !== undefined) {
                const originalValue = parseFloat(originalFood[key]) || 0;
                updatedFood[key] = originalValue * ratio;
            }
        });

        return updatedFood;
    } catch (error) {
        console.error('Error calculating food info:', error);
        return originalFood;
    }
}

// Hàm tính tổng dinh dưỡng
function setTotalMenu(listFood) {
    try {
        let totalEnergy = 0;
        let totalProtein = 0;
        let totalAnimalProtein = 0;
        let totalFat = 0;
        let totalUnAnimalLipid = 0;
        let totalCarbohydrate = 0;

        for (let food of listFood) {
            totalEnergy += parseFloat(food.energy) || 0;
            totalProtein += parseFloat(food.protein) || 0;
            totalAnimalProtein += parseFloat(food.animal_protein) || 0;
            totalFat += parseFloat(food.fat) || 0;
            totalUnAnimalLipid += parseFloat(food.unanimal_lipid) || 0;
            totalCarbohydrate += parseFloat(food.carbohydrate) || 0;
        }

        // Cập nhật các trường ẩn
        $('#energy_food').val(totalEnergy.toFixed(2));
        $('#protein_food').val(totalProtein.toFixed(2));
        $('#animal_protein').val(totalAnimalProtein.toFixed(2));
        $('#lipid_food').val(totalFat.toFixed(2));
        $('#unanimal_lipid').val(totalUnAnimalLipid.toFixed(2));
        $('#carbohydrate').val(totalCarbohydrate.toFixed(2));

        // Hiển thị tổng trong footer nếu có
        $('#total_energy').text(totalEnergy.toFixed(2));
        $('#total_protein').text(totalProtein.toFixed(2));
        $('#total_animal_protein').text(totalAnimalProtein.toFixed(2));
        $('#total_lipid').text(totalFat.toFixed(2));
        $('#total_unanimal_lipid').text(totalUnAnimalLipid.toFixed(2));
        $('#total_carbohydrate').text(totalCarbohydrate.toFixed(2));

        // Tính phần trăm
        if (totalEnergy > 0) {
            const proteinPercent = (totalProtein * 4 / totalEnergy * 100).toFixed(1);
            const fatPercent = (totalFat * 9 / totalEnergy * 100).toFixed(1);
            const carbPercent = (totalCarbohydrate * 4 / totalEnergy * 100).toFixed(1);

            $('#total_protein_percent').text(proteinPercent + '%');
            $('#total_lipid_percent').text(fatPercent + '%');
            $('#total_carbohydrate_percent').text(carbPercent + '%');
        }

    } catch (error) {
        console.error('Error calculating total menu:', error);
    }
}

// Hàm cập nhật ghi chú menu
function updateMenuNote() {
    try {
        const currentMenu = getCurrentMenu();
        if (currentMenu) {
            currentMenu.note = $('#menu_example_note').val() || '';
        }
    } catch (error) {
        console.error('Error updating menu note:', error);
    }
}

// Hàm hiển thị modal xác nhận lưu menu
function showConfirmSaveMenu() {
    try {
        const currentMenu = getCurrentMenu();
        if (!currentMenu || !currentMenu.detail || currentMenu.detail.length === 0) {
            toarstError('Không có thực đơn để lưu!');
            return;
        }

        // Kiểm tra xem có thực phẩm nào không
        let hasFood = false;
        for (let mt of currentMenu.detail) {
            if (mt.listFood && mt.listFood.length > 0) {
                hasFood = true;
                break;
            }
        }

        if (!hasFood) {
            toarstError('Thực đơn chưa có thực phẩm nào!');
            return;
        }

        $('#modal-cf-save-menu').modal('show');
    } catch (error) {
        console.error('Error showing save menu modal:', error);
        toarstError('Có lỗi xảy ra!');
    }
}

// Hàm lưu menu
function saveMenu() {
    try {
        const currentMenu = getCurrentMenu();
        if (!currentMenu) {
            toarstError('Không có thực đơn để lưu!');
            return;
        }

        // Cập nhật tên menu từ input
        const menuName = $('#name_menu').val() || 'Thực đơn mới';
        currentMenu.name = menuName;

        // Cập nhật ghi chú
        currentMenu.note = $('#menu_example_note').val() || '';

        // Gửi request lưu menu
        const examineId = getExamineIdFromUrl();
        if (!examineId) {
            toarstError('Không tìm thấy ID phiếu khám!');
            return;
        }

        const menuData = {
            examine_id: examineId,
            menu_name: currentMenu.name,
            menu_detail: JSON.stringify(currentMenu.detail),
            menu_note: currentMenu.note
        };

        $.ajax({
            url: '/examine/save-menu-example',
            method: 'POST',
            data: menuData,
            success: function(response) {
                if (response.success) {
                    toarstMessage('Lưu thực đơn mẫu thành công!');
                    $('#modal-cf-save-menu').modal('hide');
                } else {
                    toarstError(response.message || 'Có lỗi xảy ra khi lưu thực đơn!');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error saving menu:', error);
                toarstError('Có lỗi xảy ra khi lưu thực đơn!');
            }
        });

    } catch (error) {
        console.error('Error in saveMenu:', error);
        toarstError('Có lỗi xảy ra!');
    }
}

// Hàm lưu menu từ menu hiện tại
function saveMenuExampleFromMenu() {
    showConfirmSaveMenu();
}

// Hàm xuất Excel
function exportMenuExcel() {
    try {
        const currentMenu = getCurrentMenu();
        if (!currentMenu) {
            toarstError('Không có thực đơn để xuất!');
            return;
        }

        const examineId = getExamineIdFromUrl();
        if (!examineId) {
            toarstError('Không tìm thấy ID phiếu khám!');
            return;
        }

        // Tạo form để submit
        const form = $('<form>', {
            method: 'POST',
            action: '/examine/export-menu-excel'
        });

        form.append($('<input>', {
            type: 'hidden',
            name: 'examine_id',
            value: examineId
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'menu_data',
            value: JSON.stringify(currentMenu)
        }));

        $('body').append(form);
        form.submit();
        form.remove();

    } catch (error) {
        console.error('Error exporting menu excel:', error);
        toarstError('Có lỗi xảy ra khi xuất Excel!');
    }
}

// Hàm thêm menu mới (tạo menu trống)
function addMenu() {
    try {
        const newMenu = createNewMenu();
        if (newMenu) {
            updateMenuDisplay(newMenu);
            toarstMessage('Đã tạo thực đơn mới!');
        }
    } catch (error) {
        console.error('Error adding new menu:', error);
        toarstError('Có lỗi xảy ra khi tạo menu mới!');
    }
}

// Hàm thêm option mới vào VirtualSelect
function addNewOptionToVirtualSelect(elementId, newOption, selectIt = false) {
    try {
        const element = document.querySelector('#' + elementId);
        if (element && element.virtualSelect) {
            const currentOptions = element.virtualSelect.getOptions();
            currentOptions.push(newOption);
            element.virtualSelect.setOptions(currentOptions);

            if (selectIt) {
                element.virtualSelect.setValue(newOption.value);
            }
        }
    } catch (error) {
        console.error('Error adding option to VirtualSelect:', error);
    }
}

// Hàm khởi tạo khi trang load
$(document).ready(function() {
    try {
        // Khởi tạo các biến global nếu chưa có
        if (typeof window.menuExamine === 'undefined') {
            window.menuExamine = [];
        }

        if (typeof window.listMenuTime === 'undefined') {
            window.listMenuTime = menuTime || [];
        }

        // Khởi tạo dropdown thực phẩm
        generateFoodName('food_name');

        // Khởi tạo dropdown món ăn
        generateDishName('dish_name');

        // Khởi tạo dropdown menu nếu có dữ liệu
        if (window.menuExamine && window.menuExamine.length > 0) {
            updateMenuDropdown();
        }

        // Event listener cho thay đổi menu
        $(document).on('change', '#menu_id', function() {
            const menuId = $(this).val();
            if (menuId) {
                const menu = window.menuExamine.find(m => m.id == menuId);
                if (menu) {
                    updateMenuDisplay(menu);
                }
            }
        });

        // Event listener cho thay đổi tên menu
        $(document).on('change', '#name_menu', function() {
            const currentMenu = getCurrentMenu();
            if (currentMenu) {
                currentMenu.name = $(this).val() || 'Thực đơn mới';
                $('#name_menu_text').text(currentMenu.name);
                updateMenuDropdown(); // Cập nhật dropdown để hiển thị tên mới
            }
        });

        console.log('MenuExample.js initialized successfully');

    } catch (error) {
        console.error('Error initializing menuExample:', error);
    }
});

// Hàm tiện ích hiển thị thông báo (nếu chưa có)
if (typeof toarstMessage === 'undefined') {
    window.toarstMessage = function(message) {
        if (typeof toastr !== 'undefined') {
            toastr.success(message);
        } else {
            alert(message);
        }
    };
}

if (typeof toarstError === 'undefined') {
    window.toarstError = function(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert('Lỗi: ' + message);
        }
    };
}

// Export các hàm cần thiết để có thể gọi từ HTML
window.chooseMenuExample = chooseMenuExample;
window.addFoodToMenu = addFoodToMenu;
window.addDishToMenu = addDishToMenu;
window.addMenu = addMenu;
window.showConfirmSaveMenu = showConfirmSaveMenu;
window.saveMenu = saveMenu;
window.saveMenuExampleFromMenu = saveMenuExampleFromMenu;
window.exportMenuExcel = exportMenuExcel;
window.updateMenuNote = updateMenuNote;
window.updateFoodDropdown = updateFoodDropdown;
