#loading-page::before {
    content: '';
    display: block;
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 32%);
    top: 0;
    left: 0;
    z-index: 10;
}
#loading-page{
    position: fixed;
    z-index: 9999;
    top: 40%;
    left: calc(50% - 40px);
    display: none;
}
#loading-page img{
    width: 75px;
    height: 75px;
    position: absolute;
    text-align: center;
    left: 110px;
}

.badge-giu-cho {
  --bs-badge-color: #ccc;
  color: #000
}
.badge-giu-cho,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="giu-cho"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="giu-cho"] {
  background-color: #ccc;
}
.box-chart-body{
  padding: 15px;
  margin: 0px;
}
.chart--container {
    width: 100%;
    min-height: 200px;
    display: flex;
    height: 340px;
}

.login .form-control-lock,
.login .form-control-user {
    width: 20px;
    height: 20px;
    margin: 5px;
    float: left;
}
.form-control-google-recaptcha {
    margin: 0 auto;
    width: 300px;
    margin-bottom: 15px;
}
#signup-form .form-control-addon{
    top:24px;
}
#signup-form .ajax_loading{
    position: absolute;
    right: 0px;
    top: 15px;
    display: none;
}
#signup-form .error-border{
    border-bottom: 1px solid red;
}
#signup-form .ver-middle,
#login-form .ver-middle{
    height: 12px;
    line-height: 12px;
    width: 100%;
    float: left;
    margin: 5px 0px;
}
#login-form .mb-4{
    float: left;
    width: 100%;
}
#signup-form input:-internal-autofill-selected{
    background: #fff;
}
#signup-form .form-control {
    text-indent: 10px;
}
.alert-dismissable ul{
    padding: 0px;
    margin: 0px;
}
.alert-dismissable ul li{
    list-style: none;
    padding: 10px 0px;
}
.booking-detail .box-btn{
    height: 20px;
}
.header-filter .flatpickr{
    width: auto;
}
#search_filter{
    margin: 0px;
    padding: 0px;
}
@media (min-width: 992px) {
  .website-status .vscomp-option {
    width: 100%;
  }
  .select-status .vscomp-option {
    width: 50%;
  }
  .vscomp-ele-wrapper.website-status, .select-status .vscomp-option {
    width: 9rem;
  }
}
@media (max-width: 767.98px){
    .dashboard_search .col-md-auto{
        margin: 10px 0px;
    }
}
.upload a{
    color: #4a69d2;
}
#tab-history-yc-sua-huy a{
    cursor: pointer;
}
.loading{
    opacity: 0.5;
    align-items: flex-start;
    position: absolute;
}
.select2-search--dropdown.select2-search--hide{
    display: block !important;
}
.body-login .select2-search--dropdown.select2-search--hide{
    display: none !important;
}
#btn_status_filter{
    width: 190px;
}

#myChartByDate-license-text{
    display: none;
}
.form-control:disabled{
    background-color: #edf5ff;
}
.cursor-pointer{
    cursor: pointer;
}

.header-filter .search-body{
    width: 480px;
    margin: 0 auto;
}
.statistic_body{
    float: left;
    width: 100%;
}
.statistic_body .stat,
.statistic_body .box-body .box-body{
    box-shadow: 0 1px 3px -1px rgb(0 0 0 / 35%), 0 1px 6px rgb(0 0 0 / 8%);
}
.dashboard_search {
    max-width: 750px;
    margin: 0 auto;
    margin-bottom: 30px;
}
.dashboard_search .statistic_search {
    display: flex;
    gap: 0.625rem;
}
.dashboard_search .flatpickr-range>.flatpickr-input {
    min-width: 15rem;
}
.dashboard_search .box-btn {
    min-width: auto;
}
.form-label-large{
    width: 7rem;
}
.btn-side-bar{
    margin-left: calc(var(--sidebar-padding-x) * -1);
    margin-right: calc(var(--sidebar-padding-x) * -1);
    position: relative;
    border-radius: 0;
}

.lds-ellipsis {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }
  .lds-ellipsis div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #262626;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
  }
  .lds-ellipsis div:nth-child(1) {
    left: 8px;
    animation: lds-ellipsis1 0.6s infinite;
  }
  .lds-ellipsis div:nth-child(2) {
    left: 8px;
    animation: lds-ellipsis2 0.6s infinite;
  }
  .lds-ellipsis div:nth-child(3) {
    left: 32px;
    animation: lds-ellipsis2 0.6s infinite;
  }
  .lds-ellipsis div:nth-child(4) {
    left: 56px;
    animation: lds-ellipsis3 0.6s infinite;
  }
  @keyframes lds-ellipsis1 {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes lds-ellipsis3 {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(0);
    }
  }
  @keyframes lds-ellipsis2 {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(24px, 0);
    }
  }

.cur-con-weather-card__title{
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  text-transform: uppercase;
}
.forecast-container{
    align-items: center;
    display: flex;
    flex-grow: 1;
    justify-content: center;
    margin-right: 24px;
}
.weather-icon{
  height: 88px;
  margin-right: 12px;
  width: 88px;
  background-position: bottom;
  background-repeat: no-repeat;
  scale: 1.3;
}
.cur-con-weather-card .forecast-container .temp {
  font-size: 84px;
  font-weight: 500;
  letter-spacing: -.7px;
  line-height: 1.07;
}
.cur-con-weather-card .forecast-container .after-temp {
  font-size: 26px;
  line-height: 1.28;
  position: relative;
  right: 25px;
  opacity: .6;
}
.cur-con-weather-card .forecast-container .real-feel {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.43;
}
.spaced-content {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
}
.cur-con-weather-card .detail {
  align-items: center;
  border-bottom: 1px solid rgba(0,0,0,.15);
  display: flex;
  flex-wrap: wrap;
  padding: 8px 0;
}
.cur-con-weather-card .detail:last-child {
    border-bottom: none;
}
.cur-con-weather-card .detail .label {
  font-size: 16px;
  line-height: 1.25;
  margin-right: 8px;
}
.cur-con-weather-card .detail .value {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.33;
}
.cur-con-weather-card .phrase {
  font-size: 18px;
  font-weight: 500;
}
@media (max-width: 425.6px) {
  #modal-chi-tiet-phieu-kham .modal-content{
    padding: 0;
  }
  #modal-chi-tiet-phieu-kham .box-body{
    padding: 0;
  }
}

.auto-save-loading {
  background: url(data:image/gif;base64,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) no-repeat center left;
  display: inline-block;
  padding-left: 25px;
  line-height: 50px !important;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar-nav .nav-link.active {
  border: none;
}

.btn-add{
  background-color: #DEEFE7;
}

.btn-add:hover{
  background-color: #B4BEC9
}

.col-form-label-modal{
  width: 7rem;
}

.w-10{
  width: 10rem;
}

.w-08{
  width: 8rem;
}

.mw-14{
  min-width: 14rem;
}

.mw-07{
  min-width: 7rem;
}

.h-02{
  height: 2.5rem !important;
}

.bg-f1{
  background-color: #f1f1f1 !important;
}

#tb_can_lam_sang .table-responsive-inner, #tb_theo_doi_thuoc .table-responsive-inner{
  max-width: 100%;
  overflow-x: auto;
}

@media (min-width: 1199.98px) {
    .table-responsive-xl .table {
        min-width: 100%
    }
}

@media (max-width: 768px) {
    .table-responsive-xl .table {
        min-width: 100%;
    }
    .table-responsive-flush {
      /* margin-left: 0;
      max-width: 0; */
    }
}

@media (max-width: 425px) {
  .page-main .container{
    padding: 0;
  }
}