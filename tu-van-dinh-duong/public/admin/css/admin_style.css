﻿/* COMMON */
.k-multiselect-wrap .k-select{
  top:6px;
}
.flex-center{
  justify-content: center;
  display: flex;
}
.wrapper .content-wrapper .alert.alert-dismissable ul{
  margin: 0px;
  padding: 0px;
}
.wrapper .content-wrapper .alert.alert-dismissable ul li{
  list-style: none;
  margin: 10px 0px;
}
#loading-page::before {
    content: '';
    display: block;
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 32%);
    top: 0;
    left: 0;
    z-index: 10;
}
#loading-page{
    height: 75px;
    width: 300px;
    position: fixed;
    z-index: 9999;
    top: 40%;
    left: 40%;
    font-size: 12pt;
    display: none;
    line-height: 75px;
    text-align: center;
    color: green;
    font-weight: bold;
}
#loading-page img{
    width: 75px;
    height: 75px;
    position: absolute;
    text-align: center;
    left: 110px;
}
.content, .content-wrapper > .content {
  padding: 0 10px 55px 10px;
}

.content-header {
  padding: 15px 1rem;
}

.content-wrapper {
  background-color: #ecf0f5;
}

.form-control {
  border-radius: 0;
}

ul, ol {
  margin-bottom: 0;
}

label:not(.form-check-label):not(.custom-file-label) {
  font-weight: 600;
}

a {
  color: #3c8dbc;
}

.alert a {
  color: black;
}

ul.nav-tabs li.nav-item a.nav-link {
  color: #444;
}

.margin-r-10 {
  margin-right: 10px;
}

.margin-r-5 {
  margin-right: 5px;
}

.margin-l-10 {
  margin-left: 10px;
}

.margin-l-5 {
  margin-left: 5px;
}

.margin-t-5 {
  margin-top: 5px;
}

.margin-b-5 {
  margin-bottom: 5px;
}

.float-lg-left {
  float: left;
}

.float-lg-right {
  float: right;
}

.v-center {
  display: flex;
  align-items: center;
}

.please-wait {
  background: url('images/ajax_loader_small.gif') no-repeat;
  padding-left: 20px;
}

.alert {
  margin-bottom: 0;
  border-radius: 0 0 3px 3px;
}

.alert-success {
  border-color: #02A91E;
}

.callout.callout-success, .alert-success,
.label-success, .modal-success .modal-body {
  background-color: #17b76d !important;
}

.field-validation-error, .validation-summary-errors {
  color: red;
}

.field-validation-valid.warning,
.field-validation-custom.warning {
  color: #fff;
  background-color: #f39c12;
  margin: 5px 0;
  display: block;
  padding: 5px;
  border-radius: 3px;
  border-left: 5px solid #c87f0a;
}

ul.common-list {
  list-style: none;
  padding-left: 0;
  margin-bottom: 15px;
}

i.true-icon {
  color: #007FCC;
  font-size: 20px;
}

i.false-icon {
  color: #D22D2D;
  font-size: 20px;
}

.small-box.bg-yellow, .bg-yellow, .bg-yellow > a {
  color: white !important;
}

.new-item-notification {
  width: 25px;
  height: 25px;
  background-image: -webkit-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
  background-image: -o-linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
  background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
  -webkit-background-size: 40px 40px;
  background-size: 40px 40px;
  background-color: #dd4b39;
  display: inline-block;
  color: #fff;
  border-radius: 15px;
  line-height: 24px;
  text-align: center;
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
  margin-left: 10px;
}

.new-item-notification span {
  font-size: 15px;
}

.info-box {
  min-height: 106px;
}

.info-box-icon {
  min-height: 106px;
}

.btn-back-top {
  display: none;
  right: 40px;
  bottom: 40px;
  position: fixed;
  width: 61px;
  height: 48px;
  z-index: 1000;
}

.btn-back-top::before {
  content: "";
  display: block;
  position: relative;
  left: 7px;
  top: 12px;
  width: 22px;
  height: 22px;
  border-right: 6px solid white;
  border-top: 6px solid white;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -ms-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #3c8dbc;
  border-color: #367fa9;
}

.btn-primary:hover {
  background-color: #467e9f;
}

.bg-purple {
  background-color: #605ca8 !important;
}

.btn-danger {
  background-color: #dd4b39 !important;
}

.btn-info {
  background-color: #00c0ef;
  border-color: #00acd6;
}

.dropdown-menu button {
  border: none;
  padding: 3px 20px;
  text-align: left;
}

.dropdown-menu li:hover {
  background: rgba(0,0,0,0.1);
}

.dropdown-menu li:hover button {
  background-color: transparent !important;
}

.scroll-wrapper {
  overflow-x: scroll;
}

/*COLLAPSIBLE BLOCKS*/
#ajaxBusy span.no-ajax-loader {
  background: none;
}

.card.card-secondary {
  margin-bottom: 10px;
  border-top: 3px solid #00c0ef;
  border-left: none;
  border-right: none;
  border-bottom: none;
  box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.card.card-secondary.collapsed-card {
  border-top: 3px solid #d2d6de;
}

.card.card-secondary textarea {
  resize: vertical;
}


.card.card-secondary > .card-header {
  padding-right: 30px;
  position: relative;
  cursor: pointer;
  background-color: #ffffff;
  color: #444;
  border-bottom: 1px solid #f4f4f4;
}

.card.card-secondary > .card-header div {
  font-size: 20px;
  font-weight: 400;
}

.card.card-secondary > .card-header .card-title i {
  padding-right: 10px;
}

.card.card-secondary .card-header i.toggle-icon {
  position: absolute;
  right: 15px;
  top: 15px;
  font-size: 12px;
}

.card.card-secondary .card-container .cards-group {
  margin-bottom: 0;
}

.card.card-secondary .card-container {
  overflow: hidden;
}

.card-info:not(.card-outline) > .card-header {
  background-color: #d9edf7;
}

.card-info:not(.card-outline) > .card-header > a {
  color: #3c8dbc;
}
/*Search block collapse*/
.search-row {
  padding-left: 50px;
  padding-right: 30px;
  position: relative;
  cursor: pointer;
}

.card-search .search-body.closed {
  display: none;
}

.card-search .search-body {
  display: block;
  margin-top: 15px;
}

.icon-search {
  position: absolute;
  left: 0;
  top: 3px;
  text-align: center;
  width: 50px;
}

.icon-search i {
  font-size: 18px;
  padding-right: 0 !important;
}

.icon-collapse {
  position: absolute;
  right: 0;
  top: 3px;
  text-align: center;
  width: 42px;
}

.icon-collapse i {
  font-size: 20px;
  padding-right: 0 !important;
  font-weight: bold;
}

.search-text {
  font-size: 20px;
}

/* CONFIGURATION STEPS */
.configuration-steps h4 {
  text-align: center;
  font-size: 34px;
}

.configuration-steps p.intro {
  font-size: 21px;
  line-height: 30px;
  font-weight: 300;
  margin: 10px 0 30px 0;
  padding: 0 65px;
}

.configuration-step-link {
  padding: 10px 0;
  border-radius: 3px;
  display: block;
  position: relative;
  height: 100%;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-property: box-shadow, transform;
  transition-property: box-shadow, transform;
}

.configuration-step-link:hover {
  box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5);
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.configuration-step-link:before {
  content: "";
  position: absolute;
  z-index: -1;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-color: #f2f2f2;
  border-style: solid;
  border-width: 0;
  border-radius: 5px;
  -webkit-transition-property: border-width;
  transition-property: border-width;
  -webkit-transition-duration: 0.1s;
  transition-duration: 0.1s;
  -webkit-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}

.configuration-step-link:hover:before {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  border-width: 1px;
}

.configuration-step-link h5 {
  line-height: 22px;
  padding: 3px 5px 0 5px;
  margin-bottom: 0;
  font-size: 22px;
  color: #000000;
}

.configuration-step-link small {
  margin-top: 8px;
  display: block;
  font-size: 1.1rem;
  line-height: 1.4rem;
  margin-bottom: 4px;
}

.configuration-step-icon {
  border-radius: 50px;
  font-size: 38px;
  color: #ffffff !important;
  width: 80px;
  height: 80px;
  text-align: center;
  padding: 10px 0;
  float: right;
  display: table;
}

.configuration-step-icon i {
  display: table-cell;
  margin: 0 auto;
  vertical-align: middle;
  padding-right: 0 !important;
}

.theme-step .configuration-step-icon {
  background-color: #e7e247;
}

.store-info-step .configuration-step-icon {
  background-color: #824c71;
}

.shipping-step .configuration-step-icon {
  background-color: #ba5c12;
}

.payments-step .configuration-step-icon {
  background-color: #ff7f11;
}

.taxes-step .configuration-step-icon {
  background-color: #b76d68;
}

.products-step .configuration-step-icon {
  background-color: #1b9aaa;
}

.email-step .configuration-step-icon {
  background-color: #140d4f;
}

.service-step .configuration-step-icon {
  background-color: #64b650;
}

.copyright-step .configuration-step-icon {
  background-color: #3d3b30;
}

/* CONFIGURATION TOUR */
.admin-area-tour.shepherd-element {
  box-shadow: 0px 0px 7px 1px #727272;
  max-width: 450px;
}

.admin-area-tour .shepherd-content .shepherd-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  border-top-left-radius: calc(.3rem - 1px);
  border-top-right-radius: calc(.3rem - 1px);
  background: #ffffff;
}

.admin-area-tour.shepherd-has-title .shepherd-content .shepherd-header {
  background: #ffffff;
  padding: 1em;
}

.admin-area-tour.shepherd-has-title .shepherd-content .shepherd-cancel-icon {
  color: #000;
  opacity: .5;
}

.admin-area-tour.shepherd-has-title .shepherd-content .shepherd-cancel-icon:hover {
  opacity: .75;
}

.admin-area-tour.shepherd-element.shepherd-has-title[data-popper-placement^=bottom] > .shepherd-arrow:before {
  background: #fff;
  border: 1px solid #e9ecef;
  box-shadow: 0px 0px 7px 1px #727272;
}

.admin-area-tour.shepherd-element.shepherd-has-title.step-with-image {
  max-width: 500px;
}

.admin-area-tour.shepherd-element.shepherd-has-title.step-with-image img {
  max-width: 100%;
}

.admin-area-tour .shepherd-content .shepherd-progress {
  margin-right: 15px;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.4;
}

.admin-area-tour .shepherd-content .shepherd-title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: 1.4rem;
}

.admin-area-tour .shepherd-content .shepherd-cancel-icon {
  padding: 1rem;
  margin: -1rem -1rem -1rem auto;
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .5;
}

.admin-area-tour .shepherd-content .shepherd-text {
  font-size: 14px;
  padding: 1rem;
  font-size: 18px;
  line-height: 26px;
}

.admin-area-tour .shepherd-content .shepherd-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: .75rem;
  border-top: 1px solid #e9ecef;
  border-bottom-right-radius: calc(.3rem - 1px);
  border-bottom-left-radius: calc(.3rem - 1px);
}

.admin-area-tour .shepherd-content .shepherd-footer {
  display: block;
  overflow: hidden;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button {
  color: #ffffff;
  border: 1px solid transparent;
  border-radius: .25rem;
  float: right;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.shepherd-button-secondary {
  float: left;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button .button-text {
  display: inline-block;
  margin-right: 12px;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.shepherd-button-secondary .button-text {
  margin-left: 12px;
  margin-right: 0;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button i {
  display: inline-block;
  vertical-align: middle;
  font-size: 0.8rem;
  margin-top: -2px;
  padding-right: 0 !important;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.button-back {
  background-color: #f8f9fa;
  border-color: #ddd;
  color: #444;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.button-next {
  background-color: #3c8dbc;
  border-color: #367fa9;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.button-next-page {
  background-color: #28a745;
  border-color: #28a745;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.button-next-page i {
  font-size: 1.3rem;
  margin-top: -1px;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.button-done {
  background-color: #605ca8;
  border-color: #605ca8;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.button-done:hover {
  background: #218838;
  color: #ffffff;
}

.admin-area-tour .shepherd-content .shepherd-footer .shepherd-button.button-next-page:hover {
  background: #218838;
  color: #ffffff;
}

/*DISCOUNT*/
.requirement-container {
  margin-bottom: 5px;
  margin-top: 5px;
  border: 1px dashed #8c949a;
  display: table;
}

.requirement-container.requirement-group {
  border: none;
  border-left: 2px solid #8c949a;
}

.requirement-container .requirement-heading {
  padding: 7px 10px 3px 10px;
  line-height: 24px;
}

.requirement-container.requirement-group > .requirement-heading {
  height: 30px;
  line-height: 30px;
  display: table;
  background: #eeeeee;
  padding: 0px 2px 0px 10px;
}

.requirement-container .requirement-heading .btn-link {
  padding: 0 3px;
  margin-left: 50px;
  font-size: 15px;
  color: #367fa9;
  float: right;
  text-decoration: none;
}

.requirement-container.requirement-group > .requirement-heading .btn-link {
  padding: 3px 10px 3px 0px;
}

.requirement-container .requirement-body {
  padding: 10px 20px 8px 10px;
  min-height: 35px;
}

.requirement-container.requirement-group > .requirement-body {
  padding: 10px 0 5px 20px;
}

.requirement-container .requirement-product-names.filled {
  clear: both;
  max-width: 600px;
  padding-top: 5px;
}

.requirement-data-buttons {
  margin-top: 5px;
}

.requirement-container .requirement-data-buttons {
  margin-top: 0;
  float: left;
}

.requirement-container .requirement-data-input {
  float: left;
  margin-right: 5px;
}

.requirement-container .requirement-label-col {
  width: auto;
}

.requirement-container .requirement-data-col {
  width: auto;
}

.requirement-container .requirement-data-buttons button {
  margin-top: 0;
}

.requirement-container .requirement-messages-col {
  margin-left: 0;
}

.interaction-type select {
  width: 70px;
  height: 28px;
  line-height: 25px;
  font-size: 12px;
  margin-top: 2px;
}

.card-add-requirement {
  margin-top: 20px;
}

/*MODAL WINDOWS*/
.modal-content {
  text-align: left;
}

.modal-body {
  font-size: 14px;
}

/*override font-awesome styles*/
.fa, .far, .fas {
  padding-right: 5px;
  font-size: 0.9em;
}

/*typeahead*/
.twitter-typeahead {
  width: 100%;
}

.tt-input {
  border: 1px solid #e5e5e5;
  float: left;
  outline: 0;
}

.tt-menu {
  background-color: white;
  z-index: 1;
  width: 100%;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  font-weight: 300;
}

.tt-menu .empty-message {
  padding: 5px 10px;
}

.tt-menu .tt-suggestion {
  margin: 0;
  padding: 10px;
  cursor: pointer;
  border-bottom: solid 1px #d4d8dd;
}

.tt-menu .tt-highlight {
  color: #e73128;
}

.tt-menu .tt-suggestion:last-of-type {
  border-bottom: none;
}

.tt-menu .tt-suggestion h5 {
  margin: 0;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: bold;
}

.tt-menu .tt-suggestion p {
  margin: 0;
  font-size: 12px;
}

.tt-menu .tt-suggestion.tt-cursor {
  background-color: #e2e5e8;
}

.tt-menu .tt-suggestion#user-selection:hover {
  background-color: #e2e5e8;
}

.tt-menu .tt-empty {
  text-align: center;
}

/* KENDO */
table.adminContent {
  border-collapse: collapse;
  color: #333;
  font-size: 14px;
  margin: 0;
  width: 100%;
  vertical-align: middle;
  text-align: left;
}

.k-button {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: #f4f4f4;
  color: #444;
  border-color: #ddd;
}

.k-multiselect .k-button {
  white-space: normal;
}

.k-button:hover, .k-button:focus, .k-button.k-state-focused {
  background-color: #e7e7e7;
  background-image: none;
  color: #333;
  border-color: #adadad;
  text-decoration: none;
}

.k-button:active {
  color: #333;
  background-color: #e7e7e7;
  border-color: #adadad;
}

.k-button:focus:active:not(.k-state-disabled):not([disabled]) {
  box-shadow: none;
  -webkit-box-shadow: none;
}

table .btn {
  margin: 2px;
}

.k-picker-wrap.k-state-default,
.k-numeric-wrap.k-state-default {
  border-radius: 0;
  border: none;
  height: 38px;
  padding-right: 23px;
  background: #fff;
}

.k-picker-wrap.k-state-hover {
  background: #fff;
}

.k-picker-wrap .k-select,
.k-numeric-wrap .k-select {
  border-radius: 0;
  border: none;
}

.k-numeric-wrap.k-state-default > .k-select {
  background-color: #fff;
  border: none;
  height: 34px;
  width: 22px;
  opacity: 1;
}

.k-picker-wrap.k-state-default > .k-select {
  background: #fff;
  border: 1px solid #d2d6de;
  height: 36px;
  width: 4.5em;
  padding-left: 1px;
  -moz-box-sizing: content-box;
}

.k-picker-wrap.k-state-hover > .k-select {
  border-color: #afafaf;
}

.k-picker-wrap .k-input,
.k-numeric-wrap .k-input {
  height: 30px;
  border-radius: 0;
  border: 1px solid #d2d6de;
  text-indent: 0.7em;
  color: #555;
  padding: 3px 0;
}

.k-picker-wrap.k-state-focused.k-state-selected,
.k-numeric-wrap.k-state-focused.k-state-selected,
td.k-state-focused.k-state-selected {
  -webkit-box-shadow: inset 0 0 3px 4px #5FA6D2;
  box-shadow: inset 0 0 3px 4px #5FA6D2;
}

.k-state-active, .k-state-active:hover,
.k-active-filter, .k-tabstrip .k-state-active {
  border-color: #c5c5c5 !important;
}

.k-state-selected,
.k-state-selected:link,
.k-state-selected:visited,
.k-list > .k-state-selected,
.k-list > .k-state-highlight,
.k-panel > .k-state-selected,
.k-ghost-splitbar-vertical,
.k-ghost-splitbar-horizontal,
.k-draghandle.k-state-selected:hover,
.k-scheduler .k-scheduler-toolbar .k-state-selected,
.k-scheduler .k-today.k-state-selected, .k-marquee-color {
  background-color: #3c8dbc;
  border-color: #3c8dbc;
}

.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgb(0 123 255/25%);
  border-radius: 0;
  border: 1px solid #80bdff;
}

.k-picker-wrap.k-state-focused .k-input,
.k-numeric-wrap.k-state-focused .k-input {
  border: none;
}

.k-numerictextbox .k-link {
  height: 18px;
  border: 1px solid #d2d6de;
  border-radius: 0;
}

.k-numerictextbox .k-link:hover {
  border-color: #afafaf;
}

.k-numerictextbox .k-link:hover + .k-link {
  border-top-color: #afafaf;
}

.k-numeric-wrap .k-link + .k-link {
  margin-top: -1px;
  height: 17px;
  border-radius: 0;
}

.k-numeric-wrap.k-state-disabled,
.k-picker-wrap.k-state-disabled {
  border-radius: 0;
  border: none;
  opacity: 1;
  height: 38px;
}

.k-numeric-wrap.k-state-disabled .k-input,
.k-picker-wrap.k-state-disabled .k-input {
  background-color: #eee;
  cursor: not-allowed;
}

.k-numerictextbox .k-link.k-state-selected {
  -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,0.125);
  -moz-box-shadow: inset 0 3px 5px rgba(0,0,0,0.125);
  box-shadow: inset 0 3px 5px rgba(0,0,0,0.125);
  background: #fff;
}

.k-numerictextbox .k-i-arrow-n,
.k-numerictextbox .k-i-arrow-s {
  position: absolute;
  left: 0px;
  font-size: 0;
  font-weight: 400;
  height: 17px;
  background: none;
  position: relative;
  top: 0px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  opacity: 1;
}

.k-numerictextbox .k-select .k-i-arrow-n:before,
.k-numerictextbox .k-select .k-i-arrow-s:before {
  color: #444;
  font-family: 'Glyphicons Halflings';
  height: 17px;
  line-height: 17px;
  font-size: 9px;
}

.k-numerictextbox .k-link .k-i-arrow-n:before {
  content: "\e113";
}

.k-numerictextbox .k-link .k-i-arrow-s:before {
  content: "\e114";
}

.k-picker-wrap .k-select .k-i-calendar:before,
.k-picker-wrap .k-select .k-i-clock:before {
  font: normal normal normal 14px/1 "Font Awesome 5 Free";
  height: 17px;
  line-height: 17px;
}

.k-picker-wrap .k-i-calendar,
.k-picker-wrap .k-i-clock {
  background: none;
  height: 36px;
}

.k-calendar-container.k-group {
  padding: 0;
  border-radius: 0;
  background: #fff;
}

.k-calendar th {
  padding: .4em .45em .4em .8em;
}

.k-picker-wrap .k-i-calendar:before {
  content: "\f073";
}

.k-picker-wrap .k-i-clock:before {
  content: "\f017";
}

.k-datetimepicker .k-picker-wrap .k-icon {
  padding: 0 3px;
}

.ui-tooltip {
  z-index: 10005;
}

.tooltip {
  opacity: 100;
}

.tooltip.show {
  opacity: 100;
}

.tooltip .arrow {
  display: none;
}

.tooltip-inner {
  background-color: #fff;
  color: #0e0e0e;
  opacity: 100;
  box-shadow: 0 0 10px rgba(0,0,0,0.5);
  border: 1px solid #aaa;
  font-family: Verdana,Arial,sans-serif;
  font-size: 1.1em;
  text-align: left;
  max-width: 400px;
}


.k-multiselect {
  width: 100% !important;
}

.k-multiselect.k-header {
  border-color: #d2d6de;
  border-radius: 0;
}

.k-multiselect.k-header.k-state-hover {
  border-color: #3c8dbc !important;
  box-shadow: none !important;
}

.k-multiselect.k-state-focused {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  border-width: 0;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgb(0 123 255/25%);
  border-radius: 0;
}

.k-state-focused > .k-multiselect-wrap {
  box-shadow: none;
  border: 1px solid #80bdff;
  border-radius: 0;
}

.k-multiselect-wrap {
  min-height: 36px;
  border-radius: 0;
}

.k-multiselect-wrap > .k-input {
  height: 28px;
}

.k-multiselect-wrap li {
  line-height: 27px;
}

.k-list-container {
  border-color: #DEDEDE;
  background-color: #FFFFFF;
}

.form-horizontal .form-group .k-dropdown {
  cursor: default;
  width: 100% !important;
  height: 38px;
}

.form-horizontal .form-group .k-dropdown .k-dropdown-wrap {
  background: #fff;
  box-shadow: none;
  padding: 0 8px;
  border: 1px solid #d2d6de;
  border-radius: 0;
}

.form-horizontal .form-group .k-dropdown .k-dropdown-wrap .k-select {
  width: 20px;
}

.form-horizontal .form-group .k-dropdown .k-dropdown-wrap.k-state-border-down {
  border-color: #3c8dbc !important;
}

.form-horizontal .form-group .k-dropdown .k-dropdown-wrap .k-input {
  height: 36px;
  line-height: 36px;
  display: block;
  padding: 0;
}

.form-horizontal .form-group .k-dropdown .k-dropdown-wrap .k-input .image {
  margin-right: 10px;
  display: inline-block;
  vertical-align: middle;
}

.k-animation-container .k-list-container {
  border: 1px solid #3c8dbc !important;
  box-shadow: none;
}

.k-animation-container .image {
  margin-right: 10px;
  display: inline-block;
  vertical-align: middle;
}

.k-popup .k-list .k-item.k-state-hover {
  background: #3c8dbc;
  border-color: #367fa9;
  color: #fff;
  box-shadow: none;
}

.k-popup .k-list .k-item.k-state-focused {
  box-shadow: none;
}

.k-window-titlebar {
  height: 2em;
}

.table th .checkbox {
  min-height: 0;
  padding-top: 0;
}

/*GRID FOOTER*/
.k-pager-numbers .k-link {
  background: #ffffff;
  border: 1px solid #ddd;
  margin-right: -1px;
  border-radius: 0;
  height: 32px;
  line-height: 31px;
  min-width: 31px;
}

.k-pager-numbers .k-state-selected {
  border-radius: 0;
  margin: 0px;
  background: #3c8dbc;
  height: 32px;
  line-height: 31px;
  min-width: 30px;
}

.k-pager-wrap > .k-link {
  border-radius: 0;
  margin: 0;
  margin-left: -1px;
  height: 32px;
  line-height: 31px;
  background: #ffffff;
  border: 1px solid #ddd;
  min-width: 30px;
}

.k-pager-wrap .k-link:hover {
  background-color: #eee;
  border-color: #ddd;
}

.k-pager-sizes {
  padding-top: 0;
}

.k-pager-sizes .k-dropdown-wrap.k-state-default {
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 0;
  height: 32px;
}

.k-pager-sizes .k-dropdown-wrap.k-state-focused {
  border: 1px solid #ddd !important;
  box-shadow: none !important;
}

.k-pager-sizes .k-dropdown-wrap .k-input {
  height: 28px;
  line-height: 28px;
}

.k-pager-sizes .k-widget.k-dropdown {
  margin-top: 0;
  width: auto !important;
  min-width: 60px;
}

.k-list .k-state-focused, .k-list .k-state-hover {
  border-radius: 0;
  background: #3c8dbc;
}

input[type="text"].k-input {
  box-sizing: content-box;
}

.page-item.active .page-link {
  background-color: #337ab7;
  border-color: #337ab7;
}

.pagination > li > a {
  background: #fafafa;
  color: #666;
  height: 34px;
  line-height: 18px;
}

/* HEADER */
.main-header {
  border-bottom: none;
}

/* SIDE BAR */

.sidebar {
  overflow:hidden;
}

.nav-sidebar {
  padding-bottom: 40px;
}

.brand-link .brand-image-xl {
  max-height: 40px;
}

.layout-fixed .brand-link {
  width: auto;
}

.nav-sidebar .nav-link p {
  white-space: normal;
  display: inline-flex;
}

.nav-sidebar, .main-sidebar {
  white-space: normal;
}

.sidebar-mini.sidebar-collapse .main-sidebar .nav-sidebar .nav-link p {
  white-space: nowrap;
}

.sidebar-mini.sidebar-collapse .main-sidebar:hover .nav-sidebar .nav-link p {
  white-space: normal;
  display: inline-flex;
  width: auto;
}

.nav-sidebar .nav-treeview > li > a {
  padding: 5px 15px;
  display: flex;
  align-items: flex-start;
}

.nav-sidebar .nav-treeview > li > a i {
  padding-top: 4px;
  min-width: 26px;
}

.sidebar-form {
  border: none !important;
  overflow: visible;
  margin: 10px 10px 0 !important;
}

.sidebar-mini .sidebar-form input[type="text"] {
  border-radius: 2px;
}

.sidebar-mini.sidebar-collapse .sidebar-form {
  display: none !important;
  -webkit-transform: translateZ(0);
}

.nav-treeview > li.current-active-item > a {
  color: #fff;
}

.admin-search-box {
  background: url(images/search-icon.png) no-repeat 97%;
  padding-right: 30px;
}

/*CSS loader */
div.dataTables_wrapper {
  position: relative;
}

div.dataTables_wrapper div.dataTables_processing {
  border: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin-left: auto;
  margin-top: auto;
  z-index: 50;
  background: rgba(255,255,255,0.7);
  -ms-border-radius: 3px;
  border-radius: 3px;
}

div.dataTables_wrapper div.dataTables_processing .fa {
  padding-right: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -15px;
  margin-top: -15px;
  color: #000;
  font-size: 30px;
}

.dataTables_wrapper.no-footer .dataTables_scrollBody {
  border-bottom: 1px solid #f4f4f4;
}

.dataTables_wrapper thead {
  background-color: #f4f4f4;
}

.dataTables_wrapper tbody td {
  vertical-align: middle !important;
}

.custom-select-sm {
  font-size: 100%;
}

/* NAV BAR */
.navbar-custom-menu > .navbar-nav > li {
  height: 50px;
}

.navbar-custom-menu li.account-info {
  line-height: 50px;
  color: #fff;
  padding: 0 15px;
}

/* NAV TABS */
.nav > li > a > img {
  margin-right: 8px;
  vertical-align: baseline;
}

.nav-tabs-custom {
  margin-bottom: 5px;
}

.nav-tabs-custom .tab-pane > .card {
  border: none;
  box-shadow: none;
}

.nav-tabs-custom .tab-pane > .card .card-body {
  padding: 5px;
}

.nav-tabs-custom + .card, .card + .nav-tabs-custom {
  margin-top: 5px;
}

.nav-tabs-custom .nav-tabs-custom {
  border-bottom: 1px solid #ddd;
  box-shadow: none;
}

.nav-tabs-custom > .nav-tabs {
  border-bottom-color: #ddd;
}

.nav-tabs-custom > .nav-tabs > li.active > a {
  border-left-color: #ddd;
  border-right-color: #ddd;
}

.nav-tabs-custom > .tab-content {
  border-right: 1px solid #ddd;
  border-left: 1px solid #ddd;
}

.nav-tabs-custom > .nav-tabs > li.active > a {
  border-right: 1px solid #ddd !important;
  border-left: 1px solid #ddd !important;
}

.nav-tabs-customer-statistics .chart, .nav-tabs-order-statistics .chart {
  height: 300px;
}

/* CONTENT HEADER */
.content-header > h1 {
  margin-bottom: 10px;
}

.content-header > h1 > small {
  color: #0076BB;
  font-weight: normal;
  margin-left: 6px;
  font-size: 65%;
}

.content-header > h1 > small i {
  font-size: 0.8em;
  padding-right: 2px;
}

.content-header .btn {
  margin-bottom: 5px;
}

.content-header .btn-group .dropdown-menu input,
.content-header .btn-group .dropdown-menu button,
.content-header .btn-group .dropdown-menu a {
  display: block;
  background: none;
  border: none;
  margin: 0;
  padding: 3px 20px;
  font-weight: 400;
  line-height: 1.42857143;
  color: #777;
  white-space: nowrap;
}

.documentation-reference {
  margin-bottom: 10px;
}

.documentation-reference i {
  font-size: 0.9em;
  color: #4d4d4d;
}

/* FORM ELEMENTS */
.form-horizontal .label-wrapper {
  float: right;
  display: flex;
  align-items: flex-start;
}

.form-horizontal .multi-store-override-option + .label-wrapper {
  width: 90%;
}

.form-horizontal .label-wrapper .col-form-label {
  text-align: right;
  flex-grow: 2;
}

.form-horizontal .label-wrapper .ico-help {
  color: #3c8dbc;
  font-size: 1.17em;
  padding-left: 6px;
  padding-top: 0.28em;
}

.form-horizontal .label-wrapper i.fas {
  width: 16px;
}

.form-horizontal .label-wrapper i.fas:before {
  top: 5px;
}

.form-horizontal span.required {
  color: #ff2e2e;
  margin-left: 6px;
  font-size: 16px;
  font-weight: bold;
}

.form-group {
  margin-bottom: 5px;
}

/*don't show the default input spinners*/
.form-group input[type=number]::-webkit-outer-spin-button,
.form-group input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.form-group input[type=number] {
  -moz-appearance: textfield;
}

.form-group input[type=checkbox] {
  margin-top: 13px;
}

.form-group input[type=checkbox]:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgb(0 123 255/25%);
  border-radius: 0;
  border: 1px solid #80bdff;
}

.form-group .checkbox input[type=checkbox] {
  margin-top: 3px;
}

.form-group .please-wait {
  margin-top: 10px;
}

.form-group > div > a {
  display: inline-block;
  padding-top: 6px;
}

.form-group .callout {
  padding: 5px 15px;
}

.form-horizontal .input-group {
  align-items: end;
}

.form-horizontal .form-check {
  padding-top: 7px;
  padding-right: 10px;
}

.form-horizontal .form-check .form-check-input {
  margin-top: .3rem;
}

.form-group .attributes label {
  font-weight: normal;
}

.form-horizontal .attributes select {
  width: 300px !important;
  max-width: 100%;
  border-radius: 0;
  border-color: #d2d6de;
  height: 34px;
}

.form-group .attributes .qty-box {
  width: 48px;
  display: inline-block;
}

.form-horizontal .attributes .input-group-required {
  width: 320px;
}

.form-horizontal .k-autocomplete, .form-horizontal .k-combobox,
.form-horizontal .k-numerictextbox, .form-horizontal .k-dropdown,
.form-horizontal .k-selectbox, .form-horizontal .k-textbox,
.form-horizontal .k-colorpicker,
.form-horizontal .k-timepicker, .form-horizontal .k-datetimepicker,
.form-horizontal .k-datepicker {
  width: 300px !important;
  max-width: 100%;
  border-radius: 0;
  border-color: #d2d6de;
  height: 38px;
}

.tag-editor {
  border: 1px solid #d2d6de !important;
  line-height: 31px !important;
}

.form-text-row {
  padding-top: 6px;
}

.btn-search {
  margin-top: 10px;
  min-width: 150px;
  padding: 7px 10px;
  font-size: 18px;
}

.card-search .form-control, .card-search .input-group-short {
  max-width: 425px;
}

.card-search .k-multiselect.k-header {
  max-width: 423px;
}

.card-popup .btn-search {
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.dropdown-toggle.bg-purple {
  border-left: 1px solid #AA89CE;
}

.input-group .input-group-btn .input-group-btn-hint {
  font-size: 14px;
  white-space: normal;
  max-width: 330px;
}

.custom-input-group.input-group-short .custom-input-group-btn,
.input-group.input-group-short .input-group-btn {
  width: auto;
}

.custom-input-group.input-group-short .bootstrap-touchspin {
  float: left;
  margin-right: 3px;
  width: 200px;
}

.input-group.input-group-short .k-widget {
  margin-right: 10px;
}

.input-group.input-group-short .input-group-text {
  margin-right: 10px;
  margin-bottom: 4px;
  padding-left: 0;
  background-color: transparent;
  border: none;
  color: initial;
}

.input-group-required {
  width: 100%;
}

.input-group-required .input-group-btn {
  font-size: inherit;
  vertical-align: top;
  width: 1.5%;
}

.input-group-required.input-group {
  width: 100% !important;
}

input[type=file].form-control {
  height: auto;
}

.dropdown-menu a:hover {
  text-decoration: none;
}

.dropdown-menu > li > a > .fa {
  margin-right: 0;
}

.editor-settings-modal-dialog {
  width: 985px;
  max-width: 95%;
}

.editor-settings-modal-dialog .modal-body {
  position: relative;
  padding: 5px 23px 15px 23px;
}

.editor-settings-modal-dialog .form-group input[type=checkbox] {
  margin-top: 12px;
}

.editor-settings-modal-dialog .form-group input[type=checkbox].select-all-fields {
  margin-top: 8px;
}

.editor-settings-modal-dialog .cards-group {
  margin-bottom: 0;
}

.editor-settings-modal-dialog .card-body {
  padding: 10px 20px;
}

.editor-settings-modal-dialog .card-header {
  padding: 12px 20px 2px 20px;
  font-weight: 700;
  font-size: 1.3em;
}

.editor-settings-modal-dialog .col-md-4 {
  padding: 7px;
}

.store-scope-configuration .label-wrapper {
  float: left;
  margin-right: 10px;
}

/* PRODUCT EDIT/CREATE */
.form-group + .nav-tabs-localized-fields {
  margin-top: 15px;
}

.attribute-picture-selection-block .checkbox {
  display: inline-block;
}

.attribute-picture-selection-block label {
  padding-left: 0;
  padding-right: 20px;
}

/* TINY MCE */
.mce-panel {
  border-color: #d2d6de !important;
}

.mce-container {
  max-width: 100%;
}

.mce-container label {
  max-width: inherit;
}

.mce-fullscreen {
  z-index: 10000 !important;
}

/* QQ */
.sidebar-mini .qq-upload-button {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: #00a65a;
  border-color: #008d4c;
  float: left;
  margin-right: 5px;
}

.qq-upload-list {
  float: left;
}

.qq-upload-list .qq-upload-success, .qq-upload-list .qq-upload-fail {
  margin-right: 5px !important;
}

.qq-upload-list li.qq-upload-success {
  background-color: #3c8dbc !important;
}

/*order list statuses*/
.order-list span.order-status {
  padding: .2em .6em .3em;
  border-radius: .25em;
  color: #2e2e2e;
}

.order-list span.order-status-pending {
  background-color: #f39c12;
  color: #fff;
}

.order-list span.order-status-processing {
  background-color: #00c0ef;
  color: #fff;
}

.order-list span.order-status-complete {
  background-color: #00a65a;
  color: #fff;
}

.order-list span.order-status-cancelled {
  background-color: #dd4b39;
  color: #fff;
}

/*grid color statuses*/
span.grid-report-item {
  padding: .2em .6em .3em;
  border-radius: .25em;
  color: #2e2e2e;
}

span.grid-report-item.yellow {
  background-color: #f39c12;
  color: #fff;
}

span.grid-report-item.blue {
  background-color: #00c0ef;
  color: #fff;
}

span.grid-report-item.green {
  background-color: #00a65a;
  color: #fff;
}

span.grid-report-item.red {
  background-color: #dd4b39;
  color: #fff;
}

/*SETTINGS*/
.theme-selection-block .checkbox {
  float: left;
}

.theme-selection-block .checkbox label {
  padding-left: 0;
  padding-right: 20px;
  padding-top: 8px;
}

.theme-selection-block .checkbox label img {
  width: 175px;
}

.theme-selection-block .checkbox label span {
  display: block;
}

.theme-selection-block .checkbox label span input {
  margin: 0 5px 8px 0;
}

/*nested settings*/
.parent-setting.opened {
  margin-bottom: 0;
  position: relative;
}

.parent-setting.opened > div:first-child {
  padding-bottom: 5px;
}

.parent-setting.opened div[class^="col-md-"] + div[class^="col-md-"] {
  padding-left: 23px;
  height: 100%;
  position: static;
  padding-bottom: 5px;
}

.parent-setting.opened div[class^="col-md-"] + div[class^="col-md-"]:before,
.parent-setting.opened + .nested-setting .form-group div[class^="col-md-"] + div[class^="col-md-"]:before,
.parent-setting.opened + .nested-setting .form-group:last-child div[class^="col-md-"] + div[class^="col-md-"]:before {
  border-left: 1px solid #c1c1c1;
}

.parent-setting.opened div + div:before {
  position: absolute;
  height: 105%;
  content: "";
  margin-left: -10px;
  top: 15px;
  z-index: 10;
}

.parent-setting.opened div + div input[type=checkbox] {
  margin-left: -15px;
  z-index: 100;
  position: relative;
}

.basic-settings-mode .parent-setting.opened + .nested-setting .form-group.parent-setting.parent-setting-advanced.opened div + div input[type=checkbox] {
  margin-left: 0;
}

.parent-setting.opened + .nested-setting .form-group {
  margin-bottom: 0;
  position: relative;
}

.parent-setting.opened + .nested-setting .card .card-body .form-group {
  padding-bottom: 5px;
}

.parent-setting.opened + .nested-setting .form-group > div[class^="col-md-"]:first-child {
  padding-bottom: 5px;
}

.parent-setting.opened + .nested-setting .form-group:last-child > div[class^="col-md-"]:first-child {
  padding-bottom: 0;
}

.parent-setting.opened + .nested-setting .form-group div[class^="col-md-"] + div[class^="col-md-"] {
  position: static;
  padding-left: 23px;
  padding-bottom: 5px;
  height: 100%;
}

.parent-setting.opened + .nested-setting .form-group:last-child div[class^="col-md-"] + div[class^="col-md-"] {
  padding-bottom: 0;
}

.parent-setting.opened + .nested-setting .form-group div[class^="col-md-"] + div[class^="col-md-"]:before {
  position: absolute;
  height: 100%;
  content: "";
  margin-left: -10px;
}

.parent-setting.opened + .nested-setting .form-group.parent-setting-reversed.parent-setting-ticked div[class^="col-md-"] + div[class^="col-md-"]:before {
  display: none;
}

.parent-setting.opened + .nested-setting .form-group.parent-setting div[class^="col-md-"] + div[class^="col-md-"]:before {
  position: absolute;
  height: 36px;
  content: "";
  margin-left: -10px;
  margin-top: -18px;
}

.parent-setting.opened + .nested-setting .form-group.parent-setting.opened > div[class^="col-md-"] + div[class^="col-md-"]:before {
  height: 110%;
}

.parent-setting.opened + .nested-setting .form-group:last-child {
  margin-bottom: 5px;
}

.parent-setting.opened + .nested-setting .form-group > div[class^="col-md-"] + div[class^="col-md-"]:after {
  border-bottom: 1px solid #c1c1c1;
  content: "";
  position: absolute;
  width: 10px;
  display: block;
  margin-left: -10px;
  top: 17px;
}

.parent-setting.opened + .nested-setting .form-group.parent-setting.opened > div[class^="col-md-"] + div[class^="col-md-"]:after {
  display: none;
}

.parent-setting.opened + .nested-setting .form-group:last-child div[class^="col-md-"] + div[class^="col-md-"] {
  border-left: none;
}

.parent-setting.opened + .nested-setting .form-group:last-child div[class^="col-md-"] + div[class^="col-md-"]:before {
  position: absolute;
  height: 18px;
  content: "";
  margin-left: -10px;
}

.basic-settings-mode .parent-setting.parent-setting-advanced.opened div[class^="col-md-"] + div[class^="col-md-"]:before {
  display: none;
}

.basic-settings-mode .parent-setting.opened + .nested-setting .form-group.parent-setting.parent-setting-advanced.opened div[class^="col-md-"] + div[class^="col-md-"]:before {
  display: block;
  margin-top: -33px;
}

.basic-settings-mode .parent-setting.opened + .nested-setting .form-group.parent-setting.parent-setting-advanced.opened > div[class^="col-md-"] + div[class^="col-md-"]:after {
  display: block;
}

/*advanced settings*/
.basic-settings-mode .advanced-setting {
  display: none !important;
}

.advanced-settings-mode .basic-setting {
  display: none !important;
}

.onoffswitch {
  display: table;
  position: relative;
  min-width: 130px;
  max-width: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.onoffswitch-checkbox {
  display: none;
}

.onoffswitch-label {
  display: table;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #367fa9;
  border-radius: 3px;
  min-width: 130px;
  margin-bottom: 0;
}

.onoffswitch-inner {
  display: block;
  width: 200%;
  margin-left: -100%;
  transition: margin 0.3s ease-in 0s;
}

.onoffswitch-inner:before, .onoffswitch-inner:after {
  display: block;
  float: left;
  width: 50%;
  padding: 0;
  color: white;
  font-weight: 400;
  white-space: nowrap;
  height: 37px;
  line-height: 37px;
}

.onoffswitch-inner:before {
  content: attr(data-locale-advanced);
  padding-left: 10px;
  background-color: #3c8dbc;
  color: #FFFFFF;
}

.onoffswitch-inner:after {
  content: attr(data-locale-basic);
  padding-right: 10px;
  background-color: #efefef;
  color: #3380ad;
  text-align: right;
}

.onoffswitch-switch {
  display: block;
  width: 16px;
  margin: 4px 0;
  background: #FFFFFF;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 100%;
  margin-right: -25px;
  border: 1px solid #367fa9;
  border-radius: 15px;
  transition: all 0.3s ease-in 0s;
}

.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {
  margin-left: 0;
}

.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {
  right: 0px;
  margin-right: 7px;
  transition: all 0.3s ease-in 0s;
}

/*bootstrap touchspin*/
.bootstrap-touchspin {
  max-width: 300px;
  width: 100%;
}

.bootstrap-touchspin input {
  height: auto;
  display: table-cell !important;
  min-height: 34px;
}

.bootstrap-touchspin-postfix {
  border-left: none;
}

.input-group-btn-vertical {
  top: 0;
}

.bootstrap-touchspin .input-group-btn-vertical i {
  left: 6px;
}

.input-group-addon.bootstrap-touchspin-postfix {
  background-color: #eee;
  height: 34px;
  padding-right: 30px;
}

.bootstrap-touchspin .input-group-btn-vertical > .btn {
  background: #fff;
  border-color: #d2d6de;
  border-radius: 0;
  height: 18px;
  font-size: 0;
  padding-right: 14px;
  padding-left: 6px;
  margin-top: 1px;
  margin-bottom: 1px;
}

.bootstrap-touchspin .input-group-btn-vertical > .btn:before {
  font-size: 14px;
}

.bootstrap-touchspin .input-group-btn-vertical > .btn:hover {
  border-color: #afafaf;
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down {
  margin-top: 14px;
  height: 17px;
}

/* THROBBER */
.throbber-header {
  font-size: 145%;
}

.throbber {
  display: none;
}

.throbber .curtain {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #3e4d45;
  opacity: 0.9;
  filter: alpha(opacity=90);
  z-index: 9999;
}

.throbber .curtain-content {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.throbber .curtain-content div {
  text-align: center;
  padding: 250px;
  color: #FFF;
}
/*AJAX loading*/
#ajaxBusy {
  display: none;
  left: 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100000;
}

#ajaxBusy span {
  background: url(images/ajax-loading.gif) no-repeat;
  width: 40px;
  height: 40px;
  float: right;
  margin: 9px 9px 0px 0px;
}

/*Upload picture block*/
.upload-picture-block {
  display: table-row;
  width: 100%;
}

.upload-picture-block .qq-upload-success {
  word-break: break-all;
}

.upload-picture-block .picture-container {
  display: table-cell;
  vertical-align: top;
}

.upload-picture-block .upload-button-container {
  display: table-cell;
  padding-left: 25px;
  vertical-align: middle;
}
/*fix for inline-block buttons*/
.content-header .float-right > .btn, .content-header .float-right > .btn-group {
  float: left;
  margin-left: 3px;
  position: inherit;
}


/* JQUERY DATA TABLES */
.button-column {
  text-align: center;
}

.table > tbody > tr > td.button-column {
  padding: 2px;
}

.table > tbody > tr > td.button-column .btn {
  margin-top: 1px;
  margin-bottom: 1px;
  padding: 5px 12px;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_length {
  margin: 5px 5px 0 5px;
}

div.dataTables_wrapper div.dataTables_info {
  padding-top: 0;
  margin-top: 10px;
}

div.dataTables_wrapper .data-tables-refresh > div {
  float: none;
  margin-top: 2px;
  width: auto;
}

div.dataTables_wrapper > div.row.margin-t-5 > div.col-lg-1.col-xs-12 > div > div > button {
  background-color: #f4f4f4;
  color: #444;
  border-color: #ddd;
}

.table-bordered {
  border: 1px solid #e3e3e3;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th {
  border: 1px solid #e3e3e3;
}

.table-bordered > thead > tr > th label,
.table-bordered > tbody > tr > th label,
.table-bordered > tfoot > tr > th label {
  font-weight: bold !important;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.table-bordered > thead > tr > th label input[type=checkbox],
.table-bordered > tbody > tr > th label input[type=checkbox],
.table-bordered > tfoot > tr > th label input[type=checkbox] {
  margin-right: 5px;
}

.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border-top: 1px solid #f3f3f3;
  border-right: 1px solid #e3e3e3;
}

.table-striped tbody tr.odd {
  background-color: white;
}

tr.even, div.dataTables_scrollHead > div > table > thead > tr {
  background-color: #f4f4f4;
}

.dataTables_scrollBody .dataTables_wrapper {
  width: 95%;
  margin-left: 3%;
}

.dataTables_scroll > .dataTables_scrollBody > .table,
.dataTables_scroll > .dataTables_scrollHead > .dataTables_scrollHeadInner > .table {
  padding-left: 0 !important;
}

div.dataTables_wrapper div.dataTables_paginate {
  text-align: center;
}

.pagination span.current,
.pagination a, .pagination span {
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
  border: 1px solid #eaeaea;
}

@media (max-width: 1500px) {
  /* CONFIGURATION STEPS */
  .configuration-step-icon {
    width: 60px;
    height: 60px;
    max-width: 100%;
    padding-top: 9px;
    font-size: 28px;
    margin-top: .1rem;
  }

  .configuration-step-link h5 {
    padding: 0;
    padding-right: 3px;
    font-size: 20px;
  }

  .configuration-step-link h5 small {
    font-size: 1.0rem;
    line-height: 1.3rem;
  }
}

@media (max-width: 1200px) {
  /* HEADER */
  .main-header .navbar-custom-menu {
    float: none;
  }

  .main-header .navbar-custom-menu > .navbar-nav {
    float: none;
  }

  /*FORM ELEMENTS*/
  .form-horizontal .label-wrapper .ico-help {
    display: none;
  }

  .form-horizontal .card-popup .ico-help {
    display: block;
  }

  .form-control {
    margin-bottom: 3px;
  }
  /* KENDO */
  .k-autocomplete, .k-combobox, .k-datepicker, .k-timepicker, .k-datetimepicker,
  .k-colorpicker, .k-numerictextbox, .k-dropdown, .k-selectbox, .k-textbox, .k-datetimepicker {
    margin-bottom: 3px;
  }

  /*nested settings*/
  .parent-setting.opened {
    margin-bottom: 5px;
    position: static;
    height: auto !important;
  }

  .parent-setting.opened > div[class^="col-md-"]:first-child {
    padding-bottom: 0;
  }

  .parent-setting.opened div[class^="col-md-"] + div[class^="col-md-"] {
    padding-bottom: 0;
  }

  .parent-setting.opened div[class^="col-md-"] + div[class^="col-md-"] {
    padding-left: 7.5px;
    height: auto;
  }

  .parent-setting.opened div[class^="col-md-"] + div[class^="col-md-"]:before {
    display: none;
  }

  .parent-setting.opened div[class^="col-md-"] + div[class^="col-md-"] input[type=checkbox] {
    margin-left: 0;
  }

  .parent-setting.opened + .nested-setting .form-group {
    margin-bottom: 5px;
    height: auto !important;
  }

  .parent-setting.opened + .nested-setting .form-group div[class^="col-md-"] + div[class^="col-md-"] {
    position: static;
    padding-left: 7.5px;
    height: auto;
  }

  .parent-setting.opened + .nested-setting .form-group div[class^="col-md-"] + div[class^="col-md-"]:before {
    display: none;
  }

  .parent-setting.opened + .nested-setting .form-group > div[class^="col-md-"] + div[class^="col-md-"]:after {
    display: none;
  }

  .parent-setting.opened + .nested-setting .form-group:last-child div[class^="col-md-"] + div[class^="col-md-"]:before {
    display: none;
  }

  .parent-setting.opened + .nested-setting .form-group > div[class^="col-md-"]:first-child {
    padding-bottom: 0;
  }

  .parent-setting.opened + .nested-setting .form-group div[class^="col-md-"] + div[class^="col-md-"] {
    padding-bottom: 0;
  }

  .dataTables_scrollBody .dataTables_wrapper {
    width: 100%;
    margin-left: 0;
  }

  div.dataTables_wrapper div.dataTables_info {
    margin-top: 0;
  }

  div.dataTables_wrapper .data-tables-refresh {
    margin-top: 8px;
  }

  .float-lg-left {
    float: none;
  }

  .float-lg-right {
    float: none;
  }

  /* CONFIGURATION STEPS */
  .card.configuration-steps {
    display: none;
  }
}

@media (max-width: 992px) {
  /*FORM ELEMENTS*/
  .form-horizontal .label-wrapper {
    float: none;
  }

  .form-horizontal .label-wrapper .col-form-label {
    text-align: left;
  }

  .form-horizontal .card-popup .label-wrapper {
    float: right;
  }

  .form-group .callout {
    margin-left: 0 !important;
    margin-top: 5px !important;
  }

  .card-search .form-control {
    max-width: 100%;
  }

  div.dataTables_wrapper div.dataTables_length {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  /* COMMON */
  .modal-dialog {
    margin-top: 30px;
  }

  /* FORM ELEMENTS */
  .form-horizontal .card-popup .label-wrapper {
    float: left;
  }

  .form-horizontal .card-popup .label-wrapper .col-form-label {
    text-align: left;
  }

  div.dataTables_wrapper div.dataTables_length {
    text-align: center;
  }

  .form-horizontal .card-popup .ico-help {
    display: none;
  }
}

@media (max-width: 572px) {
  .upload-picture-block .picture-container {
    display: block;
    vertical-align: top;
  }

  .upload-picture-block .upload-button-container {
    display: block;
    padding-left: 0;
    vertical-align: top;
  }

  div.dataTables_wrapper div.dataTables_length {
    text-align: center;
  }

  .float-lg-left {
    float: none;
  }

  .float-lg-right {
    float: none;
  }
}
