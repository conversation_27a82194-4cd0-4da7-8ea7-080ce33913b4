const multiDeviceService = require('../app/web/service/multiDeviceService');
const jwtService = require('../app/services/jwtService');
const db = require('../app/config/db');

console.log('=== Testing Device Info Detection ===');

// Test detect device info
const testUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
const deviceInfo = jwtService.getDeviceInfo({
    headers: {
        'user-agent': testUserAgent
    },
    ip: '*************'
});

console.log('1. Device Info từ jwtService.getDeviceInfo:');
console.log(deviceInfo);

console.log('\n2. Detect Device Info từ multiDeviceService:');
const detectedInfo = multiDeviceService.detectDeviceInfo(testUserAgent);
console.log(detectedInfo);

// Kết nối database để test
db.connect(db.MODE_PRODUCTION, async function () {
    console.log('\n3. Kết nối database thành công');
    
    try {
        // Tìm user tồn tại trong database
        console.log('\n4. Tìm user tồn tại trong database:');
        db.get().query('SELECT id, email, name FROM user LIMIT 5', function(err, users) {
            if (err) {
                console.error('Error querying users:', err);
                process.exit(1);
            }
            
            if (!users || users.length === 0) {
                console.error('Không có user nào trong database');
                process.exit(1);
            }
            
            const userId = users[0].id;
            console.log('Sử dụng user:', users[0]);
            
            const tokenId = 'test-token-' + Date.now();
            const ipAddress = '*************';
            
            console.log('\n5. Test tạo session với device info:');
            console.log('User ID:', userId);
            console.log('Token ID:', tokenId);
            console.log('Device Info:', deviceInfo);
            console.log('IP Address:', ipAddress);
            
            multiDeviceService.createSession(userId, tokenId, deviceInfo, ipAddress)
                .then(sessionResult => {
                    console.log('Session Result:', sessionResult);
                    
                    if (sessionResult.success) {
                        console.log('\n6. Kiểm tra session vừa tạo trong database:');
                        db.get().query('SELECT * FROM user_sessions WHERE jwt_token_id = ?', [tokenId], function(err, results) {
                            if (err) {
                                console.error('Error querying session:', err);
                            } else {
                                console.log('Session trong database:');
                                console.log(results[0]);
                                
                                // Xóa test session
                                db.get().query('DELETE FROM user_sessions WHERE jwt_token_id = ?', [tokenId], function(deleteErr) {
                                    if (deleteErr) {
                                        console.error('Error deleting test session:', deleteErr);
                                    } else {
                                        console.log('\n7. Đã xóa test session');
                                    }
                                    process.exit(0);
                                });
                            }
                        });
                    } else {
                        console.error('Không thể tạo session:', sessionResult.message);
                        process.exit(1);
                    }
                })
                .catch(error => {
                    console.error('Error creating session:', error);
                    process.exit(1);
                });
        });
        
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}); 