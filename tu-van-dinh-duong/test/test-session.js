const jwtService = require('../app/services/jwtService');
const multiDeviceService = require('../app/web/service/multiDeviceService');
const db = require('../app/config/db');

console.log('=== Testing Session Debug ===');

// Kết nối database
db.connect(db.MODE_PRODUCTION, async function () {
    console.log('Connected to database');
    
    try {
        // Test với user_id = 12 (user có allow_multiple_devices = 0)
        const userId = 12;
        
        console.log('\n1. Kiểm tra cài đặt session của user:', userId);
        const settings = await multiDeviceService.getUserSessionSettings(userId);
        console.log('Session settings:', settings);
        
        console.log('\n2. Kiểm tra các session active trực tiếp từ DB:');
        db.get().query('SELECT * FROM user_sessions WHERE user_id = ? AND is_active = 1 ORDER BY login_at DESC', [userId], function(err, results) {
            if (err) {
                console.error('Error:', err);
            } else {
                console.log('Active sessions count:', results.length);
                console.log('Active sessions:', results);
                
                console.log('\n3. Kiểm tra token trong bảng user:');
                db.get().query('SELECT id, email, jwt_token_id, token_created_at FROM user WHERE id = ?', [userId], function(err, userResults) {
                    if (err) {
                        console.error('Error:', err);
                    } else {
                        console.log('User token info:', userResults[0]);
                        
                        if (userResults[0] && userResults[0].jwt_token_id) {
                            console.log('\n4. Kiểm tra token có hợp lệ không:');
                            jwtService.validateTokenInDatabase(userId, userResults[0].jwt_token_id).then(isValid => {
                                console.log('Token valid in database:', isValid);
                                
                                // Test decode token
                                try {
                                    const jwt = require('jsonwebtoken');
                                    const payload = jwt.decode(userResults[0].jwt_token_id);
                                    console.log('Token payload:', payload);
                                } catch (e) {
                                    console.log('Cannot decode token (not a JWT):', e.message);
                                }
                                
                                process.exit(0);
                            });
                        } else {
                            console.log('User has no active token');
                            process.exit(0);
                        }
                    }
                });
            }
        });
        
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}); 