-- <PERSON><PERSON><PERSON> cập nhật database để hỗ trợ JWT authentication
-- Ch<PERSON>y script này để thêm trường jwt_token_id vào bảng user

-- Thêm trường jwt_token_id vào bảng user
ALTER TABLE `user` 
ADD COLUMN `jwt_token_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `session_id`,
ADD COLUMN `device_info` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `jwt_token_id`,
ADD COLUMN `token_created_at` TIMESTAMP NULL DEFAULT NULL AFTER `device_info`;

-- Tạo index cho jwt_token_id để tìm kiếm nhanh hơn
CREATE INDEX `idx_jwt_token_id` ON `user` (`jwt_token_id`);

-- <PERSON><PERSON><PERSON> nhật dữ liệu hiện tại: xóa session_id cũ và set jwt_token_id = NULL
UPDATE `user` SET `session_id` = '', `jwt_token_id` = NULL WHERE `session_id` IS NOT NULL AND `session_id` != '';

-- Thêm comment cho các trường mới
ALTER TABLE `user` 
MODIFY COLUMN `jwt_token_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JWT token identifier cho single device login',
MODIFY COLUMN `device_info` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Thông tin thiết bị (user agent, IP, etc.)',
MODIFY COLUMN `token_created_at` TIMESTAMP NULL DEFAULT NULL COMMENT 'Thời gian tạo token';

-- Tạo bảng user_sessions để lưu lịch sử đăng nhập (optional)
CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `jwt_token_id` VARCHAR(255) NOT NULL,
  `device_info` TEXT NULL,
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `login_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `logout_at` TIMESTAMP NULL DEFAULT NULL,
  `is_active` TINYINT(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_jwt_token_id` (`jwt_token_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_user_sessions_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Lịch sử đăng nhập của user'; 