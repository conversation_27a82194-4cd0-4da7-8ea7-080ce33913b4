const db = require('../../config/db');
const userAgentParser = require('ua-parser-js');

const multiDeviceService = {
    /**
     * Detect thông tin thiết bị từ User-Agent
     * @param {string} userAgent - User-Agent string
     * @returns {Object} - Thông tin thiết bị
     */
    detectDeviceInfo: (userAgent) => {
        const ua = userAgentParser(userAgent);
        return {
            deviceName: ua.device.model || ua.os.name || 'Thiết bị',
            deviceType: ua.device.type || 'desktop',
            browser: ua.browser.name || 'Unknown Browser',
            os: ua.os.name || 'Unknown OS',
            userAgent: userAgent
        };
    },

    /**
     * Tạo session mới cho user
     * @param {number} userId - ID của user
     * @param {string} tokenId - JWT token ID
     * @param {Object} deviceInfo - Thông tin thiết bị
     * @param {string} ipAddress - IP address
     * @returns {Promise<Object>} - Kết quả tạo session
     */
    createSession: function(userId, tokenId, deviceInfo, ipAddress) {
        return new Promise(async (resolve, reject) => {
            try {
                // Lấy cài đặt session của user
                const userSettings = await this.getUserSessionSettings(userId);
                const allowMultipleDevices = userSettings ? userSettings.allow_multiple_devices : 1;
                const maxSessions = userSettings ? userSettings.max_sessions : 5;

                // Kiểm tra số lượng session hiện tại
                const currentSessions = await this.getActiveSessions(userId);
                
                if (allowMultipleDevices) {
                    // Chế độ multi-device: kiểm tra giới hạn số session
                    if (currentSessions.length >= maxSessions) {
                        // Xóa session cũ nhất nếu vượt quá giới hạn
                        const oldestSession = currentSessions.sort((a, b) => 
                            new Date(a.login_at) - new Date(b.login_at)
                        )[0];
                        
                        await this.logoutSession(userId, oldestSession.jwt_token_id);
                    }
                } else {
                    // Chế độ single device: logout tất cả session cũ
                    for (const session of currentSessions) {
                        await this.logoutSession(userId, session.jwt_token_id);
                    }
                }
                
                // Detect thông tin thiết bị
                const detectedInfo = this.detectDeviceInfo(deviceInfo.userAgent);
                
                // Tạo session mới
                const sql = 'INSERT INTO user_sessions (user_id, jwt_token_id, device_name, device_type, browser, os, device_info, ip_address, user_agent, is_current_session, login_at, last_activity) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())';
                const params = [
                    userId, 
                    tokenId, 
                    detectedInfo.deviceName,
                    detectedInfo.deviceType,
                    detectedInfo.browser,
                    detectedInfo.os,
                    JSON.stringify(deviceInfo),
                    ipAddress,
                    deviceInfo.userAgent
                ];
                
                db.get().query(sql, params, function(err, result) {
                    if (err) return reject(err);
                    
                    // Cập nhật session hiện tại thành 0 cho tất cả session khác của user này
                    const updateSql = 'UPDATE user_sessions SET is_current_session = 0 WHERE user_id = ? AND id != ?';
                    db.get().query(updateSql, [userId, result.insertId], function(updateErr) {
                        if (updateErr) return reject(updateErr);
                        resolve({ success: true, sessionId: result.insertId });
                    });
                });
            } catch (error) {
                console.error('Error creating session:', error);
                reject(error);
            }
        });
    },

    /**
     * Lấy danh sách session đang hoạt động
     * @param {number} userId - ID của user
     * @param {Object} req - Request object (optional)
     * @returns {Promise<Array>} - Danh sách session
     */
    getActiveSessions: function(userId, req = null) {
        return new Promise((resolve, reject) => {
            const sql = 'SELECT * FROM user_sessions WHERE user_id = ? AND is_active = 1 ORDER BY last_activity DESC';
            db.get().query(sql, [userId], function(err, results) {
                if (err) return reject(err);
                
                // Xác định thiết bị hiện tại nếu có req
                let currentTokenId = null;
                if (req) {
                    const currentToken = req.cookies.jwt_token || req.headers.authorization?.replace('Bearer ', '');
                    if (currentToken) {
                        try {
                            const jwt = require('jsonwebtoken');
                            const payload = jwt.decode(currentToken);
                            currentTokenId = payload.tokenId;
                        } catch {}
                    }
                }
                
                const devices = results.map(row => {
                    let deviceInfo = {};
                    try {
                        deviceInfo = row.device_info ? JSON.parse(row.device_info) : {};
                    } catch {}
                    
                    return {
                        tokenId: row.jwt_token_id,
                        deviceName: row.device_name || 'Thiết bị',
                        deviceType: row.device_type || 'desktop',
                        browser: row.browser || '',
                        os: row.os || '',
                        ipAddress: row.ip_address,
                        lastActivity: row.last_activity || row.login_at,
                        isCurrentSession: row.jwt_token_id === currentTokenId
                    };
                });
                resolve(devices);
            });
        });
    },

    /**
     * Lấy cài đặt session của user
     * @param {number} userId - ID của user
     * @returns {Promise<Object>} - Cài đặt session
     */
    getUserSessionSettings: function(userId) {
        return new Promise((resolve, reject) => {
            const sql = 'SELECT * FROM user_session_settings WHERE user_id = ?';
            db.get().query(sql, [userId], function(err, results) {
                if (err) return reject(err);
                if (results && results.length > 0) {
                    resolve(results[0]);
                } else {
                    // Tạo cài đặt mặc định nếu chưa có
                    const defaultSettings = {
                        user_id: userId,
                        max_sessions: 5,
                        session_timeout_hours: 24,
                        allow_multiple_devices: 1,
                        notify_new_login: 1,
                        auto_logout_inactive: 1
                    };
                    
                    const insertSql = 'INSERT INTO user_session_settings (user_id, max_sessions, session_timeout_hours, allow_multiple_devices, notify_new_login, auto_logout_inactive) VALUES (?, ?, ?, ?, ?, ?)';
                    db.get().query(insertSql, [
                        defaultSettings.user_id,
                        defaultSettings.max_sessions,
                        defaultSettings.session_timeout_hours,
                        defaultSettings.allow_multiple_devices,
                        defaultSettings.notify_new_login,
                        defaultSettings.auto_logout_inactive
                    ], function(insertErr) {
                        if (insertErr) return reject(insertErr);
                        resolve(defaultSettings);
                    });
                }
            });
        });
    },

    /**
     * Cập nhật cài đặt session
     * @param {number} userId - ID của user
     * @param {Object} settings - Cài đặt mới
     * @returns {Promise<Object>} - Kết quả cập nhật
     */
    updateUserSessionSettings: function(userId, settings) {
        return new Promise((resolve, reject) => {
            const sql = 'INSERT INTO user_session_settings (user_id, max_sessions, session_timeout_hours, allow_multiple_devices, notify_new_login, auto_logout_inactive) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE max_sessions = VALUES(max_sessions), session_timeout_hours = VALUES(session_timeout_hours), allow_multiple_devices = VALUES(allow_multiple_devices), notify_new_login = VALUES(notify_new_login), auto_logout_inactive = VALUES(auto_logout_inactive)';
            db.get().query(sql, [
                userId, 
                settings.max_sessions, 
                settings.session_timeout_hours, 
                settings.allow_multiple_devices,
                settings.notify_new_login || 1,
                settings.auto_logout_inactive || 1
            ], function(err, result) {
                if (err) return reject(err);
                resolve({ success: true });
            });
        });
    },

    /**
     * Logout một session
     * @param {number} userId - ID của user
     * @param {string} tokenId - JWT token ID
     * @returns {Promise<Object>} - Kết quả logout
     */
    logoutSession: function(userId, tokenId) {
        return new Promise((resolve, reject) => {
            const sql = 'UPDATE user_sessions SET is_active = 0, logout_at = NOW(), is_current_session = 0 WHERE user_id = ? AND jwt_token_id = ? AND is_active = 1';
            db.get().query(sql, [userId, tokenId], function(err, result) {
                if (err) return reject(err);
                resolve({ success: true });
            });
        });
    },

    /**
     * Logout tất cả session khác
     * @param {number} userId - ID của user
     * @param {string} currentTokenId - Token ID hiện tại
     * @returns {Promise<Object>} - Kết quả logout
     */
    logoutAllOtherSessions: function(userId, currentTokenId) {
        return new Promise((resolve, reject) => {
            let sql, params;
            if (currentTokenId) {
                sql = 'UPDATE user_sessions SET is_active = 0, logout_at = NOW(), is_current_session = 0 WHERE user_id = ? AND jwt_token_id != ? AND is_active = 1';
                params = [userId, currentTokenId];
            } else {
                // Nếu currentTokenId là null, logout tất cả session active
                sql = 'UPDATE user_sessions SET is_active = 0, logout_at = NOW(), is_current_session = 0 WHERE user_id = ? AND is_active = 1';
                params = [userId];
            }
            db.get().query(sql, params, function(err, result) {
                if (err) return reject(err);
                resolve({ success: true });
            });
        });
    },

    /**
     * Cập nhật thời gian hoạt động cuối cùng
     * @param {string} tokenId - JWT token ID
     * @returns {Promise<Object>} - Kết quả cập nhật
     */
    updateLastActivity: function(tokenId) {
        return new Promise((resolve, reject) => {
            const sql = 'UPDATE user_sessions SET last_activity = NOW() WHERE jwt_token_id = ? AND is_active = 1';
            db.get().query(sql, [tokenId], function(err, result) {
                if (err) return reject(err);
                resolve({ success: true });
            });
        });
    },

    /**
     * Xóa session hết hạn
     * @returns {Promise<Object>} - Kết quả xóa
     */
    cleanupExpiredSessions: function() {
        return new Promise((resolve, reject) => {
            const timeoutHours = 24; // Có thể lấy từ cài đặt
            const expiredTime = new Date(Date.now() - timeoutHours * 60 * 60 * 1000);
            
            const sql = 'UPDATE user_sessions SET logout_at = NOW(), is_active = 0, is_current_session = 0 WHERE last_activity < ? AND is_active = 1';
            db.get().query(sql, [expiredTime], function(err, result) {
                if (err) return reject(err);
                resolve({ success: true, affectedRows: result.affectedRows });
            });
        });
    }
};

module.exports = multiDeviceService; 