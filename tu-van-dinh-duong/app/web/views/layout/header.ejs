﻿<header class="header" id="header">
  <div class="container">
    <button class="sidebar-toggle hamburger hamburger--slider d-lg-none" id="sidebar-toggle" data-bs-toggle="offcanvas" data-bs-target="#sidebar" aria-controls="sidebar">
      <span class="hamburger-box d-block">
        <span class="hamburger-inner"></span>
      </span>
    </button>
    <a class="header-logo d-lg-none" href="#">
      <img class="img-fluid w-100" src="/public/content/images/logo3-compact.png" alt="" />
    </a>
    <div class="header-filter offcanvas-lg offcanvas-end" id="header-filter" tabindex="-1">
      <div class="offcanvas-header d-lg-none">
        <h4 class="offcanvas-title">Tìm kiếm</h4>
        <button class="btn-close ms-auto" type="button" data-bs-dismiss="offcanvas" data-bs-target="#header-filter" aria-label="Close"></button>
      </div>
      <form action="/search" method="get" id="search_filter">
        <div class="row">
          <% if(user.role_id.includes(1) || user.role_id.includes(3)){ %>
          <div id="hospital_filter" class="col-lg-auto">
            <div id="hospital_select" name="hospital_ids"></div>
          </div>
          <% } %>
          <div class="col-lg">
            <div class="search">
              <input class="form-control search-input" id="sfilter_keyword" name="keyword" type="text" placeholder="Nhập từ khoá tìm kiếm" aria-label="Nhập từ khoá tìm kiếm" value="<%=filter.search.keyword%>"" />
              <button class="btn search-btn" type="submit">
                <svg class="iconsvg-search">
                  <use xlink:href="/public/content/images/sprite.svg#search"></use>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="header-action">
      <div class="header-action-item">
        <a class="header-action-link d-lg-none" href="#" data-bs-toggle="offcanvas" data-bs-target="#header-filter" aria-controls="header-filter">
          <svg class="iconsvg-search header-action-icon">
            <use xlink:href="/public/content/images/sprite.svg#search"></use>
          </svg>
        </a>
      </div>
      <%- include('../layout/notification') %>
      <div class="header-action-item header-acc">
        <a class="dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-reference="parent" aria-expanded="false">
          <!-- <img class="img-fluid header-acc-avatar" src="/public/content/images/ex/avatar.jpg" alt=""> -->
          <% if (user){ %>
            <% if(user.gender == 1){ %>
              <img class="img-fluid header-acc-avatar" src="/public/content/images/doctor-male.svg" alt="">
            <% }else{ %>
              <img class="img-fluid header-acc-avatar" src="/public/content/images/doctor-female.svg" alt="">
            <% } %>
          <% }else{ %>
            <img class="img-fluid header-acc-avatar" src="/public/content/images/user-icon.svg" alt="">
          <% } %>
          
          <div class="ms-2 d-none d-xl-block">
            <% if (user){%>
            <div class="header-acc-name"><%= user.full_name ? user.full_name : user.email %></div>
            <div class="header-acc-info"><%= user.isAdmin == true ? "Administrator" : (user.role_id.includes(4) ? "Bác sĩ" : (user.role_id.includes(5) ? "Quản lý" : (user.role_id.includes(3) ? "Hệ thống" : "Khách Hàng")))%></div>
            <% } else { %>
            <div class="header-acc-name">Ẩn danh</div>
            <% } %>
          </div>
        </a>
        <ul class="dropdown-menu dropdown-menu-end">
          <li>
            <a class="dropdown-item" href="/user/profile">Thông tin cá nhân</a>
          </li>
          <li>
            <a class="dropdown-item" href="/devices/page">Thiết bị đăng nhập</a>
          </li>
          <li>
            <a class="dropdown-item" href="/user/logout">Đăng xuất</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</header>
<script type="text/javascript">
  VirtualSelect.init({
    ele: '#hospital_select',
    options: <%- JSON.stringify(filter.hospitals) %>,
    dropboxWrapper: '#hospital_filter',
    placeholder: 'Bệnh viện',
    search: true,
    multiple: true,
    additionalClasses: 'vscomp-status website-status',
    showDropboxAsPopup: false,
    selectAllText: 'Chọn tất cả',
    optionsSelectedText: 'Lựa chọn',
    allOptionsSelectedText: 'Tất cả',
    <% if(filter.search.hospital_ids != ''){%>
    selectedValue: <%- JSON.stringify(filter.hospitalIds) %>
    <%}%>
  });

  $(document).ready(function () {
    $("#search_filter #sfilter_keyword").keyup(function() {
      if(parseInt($("#search_filter #sfilter_keyword").val()) !== ""){
        if (event.keyCode === 13) {
          event.preventDefault();
          document.getElementById("search_filter").submit();
        }
      }
    });
  })
</script>