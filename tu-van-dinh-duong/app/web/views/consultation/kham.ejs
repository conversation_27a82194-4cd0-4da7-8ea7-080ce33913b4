<form id="kham_form" class="form-horizontal">
    <% if(isDetail !== true){ %>
    <h3 class="box-title-break">T<PERSON><PERSON> kiếm</h3>
    <div class="row align-items-center">
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="phone_search" style="width: 8rem">Tên - Số điện thoại</label>
            <div class="col-form-body col-md">
                <div class="form-control-has-addon">
                    <select class="form-select" name="phone_search" id="phone_search" data-plugin="select2" data-placeholder="Tên hoặc số điện thoại người bệnh">
                    </select>
                    <a class="table-more-link link-primary text-lg-center mt-2" style="cursor: pointer; float: right;display: none;" id="btn_show_history" onclick="showHistory()">Xem lịch sử khám
                        <svg class="iconsvg-chevron-right rotate-90 fs-base ms-1">
                            <use xlink:href="/public/content/images/sprite.svg#chevron-right"></use>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row g-2 align-items-center" id="table_history">

    </div>
    <% } %>
    <h3 class="box-title-break">Thông tin chung</h3>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_name">Họ và tên
                <span class="text-danger">*</span>
            </label>
            <div class="col-form-body col-md">
                <div class="form-control-has-addon">
                    <% if(isDetail !== true){ %>
                        <input type="text" name="cus_name" class="form-control form-control-title" id="cus_name" placeholder="Họ và tên" value="<%= consultation.cus_name%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_name')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    <% }else{ %>
                        <div class="form-control form-control-title"><%= consultation.cus_name%></div>
                    <% } %>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_phone">Phone
                <span class="text-danger">*</span>
            </label>
            <div class="col-form-body col-md">
                <div class="form-control-has-addon">
                    <% if(isDetail !== true){ %>
                        <input type="text" name="cus_phone" data-type="number" class="form-control form-control-title" id="cus_phone" placeholder="Tìm kiếm theo số điện thoại" value="<%= consultation.cus_phone%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_phone')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    <% }else{ %>
                        <div class="form-control form-control-title"><%= consultation.cus_phone%></div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
	<div class="row g-2 align-items-center">
        <div class="col-12 col-md-2 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_gender">Giới tính
                <span class="text-danger">*</span>
            </label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <% if(isDetail !== true){ %>
                        <select class="form-select" name="cus_gender" id="cus_gender" data-plugin="select2" data-placeholder="Giới tính" data-minimum-results-for-search="Infinity">
                            <option></option>
                            <option value="1" <%=consultation.cus_gender == '1' ? 'selected' : ''%>>Nam</option>
                            <option value="0" <%=consultation.cus_gender == '0' ? 'selected' : ''%>>Nữ</option>
                            <option value="2" <%=consultation.cus_gender == '2' ? 'selected' : ''%>>Khác</option>
                        </select>
                    <% }else{ %>
                        <div class="form-control form-control-title"><%= (consultation.cus_gender == '1') ? 'Nam' : (consultation.cus_gender == '0' ? 'Nữ' : 'Khác')%></div>
                    <% } %>    
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <div class="d-flex me-2">
                <label class="col-form-label col-md-auto fw-bold mt-2 me-2" for="cus_birthday">Ngày sinh
                    <span class="text-danger">*</span>
                </label>
                <div class="col-form-body col-md">
                    <div class="form-control-has-addon">
                        <% if(isDetail !== true){ %>
                            <input type="text" class="form-control form-control-title p-1" id="cus_birthday" placeholder="Ngày sinh" value="<%=moment(consultation.cus_birthday).format('DD-MM-YYYY')%>">
                        <% }else{ %>
                            <input type="text" disabled class="form-control form-control-title p-1" id="cus_birthday" placeholder="Ngày sinh" value="<%=moment(consultation.cus_birthday).format('DD-MM-YYYY')%>">
                        <% } %>   
                    </div>
                </div>
            </div>
            <div class="d-flex">
                <label class="col-md-auto fw-bold mt-2 me-2" for="cus_age" data-type='1'>Tuổi
                    <!-- <span class="text-danger">*</span> -->
                </label>
                <div class="col-form-body col-md">
                    <div class="form-control-has-addon">
                        <input type="number" name="cus_age" class="form-control form-control-title p-1" id="cus_age" placeholder="Tuổi">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_email">Email
                <!-- <span class="text-danger">*</span> -->
            </label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" name="cus_email" class="form-control form-control-title" id="cus_email" placeholder="Email" value="<%= consultation.cus_email%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_email')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_email%></div>
                <% } %>  
            </div>
        </div>
	</div>
	<div class="row g-2 align-items-center">
        <label class="col-form-label col-md-auto fw-bold" for="cus_address">Địa chỉ
            <!-- <span class="text-danger">*</span> -->
        </label>
	    <div class="col-form-body col-md">
            <% if(isDetail !== true){ %>
                <div class="form-control-has-addon">
                    <textarea class="form-control" id="cus_address" placeholder="Nhập địa chỉ" name="cus_address"><%= consultation.cus_address%></textarea>
                    <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_address')">
                        <svg class="iconsvg-close">
                        <use xlink:href="/public/content/images/sprite.svg#close"></use>
                        </svg>
                    </button>
                </div>
            <% }else{ %>
                <div class="form-control form-control-title"><%= consultation.cus_address%></div>
            <% } %>  
	    </div>
	</div>
    <div class="row g-2 align-items-center">
        <div class="col-12 d-flex">
            <label class="col-form-label col-md-auto fw-bold" for="cus_anamnesis">Tiền sử bệnh</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <textarea class="form-control" id="cus_anamnesis" placeholder="Tiền sử bệnh" name="cus_anamnesis"><%= consultation.cus_anamnesis%></textarea>
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_anamnesis')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_anamnesis%></div>
                <% } %>  
            </div>
        </div>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 d-flex">
            <label class="col-form-label col-md-auto fw-bold" for="cus_living_habits">Thói quen ăn uống sinh hoạt</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <textarea class="form-control" id="cus_living_habits" placeholder="Thói quen ăn uống sinh hoạt" name="cus_living_habits"><%= consultation.cus_living_habits%></textarea>
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_living_habits')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_living_habits%></div>
                <% } %>  
            </div>
        </div>
    </div>
    <div class="row g-2 align-items-center">
        <label class="col-form-label col-md-auto fw-bold" for="diagnostic">Chẩn đoán
          <!-- <span class="text-danger">*</span> -->
        </label>
        <div class="col-form-body col-md">
            <% if(isDetail !== true){ %>
                <div class="form-control-has-addon">
                    <textarea class="form-control" id="diagnostic" name="diagnostic" placeholder="Nhập chẩn đoán"><%= consultation.diagnostic%></textarea>
                    <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#diagnostic')">
                        <svg class="iconsvg-close">
                        <use xlink:href="/public/content/images/sprite.svg#close"></use>
                        </svg>
                    </button>
                </div>
            <% }else{ %>
                <div class="form-control form-control-title"><%= consultation.diagnostic%></div>
            <% } %>  
        </div>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_cctc">CCKN (m)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_cctc" class="form-control form-control-title" id="cus_cctc" placeholder="Chiều cao tiêu chuẩn" value="<%= consultation.cus_cctc%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_cctc')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_cctc%></div>
                <% } %>  
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_cntc">CNKN (kg)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_cntc" class="form-control form-control-title" id="cus_cntc" placeholder="Cân nặng tiêu chuẩn" value="<%= consultation.cus_cntc%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_cntc')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_cntc%></div>
                <% } %> 
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_cnbt">CNBT (kg)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_cnbt" class="form-control form-control-title" id="cus_cnbt" placeholder="Cân nặng thường có" value="<%= consultation.cus_cnbt%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_cnbt')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_cnbt%></div>
                <% } %> 
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_length">Cao (m)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_length" class="form-control form-control-title" id="cus_length" placeholder="Chiều cao" value="<%= consultation.cus_length%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_length')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_length%></div>
                <% } %> 
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_cnht">Cân nặng (kg)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_cnht" class="form-control form-control-title" id="cus_cnht" placeholder="Cân nặng hiện tại" value="<%= consultation.cus_cnht%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_cnht')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_cnht%></div>
                <% } %> 
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_bmi">BMI</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_bmi" class="form-control form-control-title" id="cus_bmi" placeholder="BMI" value="<%= consultation.cus_bmi%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_bmi')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_bmi%></div>
                <% } %> 
            </div>
        </div>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_height_by_age">CC theo tuổi(Cm)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_height_by_age" class="form-control form-control-title" id="cus_height_by_age" placeholder="Chiều cao theo tuổi" value="<%= consultation.cus_height_by_age%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_height_by_age')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_height_by_age%></div>
                <% } %> 
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_weight_by_age">CN theo tuổi(Kg)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_weight_by_age" class="form-control form-control-title" id="cus_weight_by_age" placeholder="Cân nặng theo tuổi" value="<%= consultation.cus_weight_by_age%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_weight_by_age')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_weight_by_age%></div>
                <% } %> 
            </div>
        </div>
        
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_bmi_by_age">BMI theo Tuổi</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_bmi_by_age" class="form-control form-control-title" id="cus_bmi_by_age" placeholder="BMI theo tuổi" value="<%= consultation.cus_bmi_by_age%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_bmi_by_age')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_bmi_by_age%></div>
                <% } %> 
            </div>
        </div>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-8 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_ncdd">NCDD</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" name="cus_ncdd" class="form-control form-control-title" id="cus_ncdd" placeholder="Nhu cầu dinh dưỡng khuyến nghị" value="<%= consultation.cus_ncdd%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_ncdd')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_ncdd%></div>
                <% } %> 
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_height_by_weight">CN - CC(Kg)</label>
            <div class="col-form-body col-md">
                <% if(isDetail !== true){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_height_by_weight" class="form-control form-control-title" id="cus_height_by_weight" placeholder="Chiều cao theo cân nặng" value="<%= consultation.cus_height_by_weight%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_height_by_weight')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= consultation.cus_height_by_weight%></div>
                <% } %> 
            </div>
        </div>
    </div>
    
    <div class="row g-2 justify-content-center mt-0">
        <% if(isDetail !== true){ %>
            <div class="col-6 col-md-auto">
                <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="saveConsultation(0, {})" title="Lưu">
                    <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                    <use xlink:href="/public/content/images/sprite.svg#save"></use>
                    </svg> Lưu
                </button>
            </div>
            <div class="col-6 col-md-auto">
                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" onclick="returnList1()" title="Quay lại danh sách">
                    <svg class="iconsvg-close flex-shrink-0 fs-16px me-2">
                    <use xlink:href="/public/content/images/sprite.svg#close"></use>
                    </svg>Huỷ
                </button>
            </div>
        <% } %>
    </div>
</form>