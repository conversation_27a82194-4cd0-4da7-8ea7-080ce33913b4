<form id="chidinhcanlamsang_form" class="form-horizontal">
    <h3 class="box-title-break">Chỉ định cận lâm sàng</h3>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="phone_search" style="width: 8rem">Loại xét nghiệm</label>
            <div class="col-form-body col-md">
                <div class="form-control-has-addon">
                    <select class="form-select" name="medical_test_type" id="medical_test_type" data-plugin="select2" data-placeholder="Chọn loại xét nghiệm">
                        <% if(medicalTestType.length > 0){ %>
                            <% for(let med of medicalTestType){ %>
                                <option value="<%=med.id %>"><%=med.name %></option>
                            <% } %>   
                        <% } %>    
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row g-2 align-items-center" id="list_medical_test">
        
    </div>
    <div class="row g-2 justify-content-center mt-0">
      <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
        <div class="col-6 col-md-auto">
          <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="saveExamine(2, 0, {})" title="Lưu phiếu khám">
            <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
              <use xlink:href="/public/content/images/sprite.svg#save"></use>
            </svg>Lưu
          </button>
        </div>
        <div class="col-6 col-md-auto">
          <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" onclick="returnList()" title="Hủy thao tác sửa phiếu khám">
            <svg class="iconsvg-close flex-shrink-0 fs-16px me-2">
            <use xlink:href="/public/content/images/sprite.svg#close"></use>
            </svg>Huỷ
          </button>
        </div>
      <% } %>
    </div>
</form>