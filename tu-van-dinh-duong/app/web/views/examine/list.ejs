<div class="box">
  <div class="box-header">
    <h2 class="box-title"><PERSON><PERSON> sách phiếu kh<PERSON>m <small class="text-icon fw-6">(<%=paginatorExamine.totalItem%>)</small>
    </h2>
    <div class="paginate fs-13px ms-auto d-none d-md-flex">
      <%- include('../component/paginator_examine') %>
    </div>
  </div>
  <div class="box-body box-table">
    <div class="table-responsive-xl table-responsive-flush mb-4">
      <div class="table-responsive-inner"></div>
      <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
        <thead>
          <tr>
            <td id="td_id_count">
              <div class="dropdown table-sort">
                <span class="table-sort-icon">
                  <button class="btn" type="button" title="Tăng dần" onclick="getDataSort(1, 'id_count', 1)"></button>
                  <button class="btn" type="button" title="Giảm dần" onclick="getDataSort(0, 'id_count', 1)"></button>
                </span>
                <a class="flex-center-y dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-offset="0, 20" data-bs-auto-close="outside" aria-expanded="false">Mã phiếu</a>
                <div class="dropdown-menu">
                  <input class="form-control" id="search_id_count" type="text" placeholder="Nhập từ khoá tìm kiếm" onkeyup="getDataSearch(1, 'id_count')"/>
                </div>
              </div>
            </td>
            <td id="td_cus_name">
              <div class="dropdown table-sort">
                <a class="flex-center-y dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-offset="0, 20" data-bs-auto-close="outside" aria-expanded="false">Tên bệnh nhân</a>
                <div class="dropdown-menu">
                  <input class="form-control" id="search_cus_name" type="text" placeholder="Nhập từ khoá tìm kiếm" onkeyup="getDataSearch(1, 'cus_name')"/>
                </div>
              </div>
            </td>
            <td id="td_cus_phone">
              <div class="dropdown table-sort">
                <span class="table-sort-icon">
                  <button class="btn" type="button" title="Tăng dần" onclick="getDataSort(1, 'cus_phone', 1)"></button>
                  <button class="btn" type="button" title="Giảm dần" onclick="getDataSort(0, 'cus_phone', 1)"></button>
                </span>
                <a class="flex-center-y dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-offset="0, 20" data-bs-auto-close="outside" aria-expanded="false">Điện thoại</a>
                <div class="dropdown-menu">
                  <input class="form-control" id="search_cus_phone" type="text" placeholder="Nhập từ khoá tìm kiếm" onkeyup="getDataSearch(1, 'cus_phone')"/>
                </div>
              </div>
            </td>
            <td id="td_cus_address">
              <div class="dropdown table-sort">
                <a class="flex-center-y dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-offset="0, 20" data-bs-auto-close="outside" aria-expanded="false">Địa chỉ</a>
                <div class="dropdown-menu">
                  <input class="form-control" id="search_cus_address" type="text" placeholder="Nhập từ khoá tìm kiếm" onkeyup="getDataSearch(1, 'cus_address')"/>
                </div>
              </div>
            </td>
            <td id="id_diagnostic">
              <div class="dropdown table-sort">
                <a class="flex-center-y dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-offset="0, 20" data-bs-auto-close="outside" aria-expanded="false">Chuẩn đoán</a>
                <div class="dropdown-menu">
                  <input class="form-control" id="search_diagnostic" type="text" placeholder="Nhập từ khoá tìm kiếm" onkeyup="getDataSearch(1, 'diagnostic')"/>
                </div>
              </div>
            </td>
            <td id="td_time_examine">
              <div class="table-sort">
                <span class="table-sort-icon">
                  <button class="btn" type="button" title="Tăng dần" onclick="getDataSort(1, 'time_examine', 1)"></button>
                  <button class="btn" type="button" title="Giảm dần" onclick="getDataSort(0, 'time_examine', 1)"></button>
                </span>
                <div id="search_time_examine" class="flatpickr flatpickr-hide-input" data-plugin="flatpickr" data-options='{"mode":"range"}'>
                  <input class="form-control invisible" type="text" placeholder="dd-mm-yyyy" value="" data-input="data-input" aria-label="dd-mm-yyyy" />
                  <span class="cursor-pointer" data-toggle="data-toggle">Thời gian</span>
                </div>
              </div>
            </td>
            <td class="w-1">
              <div class="dropdown">
                <a class="flex-center-y dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-offset="0, 20" data-bs-auto-close="outside" aria-expanded="false">Trạng thái</a>
                <div class="dropdown-menu" id="status_examine_dropdown">
                  <div class="tag_selected mb-2">
                    <div class="tag-body"></div>
                    <button class="btn tag-clear btn-link p-0 flex-shrink-0" type="button" style="visibility: hidden;" id="status_examine_dropdown_clear_all" onclick="deleteAllTagCheckBoxSearch('status_examine')">
                      <svg class="iconsvg-close">
                        <use xlink:href="/public/content/images/sprite.svg#close"></use>
                      </svg>
                    </button>
                  </div>
                  <div class="scroll-y">
                    <%if(listStatus.data.length > 0){%>
                      <%for(let itemStt of listStatus.data){%>
                        <label class="dropdown-item dropdown-item-checkbox">
                          <input class="form-check-input fs-16px mt-1" type="checkbox" aria-label="" value="<%=itemStt.value %>" id="status_examine_check_<%=itemStt.value%>" onchange="getValueSearchCheckBox('#status_examine_check_<%=itemStt.value%>', 'status_examine', '<%=itemStt.label%>', 1)"/>
                          <%-itemStt.label%>
                        </label>
                      <%}%>
                    <%}%>
                  </div>
                </div>
              </div>
            </td>
            <td class="w-1">Hành động</td>
          </tr>
        </thead>
        <tbody id="table-tbody-examine">
          <% if (listExamine.length > 0){%>
            <% for (i = 0;i < listExamine.length;i++){%>
              <tr id="examine_<%=listExamine[i].id%>">
                <td>
                  <a target="_blank" href="/examine/edit/<%=listExamine[i].id%>?detail=true">
                    <span class="text-primary fw-6 fs-13px"><%=listExamine[i].count_id%></span>
                  </a>
                </td>
                <td class="min-w-150px">
                  <div class="flex-center-y fs-13px">
                    <img class="img-fluid me-2" src="/public/content/images/user.svg" alt="" />
                    <%=listExamine[i].cus_name%>
                  </div>
                </td>
                <td class="fs-6 text-red"><%=listExamine[i].cus_phone%></td>
                <td class="fs-13px text-primary min-w-150px"><%=listExamine[i].cus_address%></td>
                <td class="fs-13px"><%=listExamine[i].diagnostic%></td>
                <td class="fs-13px"><%=moment(listExamine[i].created_at).format("HH:mm:ss")%>
                  <br>
                  <small class="text-body-2"><%=moment(listExamine[i].created_at).format("DD/MM/YYYY")%></small>
                </td>
                <td>
                  <span class="badge badge-<%=webService.examineStatusClass(listExamine[i].status).name%>">
                    <%=webService.examineStatusClass(listExamine[i].status).value%>
                  </span>
                </td>
                <td>
                  <div class="flex-center-x gap-10px" style="position: relative;">
                    <a title="Sửa" class="btn btn-action btn-action-edit" target="_blank" href="/examine/edit/<%=listExamine[i].id%>">
                      <svg class="iconsvg-edit-2">
                         <use xlink:href="/public/content/images/sprite.svg#edit-2"></use>
                      </svg>
                    </a>
                    <button class="btn btn-action btn-action-cancel" title="Huỷ phiếu khám" type="button" onclick="showModalCancelExamine('<%=listExamine[i].id%>','<%=listExamine[i].status%>')">
                      <svg class="iconsvg-close-circle">
                         <use xlink:href="/public/content/images/sprite.svg#trash"></use>
                      </svg>
                   </button>
                  </div>
                </td>
              </tr>
            <%}%>
          <%} else {%>
          <tr>
            <td colspan="9" style="text-align: center;"><b>Không có dữ liệu!</b></td>
          </tr>
          <% } %>
        </tbody>
      </table>
    </div>
    <div class="paginate fs-13px mx-auto">
      <%- include('../component/paginator_examine') %>
    </div>
  </div>
</div>
