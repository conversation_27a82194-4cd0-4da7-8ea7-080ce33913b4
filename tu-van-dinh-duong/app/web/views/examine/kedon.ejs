<form id="kham_form" class="form-horizontal">
    <h3 class="box-title-break">T<PERSON> vấn thực phẩm bổ xung</h3>
    <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
      <div class="row g-2 align-items-center">
        <div class="col-12 col-md-6 col-xl-4 d-flex">
          <label class="col-form-label col-md-auto fw-bold mt-2" for="medicine_type_id">Phân loại</label>
          <div class="col-form-body col-md w-100">
            <div class="form-control-has-addon">
              <select class="form-select" id="medicine_type_id" data-plugin="select2" data-placeholder="Chọn phân loại">
                <option value="0">Chưa phân loại</option>
                <% if(medicineType.length > 0){ %>
                  <% for (let metype of medicineType){ %>
                    <option value="<%= metype.id %>"><%= metype.name %></option>
                  <%}%>   
                <%}%>
              </select>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 col-xl-4 d-flex">
          <label class="col-form-label col-md-auto fw-bold mt-2" for="medicine_id">Tên thuốc
              <span class="text-danger">*</span>
          </label>
          <div class="col-form-body col-md w-100">
            <div class="form-control-has-addon">
              <select class="form-select" id="medicine_id" data-plugin="select2" data-placeholder="Chọn thuốc"></select>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 col-xl-2 d-flex">
          <label class="col-form-label col-md-auto fw-bold mt-2" for="total_medinice">Số lượng
              <span class="text-danger">*</span>
          </label>
          <div class="col-form-body col-md w-100">
            <div class="form-control-has-addon">
              <input type="number" class="form-control form-control-title p-1" id="total_medinice" placeholder="Số lượng">
              <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#total_medinice')">
                  <svg class="iconsvg-close">
                      <use xlink:href="/public/content/images/sprite.svg#close"></use>
                  </svg>
              </button>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 col-xl-2 d-flex">
          <label class="col-form-label col-md-auto fw-bold mt-2" for="unit_medinice">ĐVT
            <span class="text-danger">*</span>
          </label>
          <div class="col-form-body col-md w-100">
            <div class="form-control-has-addon">
              <input type="text" class="form-control form-control-title p-1" id="unit_medinice" placeholder="Đơn vị tính">
              <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#unit_medinice')">
                <svg class="iconsvg-close">
                    <use xlink:href="/public/content/images/sprite.svg#close"></use>
                </svg>
            </button>
            </div>
          </div>
        </div>
      </div>
      <div class="row g-2 align-items-center">
        <div class="col-12 d-flex">
          <label class="col-form-label col-md-auto fw-bold mt-2" for="use_medinice">Cách sử dụng
            <span class="text-danger">*</span>
          </label>
          <div class="col-form-body col-md">
            <div class="input-group">
              <input type="text" class="form-control form-control-title p-1" id="use_medinice" placeholder="Cách sử dụng">
              <button class="btn btn-primary" type="button" onclick="addMedicine()" title="Thêm dữ liệu thuốc">Thêm</button>
            </div>
          </div>
        </div>
      </div>
    <% } %>
    <div class="row g-2 align-items-center">
      <div class="table-responsive-xl table-responsive-flush mb-4" style="width: 100%;display: none;" id="tb_prescription">
        <div class="table-responsive-inner"></div>
        <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
          <thead>
            <tr>
              <td>STT</td>
              <td>
                <span>Tên thuốc</span>
              </td>
              <td>
                <span>HDSD</span>
              </td>
              <td style="width: 100px;">
                <span>Số lượng</span>
              </td>
              <td>
                <span>Đơn vị tính</span>
              </td>
              <td id="active_table_medicine" class="w-1">Hành động</td>
            </tr>
          </thead>
          <tbody>
            
          </tbody>
        </table>
      </div>
    </div>
    <div class="row g-2 justify-content-center mt-0">
      <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
        <% if(examine.status !== 3){ %>
          <div class="col-6 col-md-auto">
            <button class="btn btn-success box-btn w-100 text-uppercase" type="button" onclick="saveExamine(0, 0, {})" title="Hoàn thành phiếu khám">
              <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                <use xlink:href="/public/content/images/sprite.svg#save"></use>
              </svg>Lưu
            </button>
          </div>
        <% } %>
        <div class="col-6 col-md-auto">
          <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="saveExamine(3, 0, {})" title="Hoàn thành phiếu khám">
            <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
              <use xlink:href="/public/content/images/sprite.svg#save"></use>
            </svg><%= examine.status == 3 ? 'Lưu' : 'Hoàn thành'%>
          </button>
        </div>
      <% } %>
      <div class="col-6 col-md-auto">
        <button class="btn btn-action-edit box-btn w-100 text-uppercase" type="button" onclick="saveExamine(0,0,{isExport:true, linkExport:'examine'})" title="Tải phiếu khám">
          <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
            <use xlink:href="/public/content/images/sprite.svg#download"></use>
          </svg>Phiếu khám
        </button>
      </div>
      <div class="col-6 col-md-auto">
        <button class="btn btn-action-send box-btn w-100 text-uppercase" type="button" onclick="saveExamine(0,0,{isExport:true, linkExport:'prescription'})" title="Tải phiếu tư vấn">
          <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
            <use xlink:href="/public/content/images/sprite.svg#download"></use>
          </svg>Phiếu tư vấn
        </button>
      </div>
    </div>  
</form>
