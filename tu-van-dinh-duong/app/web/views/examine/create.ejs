<html lang="en">
   <head>
      <%- include('../layout/head') %>
      <title>Tiế<PERSON> nhận kh<PERSON>m</title>
   </head>
   <body>
      <div class="page">
         <%- include('../layout/header',{user: user}) %>
         <%- include('../layout/sidebar') %>
         <div class="page-main">
            <% if (errors.length > 0){%>
            <div class="container-fluid">
               <div class="row gy-32px">
                     <div class="alert-dismissable">
                         <div class="alert alert-danger">
                             <ul>
                                 <% for (i = 0;i < errors.length;i++){%>
                                     <li><%=errors[i]%></li>
                                 <%}%>
                             </ul>
                         </div>
                     </div>
               </div>
            </div>
            <% } else { %>
            <div class="container">
              <div id="dung_bai_tab">
                <div class="tab box">
                  <ul class="tab-nav nav nav-tabs justify-content-center">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="#kham" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="true" id="kham-tab" onclick="changeTabExamine(1)">Khám</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#tuvandinhduong" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="false" id="tuvandinhduong-tab" onclick="changeTabExamine(2)">Tư vấn dinh dưỡng</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#thucdonmau" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="false" id="thucdonmau-tab" onclick="changeTabExamine(3)">Thực đơn mẫu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#kedon" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="false" id="kedon-tab" onclick="changeTabExamine(4)">Kê đơn</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#chidinhcanlamsang" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="false" id="chidinhcanlamsang-tab" onclick="changeTabExamine(5)">Chỉ định cận lâm sàng</a>
                    </li>
                  </ul>
                </div>

                <div class="tab-content">
                  <div class="tab-pane fade box-body show active" id="kham">
                    <%- include('../examine/kham') %>
                  </div>
                  <div class="tab-pane fade box-body" id="tuvandinhduong">
                    <%- include('../examine/tuvandinhduong') %>
                  </div>
                  <div class="tab-pane fade box-body" id="thucdonmau">
                    <%- include('../examine/thucdonmau') %>
                  </div>
                  <div class="tab-pane fade box-body" id="kedon">
                    <%- include('../examine/kedon') %>
                  </div>
                  <div class="tab-pane fade box-body" id="chidinhcanlamsang">
                    <%- include('../examine/chidinhcanlamsang') %>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal fade modal-table-lg" id="modal-chi-tiet-phieu-kham" tabindex="-1" aria-hidden="true">
              <div class="modal-dialog modal-dialog-centered" style="max-width:71.75rem">
                <div class="modal-content">
                  <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                  <div class="modal-header flex-center flex-wrap gap-2 text-body-2 mb-4">
                    
                  </div>
                  <div class="table-responsive-md table-responsive-flush mb-4">
                    <div class="table-responsive-inner">
                      
                    </div>
                  </div>
                  <div class="row g-2 justify-content-center" id="btn-detail-examine">
                    
                  </div>
                </div>
              </div>
            </div>
            <div class="modal fade" id="modal-cf-save-menu" tabindex="-1" aria-hidden="true">
              <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                  <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                  <h4 class="modal-title text-center mb-2">Bạn muốn tạo mới thực đơn mẫu không ?</h4>
                  <p class="mb-5 fw-5 text-body-2 text-center">Nếu chọn không sẽ lưu vào thực đơn mẫu đang chọn.</p>
                  <div class="row g-2 justify-content-center">
                    <div class="col-6">
                      <button class="btn btn-cancel w-100 text-uppercase" type="button" onclick="saveMenu(0)" title="Lưu vào thực đơn mẫu đang chọn">
                        <svg class="iconsvg-close me-2">
                          <use xlink:href="/public/content/images/sprite.svg#close"></use>
                        </svg>Không
                      </button>
                    </div>
                    <div class="col-6">
                      <button class="btn btn-primary w-100 text-uppercase" type="button" onclick="saveMenu(1)" title="Tạo mới thực đơn mẫu">
                        <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                          <use xlink:href="/public/content/images/sprite.svg#send-2"></use>
                        </svg>Có
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <script>
              $(document).ready(function() {
                dataExamine.page = "<%=page%>";
                dataExamine.id_examine = "<%=examine.id%>";
                dataExamine.nutritionAdviceList = <%-JSON.stringify(nutritionAdvice) %>;
                dataExamine.activeModeOfLivingList = <%-JSON.stringify(activeModeOfLiving) %>;
                dataExamine.prescription = <%-JSON.stringify(prescriptionExamine) %>;
                dataExamine.listMenuTime = <%-JSON.stringify(menuTime) %>;
                dataExamine.menuExamine = <%-JSON.stringify(menuExamine) %>;
                dataExamine.menuExample = <%-JSON.stringify(menuExample) %>;

                dataExamine.diagnostic = <%-JSON.stringify(diagnostic) %>;
                dataExamine.medicalTest = <%-JSON.stringify(medicalTestExamine) %>;
                // dataExamine.examine = <%-JSON.stringify(examine) %>;
                dataExamine.isDetail = "<%=isDetail%>";
                addPrescriptionEdit();
                generateFoodName("food_name");
                generateMenuExamine();
              });
              // Lưu tự động mỗi 5 phút
              setInterval(()=>{
                saveExamine(0, 1, {});
              }, 300000)
              // VirtualSelect.init({
              //   ele: '#food_name',
              //   placeholder: 'Chọn thực phẩm',
              //   options: [
              //     { label: 'Options 2', value: '2' },
              //   ],
              //   disableSelectAll: true,
              //   showValueAsTags: false,
              //   search:true,
              //   onServerSearch: onSampleSelectServerSearch
              // });
            </script>
            <% } %>
         </div>
          <%- include('../layout/footer', { footer_class: ""}) %>
      </div>
   </body>
</html>