<form id="thucdonmau_form" class="form-horizontal">
    <h3 class="box-title-break">Th<PERSON><PERSON> đ<PERSON>n mẫu</h3>
    <input type="hidden" class="form-control form-control-title p-1" id="energy_food" placeholder="Energy" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="protein_food" placeholder="Protein" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="animal_protein" placeholder="Animal Protein" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="lipid_food" placeholder="Lipid" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="unanimal_lipid" placeholder="Unanimal Lipid" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="carbohydrate" placeholder="Carbohydrate" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="status_examine" value="<%=examine.status%>">

    <div class="row g-2 align-items-center">
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="menuExample_id">Chọn mẫu</label>
                <div class="col-form-body col-md">
                    <div class="input-group">
                        <div style="flex-grow:1;">
                            <select class="form-select" id="menuExample_id" data-plugin="select2" data-placeholder="Chọn mẫu">
                                <option></option>
                                <% if(menuExample.length > 0){ %>
                                    <% for (let me of menuExample){ %>
                                        <option value="<%= me.id %>"><%= me.name_menu %></option>
                                    <%}%>   
                                <%}%>
                            </select>
                        </div>
                        <button title="Thêm thực đơn đã chọn" class="btn btn-danger" type="button" onclick="chooseMenuExample()">Thêm</button>
                        <button title="Lưu thực đơn mẫu" class="btn btn-primary" type="button" onclick="showConfirmSaveMenu()">Lưu mẫu</button>
                        <button title="Tạo mới thực đơn trống dữ liệu" class="btn btn-primary-light" type="button" onclick="addMenu()">Tạo mới</button>
                    </div>
                </div>
            </div>
        <% } %>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="menu_id">Chọn menu</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <select class="form-select" id="menu_id" data-plugin="select2" data-placeholder="Chọn menu"></select>
                </div>
            </div>
        </div>
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="menuTime_id">Chọn giờ ăn</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <select class="form-select" id="menuTime_id" data-plugin="select2" data-placeholder="Chọn giờ ăn">
                            <option></option>
                            <% if(menuTime.length > 0){ %>
                                <% for (let mt of menuTime){ %>
                                    <option value="<%= mt.id %>"><%= mt.name %></option>
                                <%}%>   
                            <%}%>
                        </select>
                    </div>
                </div>
            </div>
        <% } %>
    </div>
    <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
        <div class="row g-2 align-items-center">
            <div class="col-12 col-md-8 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="food_name">Thực phẩm</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <select class="form-select" id="food_name" data-plugin="select2" data-placeholder="Chọn thực phẩm">
                            
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-4 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="weight_food">Khối lượng</label>
                <div class="col-form-body col-md">
                <div class="input-group">
                    <input type="number" class="form-control form-control-title p-1" id="weight_food" placeholder="Khối lượng" data-initial-value="0">
                    <button class="btn btn-primary" type="button" onclick="addFoodToMenu()" title="Thêm thực phẩm vào thực đơn">Thêm</button>
                </div>
                </div>
            </div>
        </div>
    <% } %>
    <div class="row g-2 align-items-center">
      <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_menu" style="width: 100%;display: none;">
        <div class="table-responsive-inner"></div>
        <table class="table-2 table table-pe-x-3 mb-0 align-middle table-ds-booking table-bordered table-hover">
            <thead class="text-center">
                <tr>
                    <td rowspan="2"> 
                        <span>Giờ ăn</span>
                    </td>
                    <td colspan="9">
                        <input type="text" id="name_menu" <%=(examine.status == 4 || (examine.status == 3 && isDetail == true)) ? 'readonly' : '' %> class="form-control form-control-title p-1" style="text-align: center;">
                    </td>
                </tr>
                <tr>
                    <td>Thực phẩm</td>
                    <td>Weight(g)</td>
                    <td>Energy(kcal)</td>
                    <td>Protein(g)</td>
                    <td>Animal Protein(g)</td>
                    <td>Lipid(g)</td>
                    <td>Unanimal Lipid(g)</td>
                    <td>Carbohydrate(g)</td>
                    <td style="min-width: 40px;"></td>
                </tr>
            </thead>
            <tbody>
                
            </tbody>
            <tfoot>
                <tr>
                    <td></td>
                    <td colspan="2">Tổng số</td>
                    <td id="total_energy"></td>
                    <td id="total_protein"></td>
                    <td id="total_animal_protein"></td>
                    <td id="total_lipid"></td>
                    <td id="total_unanimal_lipid"></td>
                    <td id="total_carbohydrate"></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="3"></td>
                    <td>%P</td>
                    <td id="total_protein_percent"></td>
                    <td>%L</td>
                    <td id="total_lipid_percent"></td>
                    <td>%G</td>
                    <td id="total_carbohydrate_percent"></td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
      </div>
    </div>

    <div class="row g-2 align-items-center">
        <div class="col-12 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="menu_example_note">Ghi chú</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <textarea class="form-control" id="menu_example_note" placeholder="Ghi chú"></textarea>
                    <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#menu_example_note')">
                        <svg class="iconsvg-close">
                        <use xlink:href="/public/content/images/sprite.svg#close"></use>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-2 justify-content-center mt-0">
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-6 col-md-auto">
            <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="saveExamine(2, 0, {})" title="Lưu phiếu khám">
                <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                <use xlink:href="/public/content/images/sprite.svg#save"></use>
                </svg>Lưu 
            </button>
            </div>
        <% } %>
        <div class="col-6 col-md-auto">
            <button class="btn btn-action-edit box-btn w-100 text-uppercase" type="button" onclick="exportMenuExample()" title="Tải thực đơn mẫu">
              <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                <use xlink:href="/public/content/images/sprite.svg#download"></use>
              </svg>Tải thực đơn
            </button>
        </div>
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-6 col-md-auto">
            <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" onclick="returnList()" title="Hủy thao tác sửa phiếu khám">
                <svg class="iconsvg-close flex-shrink-0 fs-16px me-2">
                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                </svg>Huỷ
            </button>
            </div>
        <% } %>
      </div>
</form>
