<form id="thucdonmau_form" class="form-horizontal">
    <h3 class="box-title-break">Th<PERSON><PERSON> đ<PERSON>n mẫu</h3>
    <input type="hidden" class="form-control form-control-title p-1" id="energy_food" placeholder="Energy" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="protein_food" placeholder="Protein" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="animal_protein" placeholder="Animal Protein" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="lipid_food" placeholder="Lipid" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="unanimal_lipid" placeholder="Unanimal Lipid" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="carbohydrate" placeholder="Carbohydrate" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="status_examine" value="<%=examine.status%>">

    <div class="row g-2 align-items-center">
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="menuExample_id">Chọn mẫu</label>
                <div class="col-form-body col-md">
                    <div class="input-group">
                        <div style="flex-grow:1;">
                            <div data-plugin="virtual-select" data-config='{"placeholder":"Chọn thực đơn mẫu"}' id="menuExample_id"
                                data-options='<%=JSON.stringify(menuExample.map(s => ({label: s.name_menu, value: s.id})))%>'></div>
                        </div>
                        <button title="Thêm thực đơn đã chọn" class="btn btn-danger" type="button" onclick="chooseMenuExample()">Thêm</button>
                    </div>
                </div>
            </div>
        <% } %>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="menu_id">Thực đơn đã chọn</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <div id="menu_id"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Card chính cho thực đơn -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="text-primary" id="name_menu_text">Tên thực đơn</h6>
            <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                <a href="#" title="Lưu thực đơn" class="btn btn-success btn-circle btn-add-patient" onclick="showConfirmSaveMenu()">
                    <i class="fa-solid fa-floppy-disk"></i>
                </a>
            <% } %>
        </div>
        <div class="card-body px-0">
            <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                <!-- Card thêm thực phẩm -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="text-primary mb-0">Thêm thực phẩm</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2 align-items-center mb-4">
                            <div class="col-12 col-md-6 d-flex">
                                <label class="col-form-label col-md-auto fw-bold" for="menuTime_id">Chọn giờ ăn</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <div id="menuTime_id" data-plugin="virtual-select" data-config='{"placeholder":"Chọn giờ ăn"}'
                                            data-options='<%=JSON.stringify(menuTime.map(s => ({label: s.name, value: s.id})))%>'></div>
                                    </div>
                                </div>
                            </div>
                            <!-- Filter cho thực phẩm -->
                            <div class="col-12 col-md-3 d-flex">
                                <label class="col-form-label col-md-auto fw-bold" for="food_type">Loại thực phẩm</label>
                                <div class="col-form-body col-md w-100">
                                    <select id="food_type" name="food_type" class="form-control" onchange="updateFoodDropdown('food_name')">
                                        <option value="">Tất cả</option>
                                        <option value="raw">Sống</option>
                                        <option value="cooked">Chín ĐP</option>
                                        <option value="cooked_vdd">Chín VDD</option>
                                        <option value="milk">Sữa</option>
                                        <option value="ddd">Dịch DD</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-md-3 d-flex">
                                <label class="col-form-label col-md-auto fw-bold" for="food_year">Năm dữ liệu</label>
                                <div class="col-form-body col-md w-100">
                                    <select id="food_year" name="food_year" class="form-control" onchange="updateFoodDropdown('food_name')">
                                        <option value="">Tất cả</option>
                                        <option value="2000">2000</option>
                                        <option value="2017">2017</option>
                                        <option value="2025">2025</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row g-2 align-items-center">
                            <div class="col-12 col-md-8 d-flex align-items-center">
                                <label class="col-form-label col-md-auto fw-bold mt-2" for="food_name">Thực phẩm</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <div id="food_name"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-4">
                                <div class="input-group">
                                    <input type="number" class="form-control form-control-title p-1"
                                        id="weight_food" placeholder="Khối lượng"
                                        data-initial-value="0">
                                    <button class="btn btn-primary" type="button"
                                        onclick="addFoodToMenu()"
                                        title="Thêm thực phẩm vào thực đơn">Thêm</button>
                                </div>
                            </div>
                        </div>
                        <div class="row g-2 align-items-center d-none" id="food_note_container">
                            <div class="col-12 col-md-12 ps-4" id="food_note"></div>
                        </div>
                    </div>
                </div>

                <!-- Card chọn món ăn -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="text-primary mb-0">Thêm món ăn vào thực đơn</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2 align-items-center">
                            <div class="col-12 col-md-6 d-flex">
                                <label class="col-form-label col-md-auto fw-bold mt-2" for="dish_menuTime_id">Chọn giờ ăn</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <div id="dish_menuTime_id" data-plugin="virtual-select" data-config='{"placeholder":"Chọn giờ ăn"}'
                                            data-options='<%=JSON.stringify(menuTime.map(s => ({label: s.name, value: s.id})))%>'></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row g-2 align-items-center">
                            <div class="col-12 col-md-8 d-flex align-items-center">
                                <label class="col-form-label col-md-auto fw-bold mt-2" for="dish_name">Món ăn</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <div id="dish_name"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-4">
                                <div class="input-group">
                                    <button class="btn btn-success" type="button"
                                        onclick="addDishToMenu()"
                                        title="Thêm món ăn vào thực đơn">Thêm món ăn</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <% } %>

            <!-- Bảng thực đơn chi tiết -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="text-primary mb-0">Thực đơn chi tiết</h6>
                </div>
                <div class="card-body px-0">
                    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_menu"
                            style="width: 100%;display: none;">
                            <div class="table-responsive-inner px-3">
                                <table
                                    class="table-2 table table-pe-x-3 mb-0 align-middle table-ds-booking table-bordered table-hover">
                                    <thead class="text-center">
                                        <tr>
                                            <td rowspan="2">
                                                <span>Giờ ăn</span>
                                            </td>
                                            <td colspan="9">
                                                <input type="text" id="name_menu"
                                                    class="form-control form-control-title p-1"
                                                    style="text-align: center;" <%=(examine.status == 4 || (examine.status == 3 && isDetail == true)) ? 'readonly' : '' %>>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Thực phẩm</td>
                                            <td>Weight(g)</td>
                                            <td>Energy(kcal)</td>
                                            <td>Protein(g)</td>
                                            <td>Animal Protein(g)</td>
                                            <td>Fat(g)</td>
                                            <td>Unanimal Lipid(g)</td>
                                            <td>Carbohydrate(g)</td>
                                            <td style="min-width: 40px;"></td>
                                        </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                        </div>

                    <div class="row g-2 align-items-center">
                        <div class="col-12 d-flex">
                            <label class="col-form-label col-md-auto fw-bold mt-2"
                                for="menu_example_note">Ghi chú</label>
                            <div class="col-form-body col-md w-100">
                                <div class="form-control-has-addon">
                                    <textarea class="form-control" id="menu_example_note"
                                        placeholder="Ghi chú" onchange="updateMenuNote()" <%=(examine.status == 4 || (examine.status == 3 && isDetail == true)) ? 'readonly' : '' %>></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                        <div class="d-flex gap-3 align-items-center justify-content-center mt-3">
                            <button class="btn btn-info" type="button" onclick="exportMenuExcel()">
                                <i class="fas fa-file-excel"></i>
                                Xuất Excel
                            </button>
                            <button class="btn btn-primary" type="button" onclick="saveMenuExampleFromMenu()">
                                <i class="fas fa-floppy-disk"></i>
                                Lưu mẫu
                            </button>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Modal xác nhận lưu menu -->
<div class="modal fade" id="modal-cf-save-menu" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal"
                aria-label="Close"></button>
            <h4 class="modal-title text-center mb-2">Bạn muốn lưu toàn bộ thực đơn mẫu không ?</h4>
            <div class="row g-2 justify-content-center">
                <div class="col-6">
                    <button class="btn btn-cancel w-100 text-uppercase" type="button" data-bs-dismiss="modal"
                        title="Lưu vào thực đơn mẫu đang chọn">Không
                    </button>
                </div>
                <div class="col-6">
                    <button class="btn btn-primary w-100 text-uppercase" type="button" onclick="saveMenu()"
                        title="Lưu toàn bộ thực đơn mẫu">Có
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modal_confirm_box">
</div>

<script src="/public/content/js/menuExample.js"></script>
<script>
    // Khởi tạo dữ liệu global
    window.menuExample = <%- JSON.stringify(menuExample) %>;
    window.menuTime = <%- JSON.stringify(menuTime) %>;
    window.menuExamine = [];
    window.listMenuTime = <%- JSON.stringify(menuTime) %>;
    window.currentExamineId = '<%= examine.id %>';
</script>
