<!DOCTYPE html>
<html class="no-js" lang="vi">
<head>
  <meta charset="utf-8">
  <title>Đăng nhập - <PERSON><PERSON> vấn dinh dưỡng</title>
  <meta name="author" content="QD">
  <meta name="description" content="Tư vấn dinh dưỡng">
  <meta name="keywords" content="Tư vấn dinh dưỡng, dinh dưỡng hỗ trợ">
  <meta name="viewport" content="width=device-width initial-scale=1">
  <link rel="shortcut icon" href="/public/content/images/favicon.ico" type="image/x-icon">
  <link type="text/css" rel="stylesheet" href="/public/content/css/style.min.css">
  <link type="text/css" rel="stylesheet" href="/public/content/css/style.css">

</head>
<body class="body-login">
  <div class="page page-dark page-login">
    <div class="page-main">
      <div class="login">
        <div id="login-content-page">
          <div class="tab login-body login-tab">
            <ul class="tab-nav nav mb-4">
              <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="#tab-login" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="true">Đăng nhập</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#tab-register" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="false">Đăng ký</a>
              </li>
            </ul>
              <div class="tab-content">
                <div class="tab-pane fade show active" id="tab-login">
                  <form class="form-mde" id="login-form" novalidate class="form-login" action="/user/login" method="POST">
                    <div class="mb-4">
                      <div class="form-floating">
                        <label class="form-control-user"></label>
                        <input maxlength="65" class="form-control form-control-icon-start" id="login_username" name="username" type="text" placeholder="Số điện thoại hoặc email" />
                        <label for="form-login-name">Phone / Email </label>
                      </div>
                      <span id="ercf_username" class="ver-middle form-text fst-italic text-danger mt-1"></span>
                    </div>
                    <div class="mb-4">
                      <div class="form-floating">
                        <label class="form-control-lock"></label>
                        <input type="password" class="form-control form-control-icon-start" id="login_password" name="password" placeholder="Mật khẩu" autocomplete="off" />
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="login_showPassword()">
                          <svg class="iconsvg-eye-off" id="show-password-1">
                            <use xlink:href="/public/content/images/sprite.svg#eye"></use>
                          </svg>
                        </button>
                        <label for="form-login-password">Mật khẩu </label>
                      </div>
                      <span id="ercf_password" class="ver-middle form-text fst-italic text-danger mt-1"></span>
                    </div>
  
                    <button class="btn login-btn btn-primary w-100 text-uppercase mb-4" type="submit">Đăng nhập</button>
                    <div class="text-end">
                      <a class="link-primary" href="">Quên mật khẩu?</a>
                    </div>
                  </form>
                </div>
                <div class="tab-pane fade" id="tab-register">
                  <form class="form-mde" id="signup-form" novalidate action="/user/signup" method="POST">
                    <div class="row gy-4 mb-4">
                      <div class="col-md-6">
                        <div class="form-floating form-control-wrapper">
                          <input class="form-control" id="email" name="email" type="text" placeholder="Email" required />
                          <span id="imgchkemail" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                          <label>
                            Email <span class="text-danger">*</span>
                          </label>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="form-floating">
                          <input class="form-control form-control-has-addon" id="phone" name="phone" type="text" placeholder="Số điện thoại" required />
                          <span id="imgchkphone" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                          <label>
                            Số điện thoại <span class="text-danger">*</span>
                          </label>
                        </div>
                      </div>
                      <div class="col-md-12 mt-2">
                        <div class="form-floating form-control-has-addon">
                          <input class="form-control form-control-has-addon" type="password" placeholder="Mật khẩu" id="password" name="password" required  autocomplete="off" />
                          <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="signup_showPassword()">
                            <svg class="iconsvg-eye" id="show-password-2">
                              <use xlink:href="/public/content/images/sprite.svg#eye"></use>
                            </svg>
                          </button>
                          <label>
                            Mật khẩu <span class="text-danger">*</span>
                          </label>
                        </div>
                        <div class="form-text fst-italic text-gray-4 mt-2">
                        Mật khẩu phải có chữ hoa, chữ thường, chữ số và tối thiểu 9 ký tự
                        </div>
                        <span id="imgchkpassword" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                      </div>
                      <div class="col-md-12 mt-2">
                        <div class="form-floating form-control-has-addon">
                          <input class="form-control form-control-has-addon" id="confirm_password" name="confirm_password" type="password" placeholder="Xác thực mật khẩu" required autocomplete="off" />
                          <span id="imgchkcf_password" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                          <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="signup_showConfirmPassword()">
                            <svg class="iconsvg-eye" id="show-password-3">
                              <use xlink:href="/public/content/images/sprite.svg#eye"></use>
                            </svg>
                          </button>
                          <label>Xác thực mật khẩu <span class="text-danger">*</span>
                          </label>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="form-floating">
                          <input class="form-control form-control-has-addon" id="full_name" name="full_name" type="text" placeholder="Họ tên" required />
                          <span id="imgchkfull_name" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                          <label>Họ tên</label>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="form-floating">
                          <input class="form-control form-control-has-addon" placeholder="Địa chỉ" id="address" name="address" type="text" required />
                          <span id="imgchkaddress" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                          <label>Địa chỉ </label>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="flatpickr form-floating">
                          <input autocomplete="off" class="form-control" type="text" id="birthday" name="birthday" placeholder="Ngày sinh"/>
                          <span id="imgchkbirthday" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                          <label class="form-label-floating">Ngày sinh</label>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div id="form-floating-gender">
                          <label class="col-md-auto fw-bold visually-hidden">Giới tính</label>
                          <select class="form-select" name="gender" id="gender" data-plugin="select2" data-placeholder="Giới tính" data-minimum-results-for-search="Infinity">
                            <option></option>
                            <option value="1">Nam</option>
                            <option value="0">Nữ</option>
                            <option value="0">Khác</option>
                          </select>
                          <span id="imgchkgender" class="ver-middle form-text fst-italic text-danger mt-2"></span>
                        </div>
                      </div>
                    </div>
  
                    <button class="btn login-btn btn-primary w-100 text-uppercase mb-4" onclick="signup()" type="button">
                      Đăng ký
                    </button>
                  </form>
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
    <%- include('../layout/footer', { footer_class: "footer-dark"}) %>
    <script type="text/javascript">
      let site_key = '<%=reCAPTCHA_site_key%>';
    </script>
    <script src="https://www.google.com/recaptcha/api.js?render=<%=reCAPTCHA_site_key%>"></script>
    <script src="/public/content/js/vendor/jquery.min.js"></script>
    <script src="/public/content/js/vendor/bootstrap.bundle.min.js"></script>
    <script src="/public/content/js/vendor/flatpickr.min.js"></script>
    <script src="/public/content/js/vendor/flatpickr.vn.js"></script>
    <script src="/public/content/js/vendor/select2.full.min.js"></script>
    <script src="/public/content/js/main.js"></script>
    <script src="/public/content/js/login.js"></script>

    <!-- Thêm toastr -->
    <link rel="stylesheet" href="/public/admin/css/toastr.min.css">
    <script src="/public/admin/js/toastr.min.js"></script>
    
    <!-- Script xử lý toast message -->
    <script>
        // Cấu hình toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": false,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "0",         // Đặt thành 0 để không tự động ẩn
            "extendedTimeOut": "0", // Đặt thành 0 để không tự động ẩn khi hover
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // Lấy parameters từ URL
        const urlParams = new URLSearchParams(window.location.search);
        const toastType = urlParams.get('toast');
        const message = urlParams.get('message');

        // Hiển thị toast message nếu có
        if (message) {
            switch(toastType) {
                case 'success':
                    toastr.success(message, 'Thông báo');
                    break;
                case 'error':
                    toastr.error(message, 'Lỗi');
                    break;
                case 'warning':
                    toastr.warning(message, 'Cảnh báo');
                    break;
                case 'info':
                    toastr.info(message, 'Thông tin');
                    break;
                default:
                    toastr.info(message, 'Thông báo');
                    break;
            }
            // Xóa parameters khỏi URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    </script>
  </div>
</body>
