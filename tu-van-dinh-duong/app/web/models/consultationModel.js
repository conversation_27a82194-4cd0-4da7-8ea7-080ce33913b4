var request         = require('request'),
    db              = require('./../../config/db'),
    moment          = require('moment'),
    webService      = require('../models/webModel'),
    idCount  = 1,
    dateCount = '';

let consultationService = {
    countAllConsultation: function(parameter, callback) {
        db.get().getConnection(function(err, connection) {
            if (err) return callback(err);
            var paraSQL = [];
            var search  = parameter.search;
            var sql     = `SELECT COUNT(*) AS count, hospital.id AS hospital_id FROM consultation
                            LEFT JOIN department ON consultation.department_id = department.id
                            LEFT JOIN hospital ON consultation.hospital_id = hospital.id WHERE consultation.active = 1`;
            
            if(parameter.filter){
                if (search.keyword !== "") {
                    sql += ` AND (consultation.cus_name like ? OR consultation.cus_phone like ? OR consultation.count_id = ?)`;
                    paraSQL.push("%" + search.keyword + "%");
                    paraSQL.push("%" + search.keyword + "%");
                    paraSQL.push(search.keyword);
                }
                if(search.id_count && search.id_count.length > 0){
                    sql += ` AND consultation.count_id LIKE ?`;
                    paraSQL.push("%" + search.id_count + "%");
                }
                if(search.cus_name && search.cus_name.length > 0){
                    sql += ` AND UPPER(consultation.cus_name) LIKE ?`;
                    paraSQL.push("%" + search.cus_name.toUpperCase() + "%");
                }
                if(search.cus_phone && search.cus_phone.length > 0){
                    sql += ` AND consultation.cus_phone LIKE ?`;
                    paraSQL.push("%" + search.cus_phone + "%");
                }
                if(search.cus_address && search.cus_address.length > 0){
                    sql += ` AND UPPER(consultation.cus_address) LIKE ?`;
                    paraSQL.push("%" + search.cus_address.toUpperCase() + "%");
                }
                if(search.diagnostic && search.diagnostic.length > 0){
                    sql += ` AND UPPER(consultation.diagnostic) LIKE ?`;
                    paraSQL.push("%" + search.diagnostic.toUpperCase() + "%");
                }
                if(search.hospital_ids !== ''){
                    sql += " AND consultation.hospital_id in (?)";
                    paraSQL.push(search.hospital_ids.split(','));
                }
                if (search.fromdate !== "" && search.todate !== "") {
                    sql += " AND consultation.created_at >= ? AND consultation.created_at <= ?";
                    paraSQL.push(search.fromdate.split("-").reverse().join("-"));
                    paraSQL.push(search.todate.split("-").reverse().join("-"));
                } else if (search.fromdate !== "") {
                    sql += " AND consultation.created_at >= ? AND consultation.created_at <= ?";
                    paraSQL.push(search.fromdate.split("-").reverse().join("-") + " 00:00:00");
                    paraSQL.push(search.fromdate.split("-").reverse().join("-") + " 23:59:59");
                }
            }
            //Không phải Administrator thì load các bản ghi theo khoa viện
            if(search.role_ids && search.role_ids.length == 0){
                if (search.name !== "" && search.phone !== "") {
                    sql += ` AND (consultation.cus_name like ? AND consultation.cus_phone like ?)`;
                    paraSQL.push("%" + search.name + "%");
                    paraSQL.push("%" + search.phone + "%");
                }
            }else{
                if (!search.role_ids.includes(1) && !search.role_ids.includes(3)){
                    //Nếu là quản lý load theo viện
                    if(search.role_ids.includes(5)){
                        sql += " AND consultation.hospital_id = ?";
                        paraSQL.push(search.hospital_id);
                    }else if(search.role_ids.includes(4)){
                        //Nếu là bác sĩ load theo khoa
                        sql += " AND consultation.created_by = ?";
                        paraSQL.push(search.created_by);
                    }else{
                        //Nếu là bệnh nhân load theo số điện thoại hoặc email
                        sql += " AND (consultation.cus_phone = ? OR consultation.cus_email = ?)";
                        paraSQL.push(search.user_phone);
                        paraSQL.push(search.user_mail);
                    }
                }
            }
            
            var query = connection.query(sql, paraSQL, function(err, results, fields) {
                connection.release();
                if (err) return callback(err);
                callback(null, results, fields);
            });
        });
    },
    getAllConsultation: function(parameter, callback) {
        db.get().getConnection(function(err, connection) {
            if (err) return callback(err);
            var paraSQL     = [];
            var search      = parameter.search;
            var order_by    = "consultation.created_at DESC";
            var sql         = `SELECT consultation.*, hospital.id AS hospital_id, hospital.name AS hospital_name FROM consultation
                                LEFT JOIN department ON consultation.department_id = department.id
                                LEFT JOIN hospital ON consultation.hospital_id = hospital.id WHERE consultation.active = 1`;

            let sortText = webService.getSortString(parameter.search.sort, 'consultation');
            if(sortText.length > 0) order_by = sortText;
            
            if(parameter.filter){
                if (search.keyword !== "") {
                    sql += ` AND (consultation.cus_name like ? OR consultation.cus_phone like ? OR consultation.count_id = ?)`;
                    paraSQL.push("%" + search.keyword + "%");
                    paraSQL.push("%" + search.keyword + "%");
                    paraSQL.push(search.keyword);
                }
                if(search.id_count && search.id_count.length > 0){
                    sql += ` AND UPPER(consultation.count_id) LIKE ?`;
                    paraSQL.push("%" + search.id_count.toUpperCase() + "%");
                }
                if(search.cus_name && search.cus_name.length > 0){
                    sql += ` AND UPPER(consultation.cus_name) LIKE ?`;
                    paraSQL.push("%" + search.cus_name.toUpperCase() + "%");
                }
                if(search.cus_phone && search.cus_phone.length > 0){
                    sql += ` AND consultation.cus_phone LIKE ?`;
                    paraSQL.push("%" + search.cus_phone + "%");
                }
                if(search.cus_address && search.cus_address.length > 0){
                    sql += ` AND UPPER(consultation.cus_address) LIKE ?`;
                    paraSQL.push("%" + search.cus_address.toUpperCase() + "%");
                }
                if(search.diagnostic && search.diagnostic.length > 0){
                    sql += ` AND UPPER(consultation.diagnostic) LIKE ?`;
                    paraSQL.push("%" + search.diagnostic.toUpperCase() + "%");
                }
                if(search.hospital_ids !== ''){
                    sql += " AND consultation.hospital_id in (?)";
                    paraSQL.push(search.hospital_ids.split(','));
                }
                if (search.fromdate !== "" && search.todate !== "") {
                    sql += " AND consultation.created_at >= ? AND consultation.created_at <= ?";
                    paraSQL.push(search.fromdate.split("-").reverse().join("-"));
                    paraSQL.push(search.todate.split("-").reverse().join("-"));
                } else if (search.fromdate !== "") {
                    sql += " AND consultation.created_at >= ? AND consultation.created_at <= ?";
                    paraSQL.push(search.fromdate.split("-").reverse().join("-") + " 00:00:00");
                    paraSQL.push(search.fromdate.split("-").reverse().join("-") + " 23:59:59");
                }
            }

            if(search.role_ids && search.role_ids.length == 0){
                if (search.name !== "" && search.phone !== "") {
                    sql += ` AND (consultation.cus_name like ? AND consultation.cus_phone like ?)`;
                    paraSQL.push("%" + search.name + "%");
                    paraSQL.push("%" + search.phone + "%");
                }
            }else{
                //Không phải Administrator thì load các bản ghi theo khoa viện
                if (!search.role_ids.includes(1) && !search.role_ids.includes(3)){
                    //Nếu là quản lý load theo viện
                    if(search.role_ids.includes(5)){
                        sql += " AND consultation.hospital_id = ?";
                        paraSQL.push(search.hospital_id);
                    }else if(search.role_ids.includes(4)){
                        //Nếu là bác sĩ load theo khoa
                        sql += " AND consultation.created_by = ?";
                        paraSQL.push(search.created_by);
                    }else{
                        //Nếu là bệnh nhân load theo số điện thoại hoặc email
                        sql += " AND (consultation.cus_phone = ? OR consultation.cus_email = ?)";
                        paraSQL.push(search.user_phone);
                        paraSQL.push(search.user_mail);
                    }
                }
            }
            
            sql += " ORDER BY "+ order_by +" LIMIT ?,?";
            paraSQL.push(search.skip);
            paraSQL.push(search.take);
            
            var query = connection.query(sql, paraSQL, function(err, results, fields) {
                connection.release();
                if (err) return callback(err);
                callback(null, results, fields);
            });
        });
    },
    getDetailConsultationById: function(id) {
        return new Promise((resolve, reject) => {
            db.get().getConnection(function(err, connection) {
                if (err) {
                    resolve({
                        success: false,
                        message: err
                    });
                }
                var sql = `SELECT consultation.*, hospital.id AS hospital_id, department.id AS department_id FROM consultation INNER JOIN department ON department.id = consultation.department_id INNER JOIN hospital ON hospital.id = department.hospital_id WHERE consultation.id = ? ORDER BY consultation.id DESC LIMIT 1`;

                connection.query(sql, [id], function(error, results, fields) {
                    connection.release();
                    if (error) {
                        resolve({
                            success: false,
                            message: error
                        });
                    }
                    resolve({
                        success: true,
                        data: results,
                        message: "Successful"
                    });
                });
            });
        });
    },
    getIdCountConsultation: function(){
        return {id: idCount, strDate: dateCount}
    },
    setIdCountConsultation: function(id, strDate){
        idCount = id;
        dateCount = strDate;
    },
    getIdCount: async function(prefix){
        try {
            let id = 1;
            let date = moment().format('MMYY');
            //lấy id
            let countConsultation = consultationService.getIdCountConsultation();
            // nếu ngày trùng tháng năm hiện tại thì lấy id
            if(countConsultation.strDate == date){
                id += countConsultation.id;
            }else{
                // nếu không trùng tháng năm hiện tại thì lấy trong db
                let sqlIdCount = 'SELECT count_id FROM consultation ORDER BY id DESC LIMIT 1';
                let data_id_consultation = await webService.getListTable(sqlIdCount, []);
                if(data_id_consultation.success && data_id_consultation.data && data_id_consultation.data.length > 0){
                    let id_consultation = data_id_consultation.data[0].count_id ? data_id_consultation.data[0].count_id : '';
                    if (id_consultation) {
                        //Check id cũ
                        let pattern = /[a-z|A-Z]/g;
                        if(pattern.test(id_consultation)){
                            //Nếu có chứa ký tự
                            let numberInId = id_consultation.replace(/[^0-9]/g, '');
                            let checkDate = numberInId.slice(-4);
                            if (checkDate == date) {
                                id = parseInt(numberInId.slice(0, numberInId.length - 4));
                                id += 1;
                            }
                        }
                    }
                }
            }
            //Set lại id count
            consultationService.setIdCountConsultation(id, date);
            return (String(id).padStart(3, '0') + date + prefix);
        } catch (error) {
            console.log('getIdCount', error);
        }
    }
}

module.exports = consultationService