var express         = require('express'),
    router          = express.Router(),
    url             = require('url'),
    path            = require('path'),
    moment          = require('moment'),
    logService      = require('../../admin/models/logModel'),
    webService      = require('./../models/webModel'),
    consultationService  = require('./../models/consultationModel');

router.get('/', function(req, res) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        webService.createSideBarFilter(req, 4).then(function(filter){
            var str_errors  = filter.error,
                arrPromise  = [],
                listConsultation = [],
                paginator   = {
                    perPage: 0,
                    page: 0,
                    totalItem: 0,
                    totalPage: 0,
                    hasPrevPage: false,
                    hasNextPage: false,
                    nextPage: '',
                    prevPage: '',
                    currentPage: '',
                };

            arrPromise.push(new Promise(function (resolve, reject) {
                consultationService.countAllConsultation({search: filter.search, filter: true}, function (err, result, fields) {
                    if (err) {
                        return logService.create(req, err).then(function(responseData){
                            if(responseData.message) str_errors.push(responseData.message);
                            else str_errors.push(err.sqlMessage);
                            resolve();
                        });
                    }
                    if (result !== undefined) {
                        paginator.totalItem = result[0].count;
                    }
                    resolve();
                });
            }));

            arrPromise.push(new Promise(function (resolve, reject) {
                consultationService.getAllConsultation({search: filter.search, filter: true}, function (err, result, fields) {
                    if (err) {
                        return logService.create(req, err).then(function(responseData){
                            if(responseData.message) str_errors.push(responseData.message);
                            else str_errors.push(err.sqlMessage);
                            resolve();
                        });
                    }
                    if (result !== undefined) {
                        listConsultation = result;
                    }
                    resolve();
                });
            }));

            return new Promise(function (resolve, reject) {
                Promise.all(arrPromise).then(function () {
                    paginator.page        = filter.search.page;
                    paginator.perPage     = filter.search.take;
                    paginator.currentPage = filter.requestUri;
                    paginator.totalPage   = Math.ceil(paginator.totalItem / paginator.perPage);
                    if(paginator.totalPage > paginator.page){
                        paginator.hasNextPage = true;
                        paginator.nextPage    = filter.requestUri + '&page=' + (paginator.page + 1);
                    }
                    if(paginator.page >= 2){
                        paginator.hasPrevPage = true;
                        paginator.prevPage    = filter.requestUri + '&page=' + (paginator.page - 1);
                    }
                    res.render('consultation/index.ejs', { 
                        user: req.user,
                        errors: str_errors,
                        listConsultation: listConsultation,
                        moment: moment,
                        webService: webService,
                        filter: filter,
                        paginatorConsultation: paginator,
                        link:'consultation'
                    });
                }).catch(err => {
                    res.render("consultation/index.ejs", {
                        user: req.user,
                        errors: [err],
                        filter: filter,
                        link:'consultation',
                        paginatorConsultation: paginator
                    });
                });
            });
        });
    } catch (e) {
        logService.create(req, e.message);
        webService.createSideBarFilter(req, 4).then(function(dataFilter) {
            res.render("consultation/index.ejs", {
                user: req.user,
                errors: [e.message],
                filter: dataFilter,
                link: 'consultation',
                paginatorConsultation: paginator
            });
        })
    }
});

router.get('/edit/:id', function(req, res, next) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        var arrPromise       = [],
            str_errors       = [],
            resultData       = {
                filter: [],
                detailConsultation: {}
            },
            analysis = [],
            medicineType = [],
            isDetail = req.query.detail && req.query.detail == 'true' ? true : false;

        arrPromise.push(webService.createSideBarFilter(req, 4).then(function(dataFilter) {
            resultData.filter = dataFilter;
            if (resultData.filter.error.length > 0) {
                str_errors = str_errors.concat(resultData.filter.error);
            }
        }));

        let sqlSubclinical = 'SELECT id, `name`, reference_value, `max`, `min` FROM subclinical WHERE id > 0';
        let sqlSubclinicalPermit = webService.addPermitTable(sqlSubclinical, req.user);
        arrPromise.push(
            webService.getListTable(sqlSubclinicalPermit.sqlQuery, sqlSubclinicalPermit.paramSql).then(responseData =>{
                if(responseData.success){
                    if(responseData.data && responseData.data.length > 0) analysis = responseData.data;
                }
            })
        )
      
        // Phân loại thuốc
        let sqlmedicineTypeList = 'SELECT id, `name` FROM medicine_type WHERE id > 0';
        let sqlmedicineTypePermit = webService.addPermitTable(sqlmedicineTypeList, req.user);
        arrPromise.push(webService.getListTable(sqlmedicineTypePermit.sqlQuery, sqlmedicineTypePermit.paramSql).then(responseData6 =>{
            if(responseData6.success){
                medicineType = responseData6.data;
            }else{
                str_errors.push(responseData6.message);
            }
        }));

        // chi tiết phiếu khám
        arrPromise.push(consultationService.getDetailConsultationById(req.params.id).then(function(detailConsultation) {
            if (detailConsultation.success) {
                if(detailConsultation.data.length == 0){
                    str_errors.push("Không tìm thấy thông tin phiếu khám có mã #" + req.params.id);
                }else{
                    if (!req.user.role_id.includes(1) && !req.user.role_id.includes(3)){
                        //Nếu là quản lý xem toàn viện
                        if(req.user.role_id.includes(5) && req.user.hospital_id !== detailConsultation.data[0].hospital_id){
                            str_errors.push("Bạn không có quyền truy cập thông tin phiếu #" + req.params.id);
                        }else if(req.user.role_id.includes(4) && req.user.department_id !== detailConsultation.data[0].department_id){
                            //Nếu là bác sĩ xem theo khoa
                            str_errors.push("Bạn không có quyền truy cập thông tin phiếu #" + req.params.id);
                        }else if(req.user.role_id.includes(2) && !(req.user.phone == detailConsultation.data[0].cus_phone || req.user.email == detailConsultation.data[0].cus_email)){
                            //Nếu là bệnh nhân xem theo số điện thoại hoặc email
                            str_errors.push("Bạn không có quyền truy cập thông tin phiếu #" + req.params.id);
                        }
                    }
                    resultData.detailConsultation = detailConsultation.data[0];
                }
            }else{
                str_errors = str_errors.push(detailConsultation.message);
            }
        }));
        
        return new Promise(function(resolve, reject) {
            Promise.all(arrPromise).then(function() {
                res.render("consultation/create.ejs", {
                    moment: moment,
                    user: req.user,
                    errors: str_errors,
                    filter: resultData.filter,
                    consultation: resultData.detailConsultation,
                    clinicalExam: JSON.parse(resultData.detailConsultation.clinical_exam ? resultData.detailConsultation.clinical_exam : '[]'),
                    nutritionTracking: JSON.parse(resultData.detailConsultation.nutrition_tracking ? resultData.detailConsultation.nutrition_tracking : '[]'),
                    subclinical: resultData.detailConsultation.subclinical ? JSON.parse(resultData.detailConsultation.subclinical) : {data:[], date: []},
                    subclinicalOrther: JSON.parse(resultData.detailConsultation.subclinical_orther ? resultData.detailConsultation.subclinical_orther : '[]'),
                    medicine: resultData.detailConsultation.medicine ? JSON.parse(resultData.detailConsultation.medicine) : {data:[], date: []},
                    additional: JSON.parse(resultData.detailConsultation.additional ? resultData.detailConsultation.additional : '[]'),
                    medicineType: medicineType,
                    isDetail: isDetail,
                    page:'edit',
                    link:'consultation-page',
                    analysis: analysis
                });
            });
        });
    } catch (e) {
        logService.create(req, e.message);
        webService.createSideBarFilter(req, 4).then(function(dataFilter) {
            res.render("consultation/create.ejs", {
                user: req.user,
                errors: [e.message],
                filter: dataFilter,
                consultation: {},
                clinicalExam:[],
                nutritionTracking: [],
                subclinical: {data:[], date: []},
                subclinicalOrther: [],
                medicine: {data:[], date: []},
                additional: [],
                medicineType: medicineType,
                analysis: analysis,
                isDetail: false,
                page:'edit',
                link:'consultation-page'
            });
        })
    }
});

router.get('/create', function(req, res, next) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        var str_errors = [],
            arrPromise = [],
            resultData = {
                filter: [],
                analysis: [],
                medicineType: []
            };

        arrPromise.push(webService.createSideBarFilter(req, 4).then(function(dataFilter) {
            resultData.filter = dataFilter;
            if (resultData.filter.error.length > 0) {
                str_errors = str_errors.concat(resultData.filter.error);
            }
        }));

        let sqlSubclinical = 'SELECT id, `name`, reference_value, `max`, `min` FROM subclinical WHERE id > 0';
        // let sqlSubclinicalPermit = webService.addPermitTable(sqlSubclinical, req.user);
        arrPromise.push(
            webService.getListTable(sqlSubclinical, []).then(responseData =>{
            // webService.getListTable(sqlSubclinicalPermit.sqlQuery, sqlSubclinicalPermit.paramSql).then(responseData =>{
                if(responseData.success){
                    if(responseData.data && responseData.data.length > 0) resultData.analysis = responseData.data;
                }
            })
        )

         // Phân loại thuốc
         let sqlmedicineTypeList = 'SELECT id, `name` FROM medicine_type WHERE id > 0';
         let sqlmedicineTypePermit = webService.addPermitTable(sqlmedicineTypeList, req.user);
         arrPromise.push(webService.getListTable(sqlmedicineTypePermit.sqlQuery, sqlmedicineTypePermit.paramSql).then(responseData6 =>{
             if(responseData6.success){
                resultData.medicineType = responseData6.data;
             }else{
                 str_errors.push(responseData6.message);
             }
         }));
         
        return Promise.all(arrPromise).then(function(){
            res.render("consultation/create.ejs", {
                user: req.user,
                errors: str_errors,
                filter: resultData.filter,
                moment: moment,
                page:'create',
                clinicalExam:[],
                nutritionTracking: [],
                subclinical: {data:[], date: []},
                subclinicalOrther: [],
                medicine: {data:[], date: []},
                additional: [],
                consultation:{cus_birthday: moment().subtract(6, 'years')},
                isDetail: false,
                link:'consultation-create',
                analysis: resultData.analysis,
                medicineType: resultData.medicineType
            });
        });
    } catch (e) {
        logService.create(req, e.message);
        webService.createSideBarFilter(req, 4).then(function(dataFilter) {
            res.render("consultation/create.ejs", {
                user: req.user,
                errors: [e.message],
                filter: dataFilter,
                page:'create',
                clinicalExam:[],
                nutritionTracking: [],
                subclinical: {data:[], date: []},
                subclinicalOrther: [],
                medicine: {data:[], date: []},
                additional: [],
                analysis: resultData.analysis,
                medicineType: resultData.medicineType,
                consultation:{},
                isDetail: false,
                link:'consultation-create',
            });
        })
    }
});

router.post('/create', async function(req, res, next) {
    var resultData = {
        success: false,
        message: "",
        data: ''
    };
    try {
        if (!req.user) {
            resultData.message = "Vui lòng đăng nhập lại để thực hiện chức năng này!";
            res.json(resultData);
            return;
        }
        var str_errors   = [],
            parameter    = {
                department_id:          req.user.department_id,
                hospital_id:            req.user.hospital_id,
                created_by:             req.user.id,
                reportdate:             new Date().toLocaleDateString('fr-CA')
            },
            id_consultation = '';
        
        if(req.body.cus_name)               parameter['cus_name']               = req.body.cus_name;
        if(req.body.cus_phone)              parameter['cus_phone']              = req.body.cus_phone;
        if(req.body.cus_email)              parameter['cus_email']              = req.body.cus_email;
        if(req.body.cus_gender)             parameter['cus_gender']             = req.body.cus_gender;
        if(req.body.cus_birthday)           parameter['cus_birthday']           = req.body.cus_birthday;
        if(req.body.cus_address)            parameter['cus_address']            = req.body.cus_address;
        if(req.body.cus_anamnesis)          parameter['cus_anamnesis']          = req.body.cus_anamnesis;
        if(req.body.cus_living_habits)      parameter['cus_living_habits']      = req.body.cus_living_habits;
        if(req.body.diagnostic)             parameter['diagnostic']             = req.body.diagnostic;
        if(req.body.cus_length)             parameter['cus_length']             = req.body.cus_length;
        if(req.body.cus_cctc)               parameter['cus_cctc']               = req.body.cus_cctc;
        if(req.body.cus_cntc)               parameter['cus_cntc']               = req.body.cus_cntc;
        if(req.body.cus_cnht)               parameter['cus_cnht']               = req.body.cus_cnht;
        if(req.body.cus_cnbt)               parameter['cus_cnbt']               = req.body.cus_cnbt;
        if(req.body.cus_bmi)                parameter['cus_bmi']                = req.body.cus_bmi;
        if(req.body.cus_ncdd)               parameter['cus_ncdd']               = req.body.cus_ncdd;
        if(req.body.cus_height_by_age)      parameter['cus_height_by_age']      = req.body.cus_height_by_age;
        if(req.body.cus_weight_by_age)      parameter['cus_weight_by_age']      = req.body.cus_weight_by_age;
        if(req.body.cus_bmi_by_age)         parameter['cus_bmi_by_age']         = req.body.cus_bmi_by_age;
        if(req.body.cus_height_by_weight)   parameter['cus_height_by_weight']   = req.body.cus_height_by_weight;
        if(req.body.clinical_exam)          parameter['clinical_exam']          = req.body.clinical_exam;
        if(req.body.nutrition_tracking)     parameter['nutrition_tracking']     = req.body.nutrition_tracking;
        if(req.body.subclinical)            parameter['subclinical']            = req.body.subclinical;
        if(req.body.subclinical_orther)     parameter['subclinical_orther']     = req.body.subclinical_orther;
        if(req.body.medicine)               parameter['medicine']               = req.body.medicine;
        if(req.body.additional)             parameter['additional']             = req.body.additional;

        if(!parameter.cus_name){
            str_errors.push("Thiếu họ tên!");
        }
        if(!parameter.cus_gender){
            str_errors.push("Thiếu giới tính!");
        }
        if(!parameter.cus_birthday){
            str_errors.push("Thiếu ngày sinh!");
        }
        if(!parameter.cus_phone){
            str_errors.push("Thiếu số điện thoại!");
        }
        if (str_errors.length > 0) {
            resultData.message = str_errors.toString();
            res.json(resultData);
            return;
        } else {
            parameter.cus_birthday = parameter.cus_birthday.split("-").reverse().join("-");
            parameter['count_id'] = await consultationService.getIdCount(req.user.hospital_prefix);
            webService.addRecordTable( parameter, 'consultation', true).then(responseData =>{
                if(!responseData.success){
                    resultData.message = responseData.message;
                    res.json(resultData);
                    logService.create(req, responseData.message);
                    return;
                }else{
                    id_consultation = responseData.data.insertId;
                    resultData['id_consultation'] = id_consultation;
                    resultData.success = true;
                    resultData.message = "Lưu phiếu khám thành công!";
                    res.json(resultData);
                }
            });
            
            // Them khach hang vao database
            let paramCustomer = {
                cus_name:      parameter.cus_name,
                cus_phone:     parameter.cus_phone,
                cus_email:     parameter.cus_email,
                cus_gender:    parameter.cus_gender,
                cus_birthday:  parameter.cus_birthday,
                cus_address:   parameter.cus_address,
                department_id: parameter.department_id,
                hospital_id: parameter.hospital_id
            };

            let sqlFindCustomer = 'SELECT * FROM customer WHERE cus_phone = ? AND cus_gender = ? AND cus_birthday = ?';
            webService.getListTable(sqlFindCustomer ,[parameter.cus_phone, parameter.cus_gender, parameter.cus_birthday]).then(responseData1 =>{
                if(responseData1.success){
                    if(responseData1.data && responseData1.data.length > 0){
                        let customerData = responseData1.data[0];
                        if(paramCustomer.cus_name !== customerData.cus_name){
                            // Cap nhat lai thong tin khach hang neu thay doi thong tin
                            webService.updateRecordTable(paramCustomer, {id: customerData.id}, 'customer').then(responseData2 => {
                                if(!responseData2.success){
                                    logService.create(req, responseData2.message);
                                }
                            });
                            // cập nhật id customer
                            webService.updateRecordTable({customer_id: customerData.id}, {id: id_consultation}, 'consultation').then(responseData3 => {
                                if(!responseData3.success){
                                    logService.create(req, responseData3.message);
                                }
                            });
                        }
                    }else{
                        // neu khong co khach hang thi them moi
                        webService.addRecordTable( paramCustomer, 'customer', true).then(responseData4 =>{
                            if(!responseData4.success){
                                logService.create(req, responseData4.message);
                            }else{
                                // cập nhật id customer
                                webService.updateRecordTable({customer_id: responseData4.data.insertId}, {id: id_consultation}, 'consultation').then(responseData5 => {
                                    if(!responseData5.success){
                                        logService.create(req, responseData5.message);
                                    }
                                });
                            }
                        })
                    }
                }
            });
        }
    } catch (e) {
        logService.create(req, e.message).then(function() {
            resultData.message = e.message;
            res.json(resultData);
        });
    }
});

router.post('/edit/:id', function(req, res, next) {
    var resultData = {
        success: false,
        message: "",
        data: ''
    };
    try {
        if (!req.user) {
            resultData.message = "Vui lòng đăng nhập lại để thực hiện chức năng này!";
            res.json(resultData);
            return;
        }
        var str_errors   = [],
            parameter    = {};

            if(req.body.cus_name)               parameter['cus_name']               = req.body.cus_name;
            if(req.body.cus_phone)              parameter['cus_phone']              = req.body.cus_phone;
            if(req.body.cus_email)              parameter['cus_email']              = req.body.cus_email;
            if(req.body.cus_gender)             parameter['cus_gender']             = req.body.cus_gender;
            if(req.body.cus_birthday)           parameter['cus_birthday']           = req.body.cus_birthday;
            if(req.body.cus_address)            parameter['cus_address']            = req.body.cus_address;
            if(req.body.cus_anamnesis)          parameter['cus_anamnesis']          = req.body.cus_anamnesis;
            if(req.body.cus_living_habits)      parameter['cus_living_habits']      = req.body.cus_living_habits;
            if(req.body.diagnostic)             parameter['diagnostic']             = req.body.diagnostic;
            if(req.body.cus_length)             parameter['cus_length']             = req.body.cus_length;
            if(req.body.cus_cctc)               parameter['cus_cctc']               = req.body.cus_cctc;
            if(req.body.cus_cntc)               parameter['cus_cntc']               = req.body.cus_cntc;
            if(req.body.cus_cnht)               parameter['cus_cnht']               = req.body.cus_cnht;
            if(req.body.cus_cnbt)               parameter['cus_cnbt']               = req.body.cus_cnbt;
            if(req.body.cus_bmi)                parameter['cus_bmi']                = req.body.cus_bmi;
            if(req.body.cus_ncdd)               parameter['cus_ncdd']               = req.body.cus_ncdd;
            if(req.body.cus_height_by_age)      parameter['cus_height_by_age']      = req.body.cus_height_by_age;
            if(req.body.cus_weight_by_age)      parameter['cus_weight_by_age']      = req.body.cus_weight_by_age;
            if(req.body.cus_bmi_by_age)         parameter['cus_bmi_by_age']         = req.body.cus_bmi_by_age;
            if(req.body.cus_height_by_weight)   parameter['cus_height_by_weight']   = req.body.cus_height_by_weight;
            if(req.body.clinical_exam)          parameter['clinical_exam']          = req.body.clinical_exam;
            if(req.body.nutrition_tracking)     parameter['nutrition_tracking']     = req.body.nutrition_tracking;
            if(req.body.subclinical)            parameter['subclinical']            = req.body.subclinical;
            if(req.body.subclinical_orther)     parameter['subclinical_orther']     = req.body.subclinical_orther;
            if(req.body.medicine)               parameter['medicine']               = req.body.medicine;
            if(req.body.additional)             parameter['additional']             = req.body.additional;

        if(!parameter.cus_name){
            str_errors.push("Thiếu họ tên!");
        }
        if(!parameter.cus_gender){
            str_errors.push("Thiếu giới tính!");
        }
        if(!parameter.cus_birthday){
            str_errors.push("Thiếu ngày sinh!");
        }
        if(!parameter.cus_phone){
            str_errors.push("Thiếu số điện thoại!");
        }
        if (str_errors.length > 0) {
            resultData.message = str_errors.toString();
            res.json(resultData);
            return;
        } else {
            parameter.cus_birthday = parameter.cus_birthday.split("-").reverse().join("-");

            webService.updateRecordTable( parameter, {id:req.params.id},'consultation').then(responseData =>{
                if(!responseData.success){
                    resultData.message = responseData.message;
                    logService.create(req, responseData.message);
                    res.json(resultData);
                    return;
                }else{
                    resultData.success = true;
                    resultData.message = "Lưu phiếu khám thành công!";
                    res.json(resultData);
                }
            });
            // Them khach hang vao database
            let paramCustomer = {
                cus_name:      parameter.cus_name,
                cus_phone:     parameter.cus_phone,
                cus_email:     parameter.cus_email,
                cus_gender:    parameter.cus_gender,
                cus_birthday:  parameter.cus_birthday,
                cus_address:   parameter.cus_address,
                department_id: parameter.department_id,
                hospital_id: parameter.hospital_id
            };

            let sqlFindCustomer = 'SELECT * FROM customer WHERE cus_phone = ? AND cus_gender = ? AND cus_birthday = ?';
            webService.getListTable(sqlFindCustomer ,[parameter.cus_phone, parameter.cus_gender, parameter.cus_birthday]).then(responseData1 =>{
                if(responseData1.success){
                    if(responseData1.data && responseData1.data.length > 0){
                        let customerData = responseData1.data[0];
                        if(paramCustomer.cus_name !== customerData.cus_name){
                            // Cap nhat lai thong tin khach hang neu thay doi thong tin
                            webService.updateRecordTable(paramCustomer, {id: customerData.id}, 'customer').then(responseData2 => {
                                if(!responseData2.success){
                                    logService.create(req, responseData2.message);
                                }
                            });
                            // cập nhật id customer
                            webService.updateRecordTable({customer_id: customerData.id}, {id: req.params.id}, 'consultation').then(responseData3 => {
                                if(!responseData3.success){
                                    logService.create(req, responseData3.message);
                                }
                            });
                        }
                    }else{
                        // neu khong co khach hang thi them moi
                        webService.addRecordTable( paramCustomer, 'customer', true).then(responseData4 =>{
                            if(!responseData4.success){
                                logService.create(req, responseData4.message);
                            }else{
                                // cập nhật id customer
                                webService.updateRecordTable({customer_id: responseData4.data.insertId}, {id: req.params.id}, 'consultation').then(responseData5 => {
                                    if(!responseData5.success){
                                        logService.create(req, responseData5.message);
                                    }
                                });
                            }
                        })
                    }
                }
            });
            return;
        }
    } catch (e) {
        logService.create(req, e.message).then(function() {
            resultData.message = e.message;
            res.json(resultData);
        });
    }
});

router.post('/cancel', async function(req, res, next){
    var resultData = {
        success: false,
        message: ''
    };
    try {  
        if (!req.user) {
            resultData.message = "Vui lòng đăng nhập lại để thực hiện chức năng này!";
            res.json(resultData);
            return;
        }
        let id      = req.body.id;
        if(!id){
            resultData.message = "Thiếu id phiếu hội chẩn!";
            res.json(resultData);
            return;
        }else{
            let responseData = await webService.updateRecordTable({active: 0}, {id: id}, 'consultation');
            // data: OkPacket {
            //     fieldCount: 0,
            //     affectedRows: 1,
            //     insertId: 0,
            //     serverStatus: 2,
            //     warningCount: 0,
            //     message: '(Rows matched: 1  Changed: 1  Warnings: 0',
            //     protocol41: true,
            //     changedRows: 1
            // }
            if(responseData.success && responseData.data && responseData.data.changedRows == 1){
                resultData.message = 'Thành công!';
                resultData.success = true;
            }else{
                resultData.message = responseData.message;
            }
            res.json(resultData);
        }
    } catch (error) {
        logService.create(req, error.message).then(responseData =>{
            resultData.message = error.message;
            res.json(resultData);
        });
    }
});

router.get('/list-html', (req, res, next) =>{
    var resultData = {
        success: false,
        message: "",
        table_html: '',
        paginator_html: '',
        totalItem: 0,
        log_type: "consultationController-list-html"
    };
    try {
        webService.createSideBarFilter(req, 4).then(function(filter){
            var str_errors  = filter.error,
                arrPromise  = [],
                listConsultation = [],
                paginator   = {
                    perPage: 0,
                    page: 0,
                    totalItem: 0,
                    totalPage: 0,
                    hasPrevPage: false,
                    hasNextPage: false,
                    nextPage: '',
                    prevPage: '',
                    currentPage: '',
                };

            arrPromise.push(new Promise(function (resolve, reject) {
                consultationService.countAllConsultation({search: filter.search, filter: true}, function (err, result, fields) {
                    if (err) {
                        return logService.create(req, err).then(function(responseData){
                            if(responseData.message) str_errors.push(responseData.message);
                            else str_errors.push(err.sqlMessage);
                            resolve();
                        });
                    }
                    if (result !== undefined) {
                        paginator.totalItem = result[0].count;
                    }
                    resolve();
                });
            }));

            arrPromise.push(new Promise(function (resolve, reject) {
                consultationService.getAllConsultation({search: filter.search, filter: true}, function (err, result, fields) {
                    if (err) {
                        return logService.create(req, err).then(function(responseData){
                            if(responseData.message) str_errors.push(responseData.message);
                            else str_errors.push(err.sqlMessage);
                            resolve();
                        });
                    }
                    if (result !== undefined) {
                        listConsultation = result;
                    }
                    resolve();
                });
            }));

            return new Promise(function (resolve, reject) {
                Promise.all(arrPromise).then(function () {
                    paginator.page        = filter.search.page;
                    paginator.perPage     = filter.search.take;
                    paginator.currentPage = filter.requestUri;
                    paginator.totalPage   = Math.ceil(paginator.totalItem / paginator.perPage);
                    if(paginator.totalPage > paginator.page){
                        paginator.hasNextPage = true;
                        paginator.nextPage    = filter.requestUri + '&page=' + (paginator.page + 1);
                    }
                    if(paginator.page >= 2){
                        paginator.hasPrevPage = true;
                        paginator.prevPage    = filter.requestUri + '&page=' + (paginator.page - 1);
                    }
                    express().render(path.resolve(__dirname, "../views/component/table_body_list_consultation.ejs"), { 
                        user: req.user,
                        listConsultation: listConsultation,
                        moment: moment,
                        webService: webService,
                        paginatorConsultation: paginator
                    }, (err, html) => {
                        if(err){
                            resultData.message = 'Lỗi lấy dữ liệu danh sách bài!';
                        }else{
                            resultData.message = 'Success!';
                            resultData.success = true;
                            resultData.table_html = html;
                        }
                    });
                    express().render(path.resolve(__dirname, "../views/component/paginator_consultation.ejs"), {paginatorConsultation: paginator},(err, html) => {
                        if(err){
                            resultData.message = 'Lỗi lấy dữ liệu phân trang bài!';
                        }else{
                            resultData.message = 'Success!';
                            resultData.paginator_html = html;
                        }
                    });
                    resultData.totalItem = paginator.totalItem;
                    res.json(resultData);
                })
            });

        });
    } catch (error) {
        logService.create(req, error.message).then(function() {
            resultData.message = error.message;
            res.json(resultData);
        });
    }
});

module.exports = router;