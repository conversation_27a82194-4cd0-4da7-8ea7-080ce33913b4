const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const db = require('../config/db');
const webService = require('../web/models/webModel');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const TOKEN_EXPIRY = '24h';

let jwtService = {
    // Tạo JWT token cho user
    createToken: function(userId, userData) {
        try {
            const tokenId = crypto.randomBytes(32).toString('hex');
            const payload = {
                userId: userId,
                tokenId: tokenId,
                email: userData.email,
                name: userData.name,
                full_name: userData.full_name,
                role_id: userData.role_id || [],
                isAdmin: userData.isAdmin || false,
                iat: Math.floor(Date.now() / 1000)
                // Không set exp ở đây, để JWT tự động set
            };

            const token = jwt.sign(payload, JWT_SECRET, {
                algorithm: 'HS256',
                expiresIn: TOKEN_EXPIRY
            });

            return {
                token: token,
                tokenId: tokenId,
                payload: payload
            };
        } catch (error) {
            console.error('Error creating JWT token:', error);
            webService.addToLogService(error, 'jwtService createToken');
            throw error;
        }
    },

    // Xác thực JWT token
    verifyToken: function(token) {
        try {
            const decoded = jwt.verify(token, JWT_SECRET);
            return {
                valid: true,
                payload: decoded
            };
        } catch (error) {
            console.error('JWT verification error:', error);
            return {
                valid: false,
                error: error.message
            };
        }
    },

    // Lưu token vào database
    saveTokenToDatabase: async function(userId, tokenId, deviceInfo = null, ipAddress = null) {
        try {
            // Lấy cài đặt session
            const multiDeviceService = require('../web/service/multiDeviceService');
            const settings = await multiDeviceService.getUserSessionSettings(userId);
            
            if (settings.allow_multiple_devices === 1 || settings.allow_multiple_devices === '1') {
                // Multi-device: sử dụng multiDeviceService.createSession
                const sessionResult = await multiDeviceService.createSession(userId, tokenId, deviceInfo, ipAddress);
                if (!sessionResult.success) {
                    throw new Error(sessionResult.message || 'Không thể tạo session');
                }
                return sessionResult;
            } else {
                // Single-device: update user và tạo session
                const deviceInfoStr = deviceInfo ? JSON.stringify(deviceInfo) : null;
                return new Promise((resolve, reject) => {
                    const sql = 'UPDATE user SET jwt_token_id = ?, device_info = ?, token_created_at = NOW() WHERE id = ?';
                    db.get().query(sql, [tokenId, deviceInfoStr, userId], function(err, result) {
                        if (err) {
                            console.error('Error saving token to database:', err);
                            webService.addToLogService(err, 'jwtService saveTokenToDatabase');
                            reject(err);
                        } else {
                            // Tạo session mới sử dụng multiDeviceService
                            multiDeviceService.createSession(userId, tokenId, deviceInfo, ipAddress)
                                .then(sessionResult => {
                                    if (sessionResult.success) {
                                        resolve(result);
                                    } else {
                                        reject(new Error(sessionResult.message));
                                    }
                                })
                                .catch(sessionErr => {
                                    console.error('Error creating session:', sessionErr);
                                    reject(sessionErr);
                                });
                        }
                    });
                });
            }
        } catch (error) {
            console.error('Error in saveTokenToDatabase:', error);
            webService.addToLogService(error, 'jwtService saveTokenToDatabase');
            throw error;
        }
    },

    // Xóa token khỏi database
    removeTokenFromDatabase: function(userId, tokenId) {
        return new Promise((resolve, reject) => {
            const sql = 'UPDATE user SET jwt_token_id = NULL, device_info = NULL, token_created_at = NULL WHERE id = ? AND jwt_token_id = ?';
            
            db.get().query(sql, [userId, tokenId], function(err, result) {
                if (err) {
                    console.error('Error removing token from database:', err);
                    webService.addToLogService(err, 'jwtService removeTokenFromDatabase');
                    reject(err);
                } else {
                    // Cập nhật session trong bảng user_sessions
                    const sessionSql = 'UPDATE user_sessions SET logout_at = NOW(), is_active = 0 WHERE user_id = ? AND jwt_token_id = ? AND is_active = 1';
                    
                    db.get().query(sessionSql, [userId, tokenId], function(sessionErr, sessionResult) {
                        if (sessionErr) {
                            console.error('Error updating session:', sessionErr);
                        }
                        resolve(result);
                    });
                }
            });
        });
    },

    // Kiểm tra token có hợp lệ trong database không
    validateTokenInDatabase: async function(userId, tokenId) {
        const multiDeviceService = require('../web/service/multiDeviceService');
        const settings = await multiDeviceService.getUserSessionSettings(userId);
        return new Promise((resolve, reject) => {
            if (settings.allow_multiple_devices === 1 || settings.allow_multiple_devices === '1') {
                // Multi-device: chỉ kiểm tra trong user_sessions
                const sql = 'SELECT * FROM user_sessions WHERE user_id = ? AND jwt_token_id = ? AND is_active = 1';
                db.get().query(sql, [userId, tokenId], function(err, result) {
                    if (err) {
                        console.error('Error validating token in user_sessions:', err);
                        webService.addToLogService(err, 'jwtService validateTokenInDatabase');
                        reject(err);
                    } else {
                        resolve(result && result.length > 0);
                    }
                });
            } else {
                // Single-device: kiểm tra bảng user như cũ
                const sql = 'SELECT jwt_token_id, token_created_at FROM user WHERE id = ? AND jwt_token_id = ?';
                db.get().query(sql, [userId, tokenId], function(err, result) {
                    if (err) {
                        console.error('Error validating token in database:', err);
                        webService.addToLogService(err, 'jwtService validateTokenInDatabase');
                        reject(err);
                    } else {
                        if (result && result.length > 0) {
                            const tokenData = result[0];
                            const tokenCreatedAt = new Date(tokenData.token_created_at);
                            const now = new Date();
                            const hoursDiff = (now - tokenCreatedAt) / (1000 * 60 * 60);
                            // Kiểm tra token có quá 24 giờ không
                            if (hoursDiff > 24) {
                                resolve(false);
                            } else {
                                resolve(true);
                            }
                        } else {
                            resolve(false);
                        }
                    }
                });
            }
        });
    },

    // Lấy thông tin user từ token
    getUserFromToken: function(token) {
        return new Promise(async (resolve, reject) => {
            try {
                const verification = this.verifyToken(token);
                if (!verification.valid) {
                    resolve(null);
                    return;
                }

                const payload = verification.payload;
                const isValidInDB = await this.validateTokenInDatabase(payload.userId, payload.tokenId);
                
                if (!isValidInDB) {
                    resolve(null);
                    return;
                }

                resolve(payload);
            } catch (error) {
                console.error('Error getting user from token:', error);
                resolve(null);
            }
        });
    },

    // Làm mới token
    refreshToken: function(oldToken) {
        return new Promise(async (resolve, reject) => {
            try {
                const verification = this.verifyToken(oldToken);
                if (!verification.valid) {
                    resolve(null);
                    return;
                }

                const payload = verification.payload;
                const isValidInDB = await this.validateTokenInDatabase(payload.userId, payload.tokenId);
                
                if (!isValidInDB) {
                    resolve(null);
                    return;
                }

                // Tạo token mới
                const newTokenData = this.createToken(payload.userId, payload);
                
                // Cập nhật token trong database
                await this.saveTokenToDatabase(payload.userId, newTokenData.tokenId, payload.deviceInfo, payload.ipAddress);
                
                resolve(newTokenData.token);
            } catch (error) {
                console.error('Error refreshing token:', error);
                resolve(null);
            }
        });
    },

    // Lấy thông tin thiết bị từ request
    getDeviceInfo: function(req) {
        return {
            userAgent: req.headers['user-agent'],
            ipAddress: req.ip || req.connection.remoteAddress,
            timestamp: new Date().toISOString()
        };
    },

    // Làm mới token nếu sắp hết hạn
    refreshTokenIfNeeded: function(decoded) {
        const now = Date.now() / 1000;
        if (decoded.exp - now < 3600) { // Còn 1 tiếng
            return jwt.sign(
                { 
                    userId: decoded.userId,
                    tokenId: decoded.tokenId,
                    email: decoded.email,
                    name: decoded.name,
                    full_name: decoded.full_name,
                    role_id: decoded.role_id,
                    isAdmin: decoded.isAdmin
                },
                JWT_SECRET,
                { expiresIn: TOKEN_EXPIRY }
            );
        }
        return null;
    },

    // Lấy danh sách thiết bị đang đăng nhập
    getActiveDevices: async function(userId) {
        try {
            const multiDeviceService = require('../web/service/multiDeviceService');
            const sessions = await multiDeviceService.getActiveSessions(userId);
            return sessions.map(session => ({
                id: session.id,
                tokenId: session.jwt_token_id,
                deviceName: session.device_name,
                deviceType: session.device_type,
                browser: session.browser,
                os: session.os,
                ipAddress: session.ip_address,
                loginAt: session.login_at,
                lastActivity: session.last_activity,
                isCurrentSession: session.is_current_session === 1
            }));
        } catch (error) {
            console.error('Error getting active devices:', error);
            return [];
        }
    }
};

module.exports = jwtService; 