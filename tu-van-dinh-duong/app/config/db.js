let env   = require('dotenv').config();
var mysql = require('mysql');
var state = {
    pool: null,
    mode: null
}
exports.MODE_PRODUCTION = 'mode_production';

exports.connect = function(mode, done) {
    try {
        state.pool = mysql.createPool({
            host: 'localhost',
            user: env.parsed.DATABASE_USER,
            password: env.parsed.DATABASE_PASSWORD,
            database: env.parsed.DATABASE_URL
        });
        state.mode = mode;
        
        // Test connection
        state.pool.getConnection(function(err, connection) {
            if (err) {
                console.error('Database connection error:', err);
                if (done) done(err);
            } else {
                console.log('Database connected successfully');
                connection.release();
                if (done) done();
            }
        });
    } catch (error) {
        console.log("error", error);
        if (done) done(error);
    }
}

exports.get = function() {
    return state.pool;
}

