﻿<nav class="main-header navbar navbar-expand-md navbar-dark bg-dark">
   <ul class="navbar-nav pl-2 pl-md-0">
      <li class="nav-item">
         <a class="nav-link" id="nopSideBarPusher" data-widget="pushmenu" href="#">
            <i class="fa fa-bars"></i>
         </a>
   </ul>
   <a href="/admin" class="brand-link navbar-dark">
      <img src="/public/admin/images/logo3_transparent.png" class="brand-image logo d-block d-md-none d-sm-block d-sm-none"> 
   </a>
   <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarText" aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation"> 
      <span class=navbar-toggler-icon></span> 
   </button>
   <div class="collapse navbar-collapse" id="navbarText">
      <ul class="navbar-nav ml-auto pl-2">
         <% if (user){%>
         <li class="nav-item"><a href="#" class="nav-link disabled"><%= user.name %></a></li>
         <li class="nav-item"><a class="nav-link" href="/user/logout">Logout</a></li>
         <%}%>
         <li class="nav-item"><a class="nav-link" href="/">Trang chủ</a></li>
      </ul>
   </div>
</nav>
<aside class="main-sidebar sidebar-dark-primary elevation-4">
   <a href="/admin" class="brand-link navbar-dark logo-switch"> 
      <img src="/public/admin/images/logo3_transparent.png" class="brand-image-xl logo-xl"> 
      <img src="/public/content/images/logo3-compact.png" class="brand-image-xs logo-xs"> 
   </a>
   <div class="sidebar">
      <nav class="mt-2">
         <ul id="admin-menu" class="nav nav-pills nav-sidebar flex-column nav-legacy" data-widget="treeview" role="menu">
            <% if(user){%>
               <li class="nav-item">
                  <a href="/admin" class="nav-link">
                     <i class="nav-icon fas fa-desktop"></i>
                     <p>Dashboard Admin</p>
                  </a>
               </li>
               <!-- Admin hoặc hệ thống -->
               <% if (user.role_id.includes(1) || user.role_id.includes(3) || user.role_id.includes(5) || user.role_id.includes(4)){%>
                  <% if (user.role_id.includes(1) || user.role_id.includes(3) || user.role_id.includes(5)){%>
                     <li class="nav-item has-treeview <%= ['/admin/role','/admin/user','/admin/hospital','/admin/log','/admin/setting'].includes(originalUrl) ? 'menu-open': ''%>">
                        <a href="#" class="nav-link">
                           <i class="nav-icon fas fa-book"></i>
                           <span>Quản lý hệ thống</span> 
                           <i class="right fas fa-angle-left"></i>
                        </a>
                        <ul class="nav nav-treeview treeview-menu">
                           <% if (user.role_id.includes(1) || user.role_id.includes(3)){ %>
                           <li class="nav-item">
                              <a href="/admin/role" class="nav-link <%=originalUrl == '/admin/role' ? 'active': ''%>">
                                 <i class="nav-icon fas fa-cubes"></i>
                                 <p>Quản lý quyền</p>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a href="/admin/user" class="nav-link <%=originalUrl == '/admin/user' ? 'active': ''%>">
                                 <i class="nav-icon fas fa-user"></i>
                                 <p>Quản lý người dùng</p>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a href="/admin/hospital" class="nav-link <%=originalUrl == '/admin/hospital' ? 'active': ''%>">
                                 <i class="nav-icon far fa-hospital"></i>
                                 <p>Quản lý bệnh viện</p>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a href="/admin/log" class="nav-link <%=originalUrl == '/admin/log' ? 'active': ''%>">
                                 <i class="nav-icon fas fa-cube"></i>
                                 <p>Nhật ký hoạt động</p>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a href="/admin/setting" class="nav-link <%=originalUrl == '/admin/setting' ? 'active': ''%>">
                                 <i class="nav-icon fas fa-cogs"></i>
                                 <p>Cài đặt</p>
                              </a>
                           </li>
                           <% } %>
                           <% if(user.role_id.includes(1) || user.role_id.includes(3) || user.role_id.includes(5)){ %>
                              <li class="nav-item">
                                 <a href="/admin/department" class="ml-3 nav-link <%=originalUrl == '/admin/department' ? 'active': ''%>">
                                    <i class="fas fa-clinic-medical"></i>
                                    <p>Quản lý khoa</p>
                                 </a>
                              </li>
                           <% } %>
                        </ul>
                     </li>
                     <% if (user.role_id.includes(1) || user.role_id.includes(3) || user.role_id.includes(4)){ %>
                        <li class="nav-item has-treeview <%= ['/admin/standard-weight-height','/admin/nutritional-needs','/admin/height-by-weight','/admin/index-by-age','/admin/menu-time','/admin/alternative-food','/admin/food-type','/admin/food-info'].includes(originalUrl) ? 'menu-open': ''%>">
                           <a href="#" class="nav-link">
                              <i class="nav-icon fas fa-book"></i>
                              <span>Chỉ số chung</span> 
                              <i class="right fas fa-angle-left"></i>
                           </a>
                           <ul class="nav nav-treeview treeview-menu">
                              <% if (user.role_id.includes(1) || user.role_id.includes(3)){ %>
                              <li class="nav-item">
                                 <a href="/admin/standard-weight-height" class="ml-3 nav-link <%=originalUrl == '/admin/standard-weight-height' ? 'active': ''%>">
                                    <i class="fas fa-balance-scale"></i>
                                    <p>CC - CN chuẩn</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/nutritional-needs" class="ml-3 nav-link <%=originalUrl == '/admin/nutritional-needs' ? 'active': ''%>">
                                    <i class="fab fa-nutritionix"></i>
                                    <p>NCDD</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/height-by-weight" class="ml-3 nav-link <%=originalUrl == '/admin/height-by-weight' ? 'active': ''%>">
                                    <i class="fas fa-weight"></i>
                                    <p>CCCN</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/index-by-age" class="ml-3 nav-link <%=originalUrl == '/admin/index-by-age' ? 'active': ''%>">
                                    <i class="fas fa-indent"></i>
                                    <p>Chỉ số theo tuổi</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/alternative-food" class="ml-3 nav-link <%=originalUrl == '/admin/alternative-food' ? 'active': ''%>">
                                    <i class="fas fa-bread-slice"></i>
                                    <p>Thực phẩm thay thế</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/food-type" class="ml-3 nav-link <%=originalUrl == '/admin/food-type' ? 'active': ''%>">
                                    <i class="fas fa-cookie-bite"></i>
                                    <p>Loại thực phẩm</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/food-info" class="ml-3 nav-link <%=originalUrl == '/admin/food-info' ? 'active': ''%>">
                                    <i class="fas fa-pizza-slice"></i>
                                    <p>Thông tin thực phẩm</p>
                                 </a>
                              </li>
                              <% } %>
                              <li class="nav-item">
                                 <a href="/admin/menu-time" class="ml-3 nav-link <%=originalUrl == '/admin/menu-time' ? 'active': ''%>">
                                    <i class="far fa-clock"></i>
                                    <p>Quản lý giờ ăn</p>
                                 </a>
                              </li>
                           </ul>
                        </li>
                     <% } %>
                  <% } %>
                  <!-- Quản lý bệnh viện -->
                  <% if (user.role_id.includes(5) || user.role_id.includes(1) || user.role_id.includes(3) || user.role_id.includes(4)){%>
                     <% if (user.role_id.includes(5) || user.role_id.includes(1) || user.role_id.includes(3)){%>
                     <li class="nav-item has-treeview <%= ['/admin/medical-test-type','/admin/medical-test','/admin/subclinical'].includes(originalUrl) ? 'menu-open': ''%>">
                        <a href="#" class="nav-link">
                           <i class="nav-icon fas fa-book"></i>
                           <span>Quản lý xét nghiệm</span> 
                           <i class="right fas fa-angle-left"></i>
                        </a>
                        <ul class="nav nav-treeview treeview-menu">
                           <li class="nav-item">
                              <a href="/admin/medical-test-type" class="ml-3 nav-link <%=originalUrl == '/admin/medical-test-type' ? 'active': ''%>">
                                 <i class="fas fa-vials"></i>
                                 <p>Loại chỉ định xét nghiệm</p>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a href="/admin/medical-test" class="ml-3 nav-link <%=originalUrl == '/admin/medical-test' ? 'active': ''%>">
                                 <i class="fas fa-vial"></i>
                                 <p>Chỉ định xét nghiệm</p>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a href="/admin/subclinical" class="ml-3 nav-link <%=originalUrl == '/admin/subclinical' ? 'active': ''%>">
                                 <i class="fa fa-archive"></i>
                                 <p>Cận lâm sàng</p>
                              </a>
                           </li>
                        </ul>
                     </li>
                     <% } %>
                     <!-- Bác sĩ -->
                     <% if (user.role_id.includes(4) || user.role_id.includes(5) || user.role_id.includes(1) || user.role_id.includes(3)){%>
                        <li class="nav-item has-treeview <%= ['/admin/medicine-type','/admin/medicine'].includes(originalUrl) ? 'menu-open': ''%>">
                           <a href="#" class="nav-link">
                              <i class="nav-icon fas fa-book"></i>
                              <span>Quản lý thuốc</span> 
                              <i class="right fas fa-angle-left"></i>
                           </a>
                           <ul class="nav nav-treeview treeview-menu">
                              <li class="nav-item">
                                 <a href="/admin/medicine-type" class="ml-3 nav-link <%=originalUrl == '/admin/medicine-type' ? 'active': ''%>">
                                    <i class="fas fa-user-md"></i>
                                    <p>Phân loại thuốc</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/medicine?tr=0" class="ml-3 nav-link <%=originalUrl == '/admin/medicine' ? 'active': ''%>">
                                    <i class="fas fa-capsules"></i>
                                    <p>Thuốc bổ xung</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/medicine?tr=1" class="ml-3 nav-link <%=originalUrl == '/admin/medicine' ? 'active': ''%>">
                                    <i class="fas fa-medkit"></i>
                                    <p>Thuốc điều trị</p>
                                 </a>
                              </li>
                           </ul>
                        </li>
                        <li class="nav-item has-treeview <%= ['/admin/active-mode-of-living','/admin/menu-example','/admin/nutrition-advice','/admin/diagnostic'].includes(originalUrl) ? 'menu-open': ''%>">
                           <a href="#" class="nav-link">
                              <i class="nav-icon fas fa-book"></i>
                              <span>Gợi ý mẫu</span> 
                              <i class="right fas fa-angle-left"></i>
                           </a>
                           <ul class="nav nav-treeview treeview-menu">
                              <li class="nav-item">
                                 <a href="/admin/active-mode-of-living" class="ml-3 nav-link <%=originalUrl == '/admin/active-mode-of-living' ? 'active': ''%>">
                                    <i class="fas fa-people-carry"></i>
                                    <p>Chế độ vận động</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/menu-example" class="ml-3 nav-link <%=originalUrl == '/admin/menu-example' ? 'active': ''%>">
                                    <i class="fas fa-utensils"></i>
                                    <p>Thực đơn mẫu</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/nutrition-advice" class="ml-3 nav-link <%=originalUrl == '/admin/nutrition-advice' ? 'active': ''%>">
                                    <i class="fas fa-comment-medical"></i>
                                    <p>Lời khuyên dinh dưỡng</p>
                                 </a>
                              </li>
                              <li class="nav-item">
                                 <a href="/admin/diagnostic" class="ml-3 nav-link <%=originalUrl == '/admin/diagnostic' ? 'active': ''%>">
                                    <i class="fas fa-stethoscope"></i>
                                    <p>Chuẩn đoán mẫu</p>
                                 </a>
                              </li>
                           </ul>
                        </li>
                     <% } %>
                  <% } %>
               <% } %>
            <% } %>
         </ul>
      </nav>
   </div>
</aside>