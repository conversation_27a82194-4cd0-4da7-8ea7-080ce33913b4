<html lang="en">
  <head>
    <title><PERSON><PERSON> sách người dùng</title>
    <%- include('../shared/head') %>
  </head>
  <body class="hold-transition sidebar-mini layout-fixed control-sidebar-slide-open">
    <div class="wrapper">
      <%- include('../shared/menu',{user: user}) %>
      <div class="content-wrapper">
        <div class="content-header clearfix">
          <h1 class="float-left">
            Danh sách người dùng
          </h1>
          <div class="float-right">
            <a href="/admin/user/create" class="btn btn-primary">
                <i class="fas fa-plus-square"></i> Thêm mới
            </a>
          </div>
        </div>
        <section class="content">
          <div class="container-fluid">
            <% if (errors.length >0){%>
            <div class="alert alert-danger alert-dismissable">
              <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
              <ul>
              <% for (i = 0;i < errors.length;i++){%>
                <li><%=errors[i]%></li>
              <%}%>
              </ul>
            </div>
            <%}%>
            <div class="form-horizontal">
              <div class="cards-group">
                <div class="card card-default card-search">
                  <div class="card-body">
                    <div class="row search-row opened">
                      <div class="search-text">Tìm kiếm</div>
                      <div class="icon-search">
                        <i class="fas fa-search" aria-hidden="true"></i>
                      </div>
                      <div class="icon-collapse">
                        <i class="far fa-angle-up" aria-hidden="true"></i>
                      </div>
                    </div>
                    <div class="search-body">
                      <div class="row">
                        <div class="col-md-5">
                            <div class="form-group row">
                                <div class="col-md-4">
                                  <div class="label-wrapper">
                                    <label class="col-form-label">Tên</label>
                                  </div>
                                </div>
                                <div class="col-md-8">
                                    <input class="form-control text-box single-line" id="search_name" name="search_name" type="text">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class=col-md-4>
                                    <div class=label-wrapper>
                                        <label class="col-form-label">Quyền người dùng</label>
                                    </div>
                                </div>
                                <div class=col-md-8>
                                    <select id="search_role_ids" multiple name="search_role_ids">
                                        <option selected value="0">Tất cả</option>
                                        <% if (roles.length>0){%>
                                        <% for (i = 0;i < roles.length;i++){%>
                                        <option value="<%=roles[i].role_id%>"><%=roles[i].role_name%></option>
                                        <%}%>
                                        <%}%>
                                    </select>
                                    <script>
                                    $(document).ready(function() {
                                        $("#search_role_ids").kendoMultiSelect({
                                            select: function(n) {
                                                var t = this.value();
                                                this.dataSource.view()[n.item.index()].value === "0" ? this.value("") : t.indexOf("0") !== -1 && (t = $.grep(t, function(n) {
                                                    return n !== "0"
                                                }), this.value(t))
                                            },
                                            change: function() {
                                                this.value().length === 0 && this.value(["0"])
                                            }
                                        }).data("kendoMultiSelect")
                                    })
                                    </script>
                                    <script>
                                    $(document).ready(function() {
                                        var n = $("#search_role_ids").data("kendoMultiSelect");
                                        n.setOptions({
                                            autoClose: !1,
                                            filter: "contains"
                                        })
                                    })
                                    </script>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7">
                            <div class="form-group row">
                                <div class="col-md-4">
                                  <div class="label-wrapper">
                                    <label class="col-form-label">Email</label>
                                  </div>
                                </div>
                                <div class="col-md-8">
                                    <input class="form-control text-box single-line" id="search_email" name="search_email" type="text">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-4">
                                  <div class="label-wrapper">
                                    <label class="col-form-label">Trạng thái</label>
                                  </div>
                                </div>
                                <div class="col-md-8">
                                    <select class="form-control valid" id="search_active" name="search_active">
                                        <option selected value="-1">Tất cả</option>
                                        <option value="1">Kích hoạt</option>
                                        <option value="0">Khoá tài khoản</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="text-center col-12">
                            <button type="button" id="search-button" class="btn btn-primary btn-search"> 
                                <i class="fas fa-search"></i> Tìm kiếm 
                            </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card card-default">
                  <div class="card-body">
                    <table class="table table-bordered table-hover table-striped dataTable" width=100% id="user-grid">
                    </table>
                    <script type="text/javascript">
                        $(document).ready(function() {
                            var loading = $("#loading-page");
                            $("#user-grid").DataTable({
                                processing: !0,
                                serverSide: !0,
                                ajax: {
                                    url: "/admin/user/list",
                                    type: "POST",
                                    dataType: "json",
                                    dataSrc: function(response){
                                        return response.data;
                                    },
                                    beforeSend: function() {
                                        loading.show();
                                        window.setTimeout(function () {
                                            loading.hide();
                                        }, 500);
                                    },
                                    data: function(n){
                                        if (!n) {
                                            n = {};
                                        }
                                        n.search_name     = $("#search_name").val();
                                        n.search_email    = $("#search_email").val();
                                        n.search_active   = $("#search_active").val();
                                        n.search_role_ids = $("#search_role_ids").val().toString();
                                        return n;
                                    }
                                },
                                scrollX: !0,
                                info: !0,
                                paging: !0,
                                pagingType: "simple_numbers",
                                language: {
                                    emptyTable: "Không có dữ liệu",
                                    info: "_START_-_END_ of _TOTAL_ items",
                                    infoEmpty: "Không có dữ liệu",
                                    infoFiltered: "(filtered from _MAX_ total entries)",
                                    thousands: ",",
                                    lengthMenu: "Show _MENU_ items",
                                    loadingRecords: "Đang tải dữ liệu...",
                                    processing: "<i class='fa fa-refresh fa-spin'><\/i>",
                                    search: "Tìm kiếm:",
                                    zeroRecords: "Không có dữ liệu",
                                    paginate: {
                                        first: "<i class='k-icon k-i-seek-w'><\/i>",
                                        last: "<i class='k-icon k-i-seek-e'><\/i>",
                                        next: "<i class='k-icon k-i-arrow-e'><\/i>",
                                        previous: "<i class='k-icon k-i-arrow-w'><\/i>"
                                    },
                                    aria: {
                                        sortAscending: ": activate to sort column ascending",
                                        sortDescending: ": activate to sort column descending"
                                    }
                                },
                                pageLength: 15,
                                lengthMenu: [15, 20, 50, 100],
                                ordering: !1,
                                buttons: [{
                                    name: "refresh",
                                    text: '<i class="fas fa-sync-alt" style="padding-left: 5px"><\/i>',
                                    action: function() {
                                        updateTable("#user-grid", !1)
                                    }
                                }],
                                dom: "<'row'<'col-md-12't>><'row margin-t-5'<'col-lg-5 col-xs-12'<'float-lg-left'p>><'col-lg-3 col-xs-12'<'text-center'l>><'col-lg-3 col-xs-12'<'float-lg-right text-center'i>><'col-lg-1 col-xs-12'<'float-lg-right text-center data-tables-refresh'B>>>",
                                columns: [{
                                    title: "Tên đăng nhập",
                                    width: "150",
                                    visible: !0,
                                    searchable: !1,
                                    render: function(n) {
                                        return escapeHtml(n)
                                    },
                                    data: "name"
                                }, {
                                    title: "Tên đầy đủ",
                                    width: "200",
                                    visible: !0,
                                    searchable: !1,
                                    className: "text-left",
                                    render: function(n) {
                                        return escapeHtml(n)
                                    },
                                    data: "full_name"
                                }, {
                                    title: "Quyền người dùng",
                                    width: "200",
                                    visible: !0,
                                    searchable: !1,
                                    className: "text-left",
                                    render: function(n) {
                                        return escapeHtml(n)
                                    },
                                    data: "role_name"
                                }, {
                                    title: "Email",
                                    visible: !0,
                                    searchable: !1,
                                    className: "text-left",
                                    render: function(n) {
                                        return escapeHtml(n)
                                    },
                                    data: "email"
                                }, {
                                    title: "Ngày tạo",
                                    width: "100",
                                    visible: !0,
                                    searchable: !1,
                                    className: "text-center",
                                    render: function(n) {
                                        return n ? moment(n).locale("vi-VNs").format("L") : null
                                    },
                                    data: "create_on"
                                }, {
                                    title: "Kích hoạt",
                                    width: "80",
                                    visible: !0,
                                    searchable: !1,
                                    className: "text-center",
                                    render: function(n) {
                                        return n ? '<i class="fas fa-check true-icon"><\/i>' : '<i class="fas fa-times false-icon"><\/i>'
                                    },
                                    data: "active"
                                }, {
                                    title: "Sửa",
                                    width: "100",
                                    visible: !0,
                                    searchable: !1,
                                    className: "button-column",
                                    render: function(n, t, i, r) {
                                        return '<a class="btn btn-default" href="/admin/user/edit/' + n + '"><i class="fas fa-pencil-alt"><\/i> Sửa<\/a>'
                                    },
                                    data: "id"
                                }]
                            });
                            $("#search-button").click(function() {
                                return $("#user-grid").DataTable().ajax.reload();
                            });
                            $("#user-grid").DataTable().columns.adjust();
                        })
                    </script>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <%- include('../shared/footer') %>
    </div>
  </body>
</html>