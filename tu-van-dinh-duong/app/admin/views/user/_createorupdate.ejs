﻿<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Tên đ<PERSON>ng nh<PERSON>p</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" id="name" name="name" type="text" value="<%=pr_user.name%>">
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Tên đ<PERSON>y đ<PERSON></label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" id="full_name" name="full_name" type="text" value="<%=pr_user.full_name%>">
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Email</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" id="email" name="email" type="text" value="<%=pr_user.email%>">
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper flex-center">
      <label class="col-form-label">Bệnh viện</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group-append input-group-required">
      <div class="input-group">
        <select id="hospital_id" name="hospital_id" placeholder="Chọn bệnh viện">
          <option></option>
          <% if (hospital.length > 0){%>
            <% for (i = 0;i < hospital.length;i++){%>
              <option <%=(hospital[i].id == pr_user.hospital_id) ? 'selected' : ''%> value="<%=hospital[i].id%>"><%=hospital[i].name%></option>
            <%}%>
          <%}%>
        </select>
        <script>
          $("#hospital_id").kendoDropDownList({
            change: function(e) {
              let value = this.value();
              let department_id = <%=pr_user.department_id ? pr_user.department_id : 0%>;
              $.ajax({
                type: 'GET',
                url: '/admin/department/list-follow-hospital',
                data: { hos_id: value},
                success: function (response) {
                    if (response.length > 0) {
                      $("#department_id").kendoDropDownList({
                        dataSource: response,
                        dataTextField: "name",
                        dataValueField: "id"
                      });
                      let dropdownlistDepartment = $("#department_id").data("kendoDropDownList");
                      if(response.findIndex(s => s.id == department_id)){
                        dropdownlistDepartment.value(department_id);
                      }else{
                        dropdownlistDepartment.value('');
                      }
                      dropdownlistDepartment.trigger("change");
                    }
                }
              });      
            }
          });
          </script>
      </div>
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper flex-center">
      <label class="col-form-label">Khoa</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group-append input-group-required">
      <div class="input-group">
        <select id="department_id" name="department_id">
          <% if( pr_user.department_id ){ %>
            <option selected value="<%=pr_user.department_id%>"><%=pr_user.department_name%></option>
          <% } %>
        </select>
        <script>
          $("#department_id").kendoDropDownList({});
        </script>
      </div>
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Mật khẩu</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group">
      <input class="form-control text-box single-line password" id="password" name="password" type="password">
      <% if (create_or_update == 1){%>
      <div class="input-group-btn"><span class="required">*</span></div>
      <%} else {%>
      <div class="input-group-btn"><span style="color:#fff" class="required">*</span></div>
      <%}%>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Quyền người dùng</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group-append input-group-required">
      <div class="input-group">
        <select id="selected_role_ids" multiple name="selected_role_ids">
          <% if (roles.length > 0){%>
          <% for (i = 0;i < roles.length;i++){%>
          <option <%=role_ids.indexOf(roles[i].role_id) !== -1 ? 'selected' : ''%> value="<%=roles[i].role_id%>"><%=roles[i].role_name%></option>
          <%}%>
          <%}%>
        </select>
        <script>
            $(document).ready(function () {
            $("#selected_role_ids").kendoMultiSelect({
                select: function (e) {
                  var current = this.value();
                  if (this.dataSource.view()[e.item.index()].value === "0") {
                    this.value("");
                  } else if (current.indexOf("0") !== -1) {
                    current = $.grep(current, function (value) {
                      return value !== "0";
                    });
                    this.value(current);
                  }
                },
                change: function (e) {
                  if (this.value().length === 0){
                    this.value(["0"]);
                  }
                }
            }).data("kendoMultiSelect");
          });
        </script>
      </div>
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
    <script>
      $(document).ready(function() {
        var rolesIdsInput = $('#selected_role_ids').data("kendoMultiSelect");
        <% if (roles.length > 0){%>
        rolesIdsInput.setOptions({
          rolesIdsInput: false,
          filter: "contains"
        });
        <%} else {%>
          rolesIdsInput.setOptions({
            enable: false,
            placeholder: 'Không có danh sách quyền người dùng.'
          });
          rolesIdsInput._placeholder();
          rolesIdsInput._enable();
        <%}%>
      });
    </script>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Trạng thái</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group">
      <input type="checkbox" <%=pr_user.active == 1 ? 'checked' : ''%> id="active" name="active"/>
    </div>
  </div>
</div>
<% if (create_or_update == 0){%>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Ngày tạo</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="form-text-row">
    <%=moment(pr_user.create_on).locale("vi-VN").format("L")%>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Đăng nhập gần đây</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="form-text-row">
      <%=moment(pr_user.last_login).locale("vi-VN").format("L")%>
    </div>
  </div>
</div>
<%}%>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Số điện thoại</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group">
      <input class="form-control text-box single-line" id="phone" name="phone" type="text" value="<%=pr_user.phone%>">
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Địa chỉ</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group">
      <input class="form-control text-box single-line" id="address" name="address" type="text" value="<%=pr_user.address%>">
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Sinh nhật</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group">
      <input id="birthday" name="birthday" type="text" value="<%=pr_user.birthday%>">
    </div>
    <script>$(document).ready(function(){
      $("#birthday").kendoDatePicker()
    })
    </script>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Giới tính</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group">
      <div class="form-check">
        <input <%=pr_user.gender == 0 ? "checked" : ""%> type="radio" class="form-check-input" name="gender" id="gender_male" value="male" /> 
        <label class="form-check-label"> Nam </label>
      </div>
      <div class="form-check">
        <input <%=pr_user.gender == 1 ? "checked" : ""%> type="radio" class="form-check-input" name="gender" id="gender_female" value="female"/> 
        <label class="form-check-label"> Nữ </label>
      </div>
      <div class="form-check">
        <input <%=pr_user.gender == 2 ? "checked" : ""%> type="radio" class="form-check-input" name="gender" id="gender_orther" value="orther"/> 
        <label class="form-check-label"> Khác </label>
      </div>
    </div>
  </div>
</div>