﻿<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label flex-center">Tên khoa</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="department_name" type="text" value="<%=department.name%>">
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper flex-center">
      <label class="col-form-label">Bệnh viện</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group-append input-group-required">
      <div class="input-group">
        <select id="hospital_id" name="hospital_id">
          <% if (hospital.length > 0){%>
            <% for (i = 0;i < hospital.length;i++){%>
              <option <%=(hospital[i].id == department.hospital_id) ? 'selected' : ''%> value="<%=hospital[i].id%>"><%=hospital[i].name%></option>
            <%}%>
          <%}%>
        </select>
        <script>
          $("#hospital_id").kendoDropDownList({
            enable: true
          });
          </script>
      </div>
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper flex-center">
      <label class="col-form-label">Số điện thoại</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="department_phone" type="text" value="<%=department.phone%>">
    </div>
  </div>
</div>