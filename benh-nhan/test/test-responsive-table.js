/**
 * <PERSON><PERSON><PERSON> để test responsive table functionality
 * Kiểm tra xem responsive table có hoạt động đúng không
 */

const fs = require('fs');
const path = require('path');

class ResponsiveTableTester {
    constructor() {
        this.viewsDir = path.join(__dirname, '../views');
        this.testResults = [];
    }

    // Tìm tất cả views có table
    findViewsWithTables() {
        const views = [];
        
        const scanDirectory = (dir) => {
            const items = fs.readdirSync(dir);
            
            items.forEach(item => {
                const itemPath = path.join(dir, item);
                const stat = fs.statSync(itemPath);
                
                if (stat.isDirectory()) {
                    scanDirectory(itemPath);
                } else if (item.endsWith('.ejs')) {
                    const content = fs.readFileSync(itemPath, 'utf8');
                    
                    if (content.includes('<table') && content.includes('table-responsive')) {
                        views.push({
                            file: item,
                            path: itemPath,
                            content: content,
                            relativePath: path.relative(this.viewsDir, itemPath)
                        });
                    }
                }
            });
        };
        
        scanDirectory(this.viewsDir);
        return views;
    }

    // Kiểm tra view có đúng structure không
    checkViewStructure(view) {
        const content = view.content;
        const checks = {
            hasTableResponsive: content.includes('table-responsive'),
            hasTableClass: content.includes('class="table'),
            hasThead: content.includes('<thead>'),
            hasTbody: content.includes('<tbody>') || content.includes('DataTable'),
            hasProperHeaders: this.checkHeaders(content),
            hasDataTable: content.includes('DataTable') || content.includes('dataTable'),
            includesResponsiveJS: this.checkJSInclusion(view)
        };

        return checks;
    }

    // Kiểm tra headers có đúng format không
    checkHeaders(content) {
        const headerMatches = content.match(/<th[^>]*>(.*?)<\/th>/gi);
        if (!headerMatches) return false;

        // Kiểm tra có ít nhất 2 headers
        return headerMatches.length >= 2;
    }

    // Kiểm tra JS có được include không
    checkJSInclusion(view) {
        // Kiểm tra xem view có include footer không (nơi chứa responsive-table.js)
        return view.content.includes("include('../layout/footer')") || 
               view.content.includes("include('layout/footer')") ||
               view.content.includes('responsive-table.js');
    }

    // Tạo report chi tiết
    generateDetailedReport(view, checks) {
        const issues = [];
        const suggestions = [];

        if (!checks.hasTableResponsive) {
            issues.push('❌ Thiếu class "table-responsive"');
            suggestions.push('Thêm <div class="table-responsive"> bao quanh table');
        }

        if (!checks.hasTableClass) {
            issues.push('❌ Thiếu class "table" trên table element');
            suggestions.push('Thêm class="table table-bordered" vào table');
        }

        if (!checks.hasThead) {
            issues.push('❌ Thiếu <thead> element');
            suggestions.push('Thêm <thead> với các <th> headers');
        }

        if (!checks.hasProperHeaders) {
            issues.push('❌ Headers không đúng format hoặc quá ít');
            suggestions.push('Đảm bảo có ít nhất 2 headers trong <thead>');
        }

        if (!checks.includesResponsiveJS) {
            issues.push('⚠️  Có thể thiếu responsive-table.js');
            suggestions.push('Đảm bảo view include layout/footer hoặc responsive-table.js');
        }

        return { issues, suggestions };
    }

    // Test một view cụ thể
    testView(view) {
        console.log(`\n🔍 Testing: ${view.relativePath}`);
        
        const checks = this.checkViewStructure(view);
        const { issues, suggestions } = this.generateDetailedReport(view, checks);
        
        const score = Object.values(checks).filter(Boolean).length;
        const total = Object.keys(checks).length;
        const percentage = Math.round((score / total) * 100);

        console.log(`📊 Score: ${score}/${total} (${percentage}%)`);

        if (issues.length > 0) {
            console.log('🚨 Issues found:');
            issues.forEach(issue => console.log(`   ${issue}`));
        }

        if (suggestions.length > 0) {
            console.log('💡 Suggestions:');
            suggestions.forEach(suggestion => console.log(`   ${suggestion}`));
        }

        if (issues.length === 0) {
            console.log('✅ All checks passed!');
        }

        return {
            view: view.relativePath,
            score,
            total,
            percentage,
            issues,
            suggestions,
            checks
        };
    }

    // Chạy test cho tất cả views
    runAllTests() {
        console.log('🚀 Starting Responsive Table Tests...\n');

        const views = this.findViewsWithTables();
        console.log(`📄 Found ${views.length} views with tables\n`);

        views.forEach(view => {
            const result = this.testView(view);
            this.testResults.push(result);
        });

        this.generateSummaryReport();
    }

    // Tạo summary report
    generateSummaryReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📋 SUMMARY REPORT');
        console.log('='.repeat(60));

        const totalViews = this.testResults.length;
        const passedViews = this.testResults.filter(r => r.issues.length === 0).length;
        const avgScore = this.testResults.reduce((sum, r) => sum + r.percentage, 0) / totalViews;

        console.log(`📊 Total Views Tested: ${totalViews}`);
        console.log(`✅ Fully Compatible: ${passedViews}/${totalViews} (${Math.round((passedViews/totalViews)*100)}%)`);
        console.log(`📈 Average Score: ${Math.round(avgScore)}%`);

        console.log('\n🏆 Top Performing Views:');
        const topViews = this.testResults
            .sort((a, b) => b.percentage - a.percentage)
            .slice(0, 5);
        
        topViews.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.view} - ${result.percentage}%`);
        });

        console.log('\n⚠️  Views Needing Attention:');
        const needsAttention = this.testResults
            .filter(r => r.percentage < 80)
            .sort((a, b) => a.percentage - b.percentage);
        
        if (needsAttention.length === 0) {
            console.log('   🎉 All views are in good shape!');
        } else {
            needsAttention.forEach(result => {
                console.log(`   📄 ${result.view} - ${result.percentage}% (${result.issues.length} issues)`);
            });
        }

        console.log('\n💡 Next Steps:');
        console.log('   1. Fix issues in views with low scores');
        console.log('   2. Test responsive behavior in browser');
        console.log('   3. Verify mobile layout on real devices');
        console.log('   4. Check DataTables integration');
    }
}

// Chạy tests
try {
    const tester = new ResponsiveTableTester();
    tester.runAllTests();
} catch (error) {
    console.error('❌ Error running tests:', error.message);
    console.error(error.stack);
}
