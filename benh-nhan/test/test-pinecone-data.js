const pineconeService = require('../services/pineconeService');

async function testPineconeData() {
    console.log('🧪 Testing Pinecone Data Structure...\n');

    try {
        // Test raw search result
        console.log('1️⃣ Testing raw Pinecone search...');
        
        const foodResult = await pineconeService.searchFoods('cơm', { topK: 1 });
        
        if (foodResult.success && foodResult.data.length > 0) {
            const food = foodResult.data[0];
            console.log('   Raw food result:');
            console.log('   ', JSON.stringify(food, null, 2));
        }

        const dishResult = await pineconeService.searchDishes('cơm', { topK: 1 });
        
        if (dishResult.success && dishResult.data.length > 0) {
            const dish = dishResult.data[0];
            console.log('\n   Raw dish result:');
            console.log('   ', JSON.stringify(dish, null, 2));
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testPineconeData();
