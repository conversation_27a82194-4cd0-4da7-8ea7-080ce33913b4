const aiMenuService = require('../services/aiMenuService');

async function testLipidMapping() {
    console.log('🧪 Testing Lipid to Fat Mapping...\n');

    try {
        // Test với yêu cầu đơn giản
        console.log('1️⃣ Testing AI menu generation...');
        const request = {
            requirements: "Tạo thực đơn cân bằng",
            preferences: [],
            restrictions: [],
            meal_count: 3,
            target_nutrition: {
                energy: 1000,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };

        const result = await aiMenuService.generateSmartMenu(request);

        if (result.success) {
            const menu = result.data.menu;
            const nutrition = result.data.nutrition_summary;

            console.log(`   ✅ Menu generated: ${menu.name}`);
            console.log(`   📊 Energy: ${nutrition.energy} kcal`);
            console.log(`   📊 Lipid: ${nutrition.lipid}g`);

            // Check lipid field in foods
            let hasLipidField = false;
            let hasFatField = false;

            menu.detail.forEach(meal => {
                console.log(`\n   🍽️  ${meal.name}:`);
                meal.listFood.forEach(food => {
                    if (typeof food.lipid !== 'undefined') {
                        hasLipidField = true;
                        console.log(`      - ${food.name}: lipid=${food.lipid}g`);
                    }
                    if (typeof food.fat !== 'undefined') {
                        hasFatField = true;
                        console.log(`      - ${food.name}: fat=${food.fat}g`);
                    }
                });
            });

            console.log(`\n   📊 Has lipid field: ${hasLipidField}`);
            console.log(`   📊 Has fat field: ${hasFatField}`);

            // Test addAIMenuToSystem mapping
            console.log('\n2️⃣ Testing addAIMenuToSystem mapping...');
            
            // Simulate the mapping function
            const testFood = {
                name: "Test Food",
                energy: 100,
                protein: 10,
                carbohydrate: 15,
                lipid: 5.5, // AI response has lipid
                fiber: 2
            };

            console.log('   Before mapping:', JSON.stringify(testFood, null, 2));

            // Apply the mapping logic
            if (typeof testFood.lipid !== 'undefined' && typeof testFood.fat === 'undefined') {
                testFood.fat = testFood.lipid;
            }

            console.log('   After mapping:', JSON.stringify(testFood, null, 2));

            if (testFood.fat === testFood.lipid) {
                console.log('   ✅ Lipid to Fat mapping works correctly!');
            } else {
                console.log('   ❌ Lipid to Fat mapping failed');
            }

        } else {
            console.log(`   ❌ Menu generation failed: ${result.message}`);
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testLipidMapping();
