#!/usr/bin/env node

/**
 * Test script để kiểm tra việc tích hợp AI menu vào hệ thống
 * Mô phỏng việc user click "Sử dụng thực đơn này"
 */

const aiMenuService = require('../services/aiMenuService');

// Mock window object và các function cần thiết
global.window = {
    menuExamine: [],
    addAIMenuToSystem: null
};

// Mock jQuery và các function UI
global.$ = (selector) => ({
    show: () => {},
    modal: () => {},
    text: () => {},
    val: () => ''
});

// Mock các function khác
global.generateTableMenu = () => {};
global.addNewOptionToVirtualSelect = () => {};
global.toarstMessage = (msg) => console.log('Toast:', msg);

// Implement addAIMenuToSystem function (copy từ menuExample.js)
function addAIMenuToSystem(aiMenu) {
    try {
        console.log('🔗 Integrating AI menu into system...');

        // Tạo ID mới cho menu
        let newId = 1;
        if (window.menuExamine && window.menuExamine.length > 0) {
            newId = Math.max(...window.menuExamine.map(m => m.id || 0)) + 1;
        }

        // Chuẩn bị menu structure
        const menuToAdd = {
            id: newId,
            name: aiMenu.name || 'Thực đơn AI',
            detail: aiMenu.detail || [],
            created_at: aiMenu.created_at || new Date().toISOString(),
            ai_generated: true,
            requirements: aiMenu.requirements || ''
        };

        // Đảm bảo cấu trúc detail đúng format
        if (menuToAdd.detail && Array.isArray(menuToAdd.detail)) {
            menuToAdd.detail.forEach((meal, mealIndex) => {
                if (!meal.id) meal.id = mealIndex + 1;
                if (!meal.name) meal.name = `Bữa ${mealIndex + 1}`;
                if (!meal.listFood) meal.listFood = [];

                // Đảm bảo mỗi food có đủ thông tin
                meal.listFood.forEach((food, foodIndex) => {
                    if (!food.id) food.id = foodIndex + 1;
                    // Đảm bảo có các trường dinh dưỡng cơ bản
                    if (typeof food.energy === 'undefined') food.energy = 0;
                    if (typeof food.protein === 'undefined') food.protein = 0;
                    if (typeof food.carbohydrate === 'undefined') food.carbohydrate = 0;
                    if (typeof food.lipid === 'undefined') food.lipid = 0;
                    if (typeof food.fiber === 'undefined') food.fiber = 0;
                });
            });
        }

        // Thêm vào menuExamine
        if (!window.menuExamine) {
            window.menuExamine = [];
        }
        window.menuExamine.push(menuToAdd);

        console.log('✅ AI menu integrated successfully');
        console.log(`   Menu ID: ${menuToAdd.id}`);
        console.log(`   Menu Name: ${menuToAdd.name}`);
        console.log(`   Meals: ${menuToAdd.detail.length}`);
        
        return true;

    } catch (error) {
        console.error('❌ Error integrating AI menu:', error);
        return false;
    }
}

// Set function to global
global.window.addAIMenuToSystem = addAIMenuToSystem;

async function testMenuIntegration() {
    console.log('🧪 Testing AI Menu Integration Flow');
    console.log('='.repeat(60));
    
    try {
        // Step 1: Generate AI Menu
        console.log('📝 Step 1: Generate AI Menu');
        const request = {
            requirements: 'Tạo thực đơn cho bệnh nhân tiểu đường, 1800 kcal/ngày, ít đường, nhiều chất xơ',
            preferences: ['ít đường', 'nhiều chất xơ'],
            restrictions: ['đường', 'kẹo', 'bánh ngọt'],
            meal_count: 5,
            target_nutrition: {
                energy: 1800,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };
        
        const result = await aiMenuService.generateSmartMenu(request);
        
        if (!result.success) {
            console.log('❌ FAILED: Menu generation failed');
            console.log('Error:', result.message);
            return false;
        }
        
        console.log('✅ AI Menu generated successfully');
        const aiMenu = result.data.menu;
        const nutrition = result.data.nutrition_summary;
        
        // Step 2: Simulate user clicking "Sử dụng thực đơn này"
        console.log('\n🖱️  Step 2: Simulate User Click "Sử dụng thực đơn này"');
        
        // Check initial state
        console.log(`Initial menuExamine length: ${window.menuExamine.length}`);
        
        // Call integration function
        const integrationSuccess = window.addAIMenuToSystem(aiMenu);
        
        if (!integrationSuccess) {
            console.log('❌ FAILED: Menu integration failed');
            return false;
        }
        
        // Step 3: Verify integration
        console.log('\n✅ Step 3: Verify Integration Results');
        
        console.log(`Final menuExamine length: ${window.menuExamine.length}`);
        
        if (window.menuExamine.length === 0) {
            console.log('❌ FAILED: No menu added to menuExamine');
            return false;
        }
        
        const integratedMenu = window.menuExamine[window.menuExamine.length - 1];
        
        // Verify menu structure
        const verificationChecks = {
            hasId: !!integratedMenu.id,
            hasName: !!integratedMenu.name,
            hasDetail: Array.isArray(integratedMenu.detail),
            hasCreatedAt: !!integratedMenu.created_at,
            isAIGenerated: integratedMenu.ai_generated === true,
            hasRequirements: !!integratedMenu.requirements,
            correctMealCount: integratedMenu.detail.length === 5
        };
        
        console.log('\nIntegration verification:');
        Object.entries(verificationChecks).forEach(([key, value]) => {
            console.log(`  ${key}: ${value ? '✅' : '❌'}`);
        });
        
        // Verify meal structure
        let mealStructureValid = true;
        integratedMenu.detail.forEach((meal, index) => {
            const mealChecks = {
                hasId: !!meal.id,
                hasName: !!meal.name,
                hasListFood: Array.isArray(meal.listFood)
            };
            
            const mealValid = Object.values(mealChecks).every(v => v);
            if (!mealValid) {
                mealStructureValid = false;
                console.log(`  Meal ${index + 1}: ❌`);
            }
            
            // Check foods
            meal.listFood.forEach((food, foodIndex) => {
                const foodChecks = {
                    hasId: !!food.id,
                    hasIdFood: !!food.id_food,
                    hasName: !!food.name,
                    hasWeight: typeof food.weight === 'number',
                    hasEnergy: typeof food.energy === 'number'
                };
                
                const foodValid = Object.values(foodChecks).every(v => v);
                if (!foodValid) {
                    mealStructureValid = false;
                    console.log(`    Food ${foodIndex + 1} in meal ${index + 1}: ❌`);
                }
            });
        });
        
        console.log(`Meal structure valid: ${mealStructureValid ? '✅' : '❌'}`);
        
        // Step 4: Summary
        console.log('\n' + '='.repeat(60));
        console.log('📊 INTEGRATION TEST RESULTS:');
        
        const allChecks = {
            'Menu Generation': true,
            'Integration Process': integrationSuccess,
            'Basic Structure': Object.values(verificationChecks).every(v => v),
            'Meal Structure': mealStructureValid
        };
        
        Object.entries(allChecks).forEach(([category, passed]) => {
            console.log(`${category}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        });
        
        const overallSuccess = Object.values(allChecks).every(v => v);
        
        console.log(`\n${overallSuccess ? '🎉 ALL INTEGRATION TESTS PASSED!' : '❌ SOME INTEGRATION TESTS FAILED'}`);
        
        if (overallSuccess) {
            console.log('\n✨ The AI menu integration is working correctly!');
            console.log('✨ Users can successfully click "Sử dụng thực đơn này" and the menu will be added to the system.');
            console.log('\n📋 Integrated Menu Summary:');
            console.log(`   ID: ${integratedMenu.id}`);
            console.log(`   Name: ${integratedMenu.name}`);
            console.log(`   Meals: ${integratedMenu.detail.length}`);
            console.log(`   Total Foods: ${integratedMenu.detail.reduce((sum, meal) => sum + meal.listFood.length, 0)}`);
            console.log(`   Energy: ${nutrition.total_energy} kcal`);
        }
        
        return overallSuccess;
        
    } catch (error) {
        console.error('\n❌ Integration test failed with error:', error.message);
        return false;
    }
}

// Run the test
if (require.main === module) {
    testMenuIntegration()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

module.exports = testMenuIntegration;
