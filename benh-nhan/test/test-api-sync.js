#!/usr/bin/env node

/**
 * Test API sync functionality
 * This script tests the sync-foods API endpoint
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000'; // Adjust if your server runs on different port

async function testAPISync() {
    console.log('🧪 Testing AI Menu API Sync');
    console.log('============================\n');

    try {
        // Test 1: Test connection
        console.log('1️⃣ Testing connection...');
        try {
            const response = await axios.get(`${BASE_URL}/api/ai-menu/test`, {
                headers: {
                    'Cookie': 'connect.sid=your-session-cookie' // You'll need to get this from browser
                }
            });
            
            console.log('✅ Connection test response:', response.data);
        } catch (error) {
            console.log('⚠️  Connection test failed (might need authentication):', error.response?.data || error.message);
        }

        // Test 2: Test sync (this will require authentication)
        console.log('\n2️⃣ Testing sync foods...');
        try {
            const response = await axios.post(`${BASE_URL}/api/ai-menu/sync-foods`, {}, {
                headers: {
                    'Cookie': 'connect.sid=your-session-cookie' // You'll need to get this from browser
                }
            });
            
            console.log('✅ Sync test response:', response.data);
        } catch (error) {
            console.log('⚠️  Sync test failed (might need authentication):', error.response?.data || error.message);
        }

        console.log('\n💡 To test with authentication:');
        console.log('1. Login to your app in browser');
        console.log('2. Open browser dev tools > Application > Cookies');
        console.log('3. Copy the connect.sid cookie value');
        console.log('4. Replace "your-session-cookie" in this script');
        console.log('5. Or test directly in browser at /test/ai-menu');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run test if this file is executed directly
if (require.main === module) {
    testAPISync().then(() => {
        process.exit(0);
    }).catch((error) => {
        console.error('Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = testAPISync;
