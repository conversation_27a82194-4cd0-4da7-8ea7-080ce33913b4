#!/usr/bin/env node

/**
 * Test script để kiểm tra AI có tạo đúng yêu cầu về số bữa ăn và năng lượng không
 */

const aiMenuService = require('../services/aiMenuService');

async function testAIMenuRequirements() {
    console.log('🧪 Testing AI Menu Requirements Compliance');
    console.log('='.repeat(60));
    
    try {
        // Test cases với các yêu cầu khác nhau
        const testCases = [
            {
                name: 'Test 1: 3 bữa ăn, 2000 kcal',
                request: {
                    requirements: 'Tạo thực đơn cân bằng dinh dưỡng cho người trưởng thành',
                    preferences: [],
                    restrictions: [],
                    meal_count: 3,
                    target_nutrition: {
                        energy: 2000,
                        protein: null,
                        carbohydrate: null,
                        lipid: null
                    }
                }
            },
            {
                name: 'Test 2: 5 bữa ăn, 1800 kcal (Tiểu đường)',
                request: {
                    requirements: 'T<PERSON><PERSON> thực đơn cho bệnh nhân tiểu đườ<PERSON>, 1800 kcal/ngày, ít đường, nhiều chất xơ',
                    preferences: ['ít đường', 'nhiều chất xơ'],
                    restrictions: ['đường', 'kẹo'],
                    meal_count: 5,
                    target_nutrition: {
                        energy: 1800,
                        protein: null,
                        carbohydrate: null,
                        lipid: null
                    }
                }
            },
            {
                name: 'Test 3: 4 bữa ăn, 1500 kcal (Giảm cân)',
                request: {
                    requirements: 'Tạo thực đơn giảm cân, ít calo, nhiều protein',
                    preferences: ['ít calo', 'nhiều protein'],
                    restrictions: ['đồ chiên', 'đồ ngọt'],
                    meal_count: 4,
                    target_nutrition: {
                        energy: 1500,
                        protein: null,
                        carbohydrate: null,
                        lipid: null
                    }
                }
            }
        ];
        
        let passedTests = 0;
        let totalTests = testCases.length;
        
        for (let i = 0; i < testCases.length; i++) {
            const testCase = testCases[i];
            console.log(`\n${testCase.name}`);
            console.log('-'.repeat(40));
            
            try {
                const result = await aiMenuService.generateSmartMenu(testCase.request);
                
                if (!result.success) {
                    console.log('❌ FAILED: Menu generation failed');
                    console.log('   Error:', result.message);
                    continue;
                }
                
                const menu = result.data.menu;
                const nutrition = result.data.nutrition_summary;
                
                // Kiểm tra số bữa ăn
                const expectedMeals = testCase.request.meal_count;
                const actualMeals = menu.detail.length;
                const mealsCorrect = actualMeals === expectedMeals;
                
                // Kiểm tra năng lượng
                const expectedEnergy = testCase.request.target_nutrition.energy;
                const actualEnergy = nutrition.total_energy;
                const energyDiff = Math.abs(actualEnergy - expectedEnergy);
                const energyCorrect = energyDiff <= 100; // Cho phép sai lệch 100 kcal
                
                // Kiểm tra cấu trúc courses
                let coursesCorrect = true;
                menu.detail.forEach((meal, index) => {
                    if (!meal.courses || !Array.isArray(meal.courses) || meal.courses.length === 0) {
                        coursesCorrect = false;
                        console.log(`   ⚠️  Meal ${index + 1} missing courses`);
                    }
                    
                    meal.listFood.forEach((food, foodIndex) => {
                        if (!food.course_id) {
                            coursesCorrect = false;
                            console.log(`   ⚠️  Food ${foodIndex + 1} in meal ${index + 1} missing course_id`);
                        }
                    });
                });
                
                // Hiển thị kết quả
                console.log(`   Meals: ${actualMeals}/${expectedMeals} ${mealsCorrect ? '✅' : '❌'}`);
                console.log(`   Energy: ${actualEnergy}/${expectedEnergy} kcal (diff: ${energyDiff}) ${energyCorrect ? '✅' : '❌'}`);
                console.log(`   Courses structure: ${coursesCorrect ? '✅' : '❌'}`);
                
                // Hiển thị chi tiết bữa ăn
                console.log('   Meals detail:');
                menu.detail.forEach((meal, index) => {
                    console.log(`     ${index + 1}. ${meal.name}: ${meal.listFood.length} foods, ${meal.courses?.length || 0} courses`);
                });
                
                if (mealsCorrect && energyCorrect && coursesCorrect) {
                    console.log('   🎉 PASSED: All requirements met!');
                    passedTests++;
                } else {
                    console.log('   ❌ FAILED: Some requirements not met');
                }
                
            } catch (error) {
                console.log('❌ FAILED: Exception occurred');
                console.log('   Error:', error.message);
            }
        }
        
        console.log('\n' + '='.repeat(60));
        console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
        
        if (passedTests === totalTests) {
            console.log('🎉 All tests passed! AI is generating menus correctly.');
        } else {
            console.log('⚠️  Some tests failed. AI needs optimization.');
            
            console.log('\n🔧 Recommendations:');
            console.log('1. Check if AI prompt is clear about meal count requirements');
            console.log('2. Verify energy calculation logic in recalculateNutrition');
            console.log('3. Ensure validateAndFixMenuStructure is working properly');
            console.log('4. Consider increasing retry attempts for energy target');
        }
        
        return passedTests === totalTests;
        
    } catch (error) {
        console.error('\n❌ Test suite failed with error:', error.message);
        return false;
    }
}

// Run the test
if (require.main === module) {
    testAIMenuRequirements()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

module.exports = testAIMenuRequirements;
