// File test để kiểm tra role multi-select (không cần database)
const commonService = require('../services/commonService');
const securityService = require('../services/securityService');

async function testRoleMultiSelect() {
    console.log('=== Testing Role Multi-Select ===');
    
    try {
        // Test 1: Kiểm tra validateInput với array
        console.log('\n1. Testing validateInput with array type...');
        const testData = {
            fullname: 'Test User',
            email: '<EMAIL>',
            role: [1, 2, 3] // Array of roles
        };
        
        const validateRules = [
            { field: "fullname", type: "string", required: true, message: "<PERSON>ui lòng nhập họ tên!" },
            { field: "email", type: "string", required: true, message: "Vui lòng nhập email!" },
            { field: "role", type: "array", required: true, message: "<PERSON>ui lòng chọn quyền!" }
        ];
        
        const errors = securityService.validateInput(testData, validateRules, { returnType: 'array' });
        console.log('Validation errors:', errors);
        console.log('Validation passed:', errors.length === 0);
        
        // Test 2: Kiểm tra xử lý role trong adminController logic
        console.log('\n2. Testing adminController role processing...');
        
        // Simulate role_ids array
        const role_ids = [1, 2, 3];
        console.log('role_ids:', role_ids);
        console.log('role_ids is array:', Array.isArray(role_ids));
        
        // Test role processing logic
        const processedRoleIds = Array.isArray(role_ids) ? role_ids : [role_ids];
        console.log('processedRoleIds:', processedRoleIds);
        
        // Test adding roles to database (simulation)
        console.log('Simulating adding roles to database:');
        for (let role_id of processedRoleIds) {
            console.log(`  - Adding role_id: ${role_id}`);
        }
        
        // Test 3: Kiểm tra role checking logic
        console.log('\n3. Testing role checking logic...');
        const mockUser = {
            id: 1,
            role_id: [1, 3, 5],
            isAdmin: false
        };
        
        console.log('Mock user:', mockUser);
        console.log('User has role 1:', mockUser.role_id.includes(1));
        console.log('User has role 2:', mockUser.role_id.includes(2));
        console.log('User has role 3:', mockUser.role_id.includes(3));
        
        // Test permission checking
        const hasPermission = mockUser.isAdmin || mockUser.role_id.includes(1);
        console.log('User has admin permission:', hasPermission);
        
        // Test multiple role checking
        const allowedRoles = [1, 3];
        const hasAnyAllowedRole = mockUser.isAdmin || allowedRoles.some(role => mockUser.role_id.includes(role));
        console.log('User has any allowed role:', hasAnyAllowedRole);
        
        // Test 4: Kiểm tra logic xử lý role từ form
        console.log('\n4. Testing form role processing...');
        
        // Simulate form data
        const formData = {
            fullname: 'Test User',
            email: '<EMAIL>',
            role: [1, 2, 3] // From multi-select
        };
        
        console.log('Form data:', formData);
        
        // Process role data like in adminController
        const role_ids_from_form = Array.isArray(formData.role) ? formData.role : [formData.role];
        console.log('Processed role_ids:', role_ids_from_form);
        
        // Test 5: Kiểm tra logic setValue cho virtual-select
        console.log('\n5. Testing virtual-select setValue logic...');
        
        const userData = {
            role: [1, 3, 5] // From database
        };
        
        // Logic like in setDataFormUser
        if (Array.isArray(userData.role)) {
            console.log('Setting virtual-select with array:', userData.role);
        } else {
            console.log('Setting virtual-select with single value as array:', [userData.role]);
        }
        
        // Test 6: Kiểm tra logic reset form
        console.log('\n6. Testing form reset logic...');
        console.log('Setting default role as array:', ['2']);
        
    } catch (error) {
        console.error('Error testing role multi-select:', error);
    }
    
    console.log('\n=== Role Multi-Select Test Completed ===');
}

// Chạy test
testRoleMultiSelect(); 