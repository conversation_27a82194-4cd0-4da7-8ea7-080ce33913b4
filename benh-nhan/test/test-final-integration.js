const aiMenuService = require('../services/aiMenuService');
const dishService = require('../services/dishService');

async function testFinalIntegration() {
    console.log('🧪 Final Integration Test...\n');

    try {
        // 1. Test dish sync
        console.log('1️⃣ Testing dish sync to Pinecone...');
        const syncResult = await dishService.syncDishesToPinecone();
        
        if (syncResult.success) {
            console.log(`   ✅ Dish sync successful: ${syncResult.message || 'Completed'}`);
        } else {
            console.log(`   ❌ Dish sync failed: ${syncResult.message}`);
        }

        // 2. Test AI menu generation with dishes
        console.log('\n2️⃣ Testing AI menu generation with dishes...');
        const menuResult = await aiMenuService.generateSmartMenu({
            requirements: 'Tạo thực đơn có canh và cơm cho bệnh nhân tiểu đường, 1800 kcal',
            meal_count: 5,
            target_nutrition: { energy: 1800 }
        });

        if (menuResult.success) {
            const menu = menuResult.data.menu;
            console.log(`   ✅ Menu generated: ${menu.name}`);
            console.log(`   📊 Meals: ${menu.detail.length}`);
            console.log(`   📊 Total energy: ${menuResult.data.nutrition_summary.energy} kcal`);
            
            // Check meal IDs
            const mealIds = menu.detail.map(m => m.id);
            console.log(`   📊 Meal IDs: [${mealIds.join(', ')}]`);
            console.log(`   📊 Expected: [3, 4, 5, 6, 7]`);
            
            if (JSON.stringify(mealIds) === JSON.stringify([3, 4, 5, 6, 7])) {
                console.log('   ✅ MenuTime mapping CORRECT');
            } else {
                console.log('   ❌ MenuTime mapping INCORRECT');
            }

            // Check food diversity
            let totalFoods = 0;
            let mainMealsWithMultipleFoods = 0;
            
            menu.detail.forEach(meal => {
                const foodCount = meal.listFood.length;
                totalFoods += foodCount;
                
                console.log(`   🍽️  ${meal.name}: ${foodCount} món`);
                
                if (['Sáng', 'Trưa', 'Tối'].includes(meal.name) && foodCount >= 3) {
                    mainMealsWithMultipleFoods++;
                }
            });
            
            console.log(`   📊 Total foods: ${totalFoods}`);
            console.log(`   📊 Main meals with 3+ foods: ${mainMealsWithMultipleFoods}/3`);
            
            if (mainMealsWithMultipleFoods >= 2) {
                console.log('   ✅ Food diversity GOOD');
            } else {
                console.log('   ⚠️  Food diversity needs improvement');
            }

        } else {
            console.log(`   ❌ Menu generation failed: ${menuResult.message}`);
        }

        // 3. Test 3-meal menu
        console.log('\n3️⃣ Testing 3-meal menu...');
        const menu3Result = await aiMenuService.generateSmartMenu({
            requirements: 'Tạo thực đơn 3 bữa ăn cân bằng dinh dưỡng',
            meal_count: 3,
            target_nutrition: { energy: 1800 }
        });

        if (menu3Result.success) {
            const mealIds = menu3Result.data.menu.detail.map(m => m.id);
            const mealNames = menu3Result.data.menu.detail.map(m => m.name);
            console.log(`   📊 3-meal IDs: [${mealIds.join(', ')}]`);
            console.log(`   📊 3-meal Names: [${mealNames.join(', ')}]`);
            console.log(`   📊 Expected: [3, 5, 7] (Sáng, Trưa, Tối)`);
            
            if (JSON.stringify(mealIds) === JSON.stringify([3, 5, 7])) {
                console.log('   ✅ 3-meal mapping CORRECT');
            } else {
                console.log('   ❌ 3-meal mapping INCORRECT');
            }
        }

        console.log('\n🎉 Final integration test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testFinalIntegration();
