const dishService = require('../services/dishService');
const pineconeService = require('../services/pineconeService');
const aiMenuService = require('../services/aiMenuService');

class DishIntegrationTest {
    constructor() {
        this.testResults = [];
    }

    async runAllTests() {
        console.log('🧪 Starting Dish Integration Tests...\n');

        await this.testDishService();
        await this.testDishPineconeSync();
        await this.testDishSearch();
        await this.testAIMenuWithDishes();
        await this.testMenuTimeMapping();

        this.printResults();
    }

    async testDishService() {
        console.log('1️⃣ Testing Dish Service...');
        
        try {
            // Test get all dishes with nutrition
            const dishesResult = await dishService.getAllDishesWithNutrition();
            
            if (dishesResult.success && dishesResult.data && dishesResult.data.length > 0) {
                const dish = dishesResult.data[0];
                console.log(`   ✅ Found ${dishesResult.data.length} dishes`);
                console.log(`   📊 Sample dish: ${dish.name} - ${dish.total_energy}kcal`);
                
                // Test individual dish
                const singleDishResult = await dishService.getDishWithNutrition(dish.id);
                if (singleDishResult.success) {
                    console.log(`   ✅ Individual dish fetch works`);
                    this.testResults.push({ test: 'Dish Service', status: 'PASS' });
                } else {
                    console.log(`   ❌ Individual dish fetch failed`);
                    this.testResults.push({ test: 'Dish Service', status: 'FAIL', error: singleDishResult.message });
                }
            } else {
                console.log(`   ⚠️  No dishes found or service failed`);
                this.testResults.push({ test: 'Dish Service', status: 'WARN', error: 'No dishes found' });
            }
        } catch (error) {
            console.log(`   ❌ Dish service error: ${error.message}`);
            this.testResults.push({ test: 'Dish Service', status: 'FAIL', error: error.message });
        }
    }

    async testDishPineconeSync() {
        console.log('\n2️⃣ Testing Dish Pinecone Sync...');
        
        try {
            const syncResult = await dishService.syncDishesToPinecone();
            
            if (syncResult.success) {
                console.log(`   ✅ Dish sync successful: ${syncResult.message}`);
                this.testResults.push({ test: 'Dish Pinecone Sync', status: 'PASS' });
            } else {
                console.log(`   ❌ Dish sync failed: ${syncResult.message}`);
                this.testResults.push({ test: 'Dish Pinecone Sync', status: 'FAIL', error: syncResult.message });
            }
        } catch (error) {
            console.log(`   ❌ Dish sync error: ${error.message}`);
            this.testResults.push({ test: 'Dish Pinecone Sync', status: 'FAIL', error: error.message });
        }
    }

    async testDishSearch() {
        console.log('\n3️⃣ Testing Dish Search...');
        
        try {
            // Test dish search
            const dishSearchResult = await pineconeService.searchDishes('cơm gà', { topK: 5 });
            
            if (dishSearchResult.success && dishSearchResult.data.length > 0) {
                console.log(`   ✅ Dish search found ${dishSearchResult.data.length} results`);
                console.log(`   🍽️  Top result: ${dishSearchResult.data[0].name} (score: ${dishSearchResult.data[0].score.toFixed(3)})`);
                
                // Test combined search
                const combinedResult = await pineconeService.searchFoodsAndDishes('gà', { topK: 10 });
                
                if (combinedResult.success) {
                    const foods = combinedResult.data.filter(item => item.type === 'food').length;
                    const dishes = combinedResult.data.filter(item => item.type === 'dish').length;
                    console.log(`   ✅ Combined search: ${foods} foods + ${dishes} dishes`);
                    this.testResults.push({ test: 'Dish Search', status: 'PASS' });
                } else {
                    console.log(`   ❌ Combined search failed`);
                    this.testResults.push({ test: 'Dish Search', status: 'FAIL', error: 'Combined search failed' });
                }
            } else {
                console.log(`   ⚠️  No dishes found in search`);
                this.testResults.push({ test: 'Dish Search', status: 'WARN', error: 'No dishes found' });
            }
        } catch (error) {
            console.log(`   ❌ Dish search error: ${error.message}`);
            this.testResults.push({ test: 'Dish Search', status: 'FAIL', error: error.message });
        }
    }

    async testAIMenuWithDishes() {
        console.log('\n4️⃣ Testing AI Menu with Dishes...');
        
        try {
            const menuRequest = {
                requirements: 'Tạo thực đơn có món cơm gà và canh rau cho bệnh nhân tiểu đường',
                preferences: ['món Việt Nam', 'ít đường'],
                restrictions: ['không cay'],
                meal_count: 3,
                target_nutrition: {
                    energy: 1500
                }
            };

            const aiResult = await aiMenuService.generateSmartMenu(menuRequest);
            
            if (aiResult.success && aiResult.data && aiResult.data.menu) {
                const menu = aiResult.data.menu;
                console.log(`   ✅ AI Menu generated: ${menu.name}`);
                console.log(`   🍽️  Meals: ${menu.detail.length}`);
                
                // Check if dishes are included
                let dishCount = 0;
                let foodCount = 0;
                
                menu.detail.forEach(meal => {
                    if (meal.listFood) {
                        meal.listFood.forEach(food => {
                            if (food.id_food && food.id_food.toString().startsWith('dish_')) {
                                dishCount++;
                            } else {
                                foodCount++;
                            }
                        });
                    }
                });
                
                console.log(`   📊 Menu contains: ${foodCount} foods + ${dishCount} dishes`);
                
                // Check menuTime mapping
                const menuTimeIds = menu.detail.map(meal => meal.id);
                const expectedIds = [3, 5, 7]; // Sáng, Trưa, Tối
                const correctMapping = menuTimeIds.every(id => expectedIds.includes(id));
                
                if (correctMapping) {
                    console.log(`   ✅ MenuTime mapping correct: [${menuTimeIds.join(', ')}]`);
                    this.testResults.push({ test: 'AI Menu with Dishes', status: 'PASS' });
                } else {
                    console.log(`   ❌ MenuTime mapping incorrect: [${menuTimeIds.join(', ')}]`);
                    this.testResults.push({ test: 'AI Menu with Dishes', status: 'FAIL', error: 'Incorrect menuTime mapping' });
                }
            } else {
                console.log(`   ❌ AI Menu generation failed: ${aiResult.message}`);
                this.testResults.push({ test: 'AI Menu with Dishes', status: 'FAIL', error: aiResult.message });
            }
        } catch (error) {
            console.log(`   ❌ AI Menu error: ${error.message}`);
            this.testResults.push({ test: 'AI Menu with Dishes', status: 'FAIL', error: error.message });
        }
    }

    async testMenuTimeMapping() {
        console.log('\n5️⃣ Testing MenuTime Mapping...');
        
        try {
            const testCases = [
                { mealCount: 3, expectedIds: [3, 5, 7], expectedNames: ['Sáng', 'Trưa', 'Tối'] },
                { mealCount: 5, expectedIds: [3, 4, 5, 6, 7], expectedNames: ['Sáng', 'Phụ 1', 'Trưa', 'Phụ 2', 'Tối'] }
            ];

            for (const testCase of testCases) {
                const menuRequest = {
                    requirements: `Tạo thực đơn ${testCase.mealCount} bữa ăn`,
                    preferences: [],
                    restrictions: [],
                    meal_count: testCase.mealCount,
                    target_nutrition: { energy: 1800 }
                };

                const aiResult = await aiMenuService.generateSmartMenu(menuRequest);
                
                if (aiResult.success && aiResult.data && aiResult.data.menu) {
                    const menu = aiResult.data.menu;
                    const actualIds = menu.detail.map(meal => meal.id).sort((a, b) => a - b);
                    const actualNames = menu.detail.map(meal => meal.name);
                    
                    const idsMatch = JSON.stringify(actualIds) === JSON.stringify(testCase.expectedIds);
                    const namesMatch = testCase.expectedNames.every(name => actualNames.includes(name));
                    
                    if (idsMatch && namesMatch) {
                        console.log(`   ✅ ${testCase.mealCount} meals: IDs [${actualIds.join(', ')}] ✓`);
                    } else {
                        console.log(`   ❌ ${testCase.mealCount} meals: Expected [${testCase.expectedIds.join(', ')}], got [${actualIds.join(', ')}]`);
                        this.testResults.push({ 
                            test: `MenuTime Mapping (${testCase.mealCount} meals)`, 
                            status: 'FAIL', 
                            error: `Expected ${testCase.expectedIds}, got ${actualIds}` 
                        });
                        return;
                    }
                } else {
                    console.log(`   ❌ ${testCase.mealCount} meals generation failed`);
                    this.testResults.push({ 
                        test: `MenuTime Mapping (${testCase.mealCount} meals)`, 
                        status: 'FAIL', 
                        error: 'Menu generation failed' 
                    });
                    return;
                }
            }
            
            this.testResults.push({ test: 'MenuTime Mapping', status: 'PASS' });
        } catch (error) {
            console.log(`   ❌ MenuTime mapping error: ${error.message}`);
            this.testResults.push({ test: 'MenuTime Mapping', status: 'FAIL', error: error.message });
        }
    }

    printResults() {
        console.log('\n📊 TEST RESULTS SUMMARY');
        console.log('========================');
        
        const passed = this.testResults.filter(r => r.status === 'PASS').length;
        const failed = this.testResults.filter(r => r.status === 'FAIL').length;
        const warned = this.testResults.filter(r => r.status === 'WARN').length;
        
        this.testResults.forEach(result => {
            const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
            console.log(`${icon} ${result.test}: ${result.status}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });
        
        console.log(`\n📈 Summary: ${passed} passed, ${failed} failed, ${warned} warnings`);
        
        if (failed === 0) {
            console.log('🎉 All critical tests passed! Dish integration is working correctly.');
        } else {
            console.log('⚠️  Some tests failed. Please check the errors above.');
        }
    }
}

// Run tests
async function runTests() {
    const tester = new DishIntegrationTest();
    await tester.runAllTests();
}

if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = DishIntegrationTest;
