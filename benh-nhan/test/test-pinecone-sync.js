#!/usr/bin/env node

/**
 * Test script for Pinecone food sync
 * This script tests the food synchronization with the corrected 1024 dimensions
 */

const pineconeService = require('../services/pineconeService');
const commonService = require('../services/commonService');

async function testPineconeSync() {
    console.log('🧪 Testing Pinecone Food Sync with 1024 dimensions');
    console.log('==================================================\n');

    try {
        // Step 1: Initialize Pinecone service
        console.log('1️⃣ Initializing Pinecone service...');
        const initResult = await pineconeService.initialize();
        
        if (!initResult) {
            console.error('❌ Failed to initialize Pinecone service');
            console.log('💡 Check your PINECONE_API_KEY and PINECONE_ENVIRONMENT in .env');
            return;
        }
        console.log('✅ Pinecone service initialized successfully\n');

        // Step 2: Test with sample food data
        console.log('2️⃣ Testing with sample food data...');
        const sampleFood = {
            id: 999999,
            name: 'Cơm trắng test',
            ten: 'White rice test',
            type: 'cooked',
            type_year: '2017',
            energy: 130,
            protein: 2.7,
            carbohydrate: 29.0,
            lipid: 0.3,
            fiber: 0.4,
            calci: 10,
            fe: 0.8,
            vitamin_c: 0
        };

        // Test embedding generation
        const description = pineconeService.createFoodDescription(sampleFood);
        console.log(`📝 Generated description: ${description.substring(0, 100)}...`);
        
        const embedding = pineconeService.generateSimpleEmbedding(description);
        console.log(`🔢 Generated embedding with ${embedding.length} dimensions`);
        
        if (embedding.length !== 1024) {
            console.error(`❌ Wrong embedding dimension: ${embedding.length}, expected 1024`);
            return;
        }
        console.log('✅ Embedding dimension is correct (1024)\n');

        // Step 3: Test upload single food
        console.log('3️⃣ Testing single food upload...');
        const uploadResult = await pineconeService.upsertFood(sampleFood);
        
        if (uploadResult.success) {
            console.log('✅ Single food upload successful');
        } else {
            console.error(`❌ Single food upload failed: ${uploadResult.message}`);
            return;
        }

        // Step 4: Test search
        console.log('\n4️⃣ Testing food search...');
        const searchResult = await pineconeService.searchFoods('cơm trắng', { topK: 5 });
        
        if (searchResult.success) {
            console.log(`✅ Search successful, found ${searchResult.data.length} results`);
            if (searchResult.data.length > 0) {
                console.log(`   Top result: ${searchResult.data[0].name} (score: ${searchResult.data[0].score})`);
            }
        } else {
            console.error(`❌ Search failed: ${searchResult.message}`);
        }

        // Step 5: Test with real database data (small batch)
        console.log('\n5️⃣ Testing with real database data (small batch)...');
        const sql = 'SELECT * FROM food_info WHERE active = 1 ORDER BY id LIMIT 5';
        const dbResult = await commonService.getListTable(sql, []);
        
        if (dbResult.success && dbResult.data && dbResult.data.length > 0) {
            console.log(`📊 Found ${dbResult.data.length} foods in database`);
            
            const batchUploadResult = await pineconeService.upsertFoods(dbResult.data);
            
            if (batchUploadResult.success) {
                console.log('✅ Batch upload successful');
            } else {
                console.error(`❌ Batch upload failed: ${batchUploadResult.message}`);
                return;
            }
        } else {
            console.error('❌ Failed to fetch foods from database');
            return;
        }

        // Step 6: Test search with real data
        console.log('\n6️⃣ Testing search with real data...');
        const realSearchResult = await pineconeService.searchFoods('thịt gà protein', { topK: 3 });
        
        if (realSearchResult.success) {
            console.log(`✅ Real data search successful, found ${realSearchResult.data.length} results`);
            realSearchResult.data.forEach((food, index) => {
                console.log(`   ${index + 1}. ${food.name} - ${food.energy}kcal, ${food.protein}g protein (score: ${food.score.toFixed(3)})`);
            });
        } else {
            console.error(`❌ Real data search failed: ${realSearchResult.message}`);
        }

        // Step 7: Get index stats
        console.log('\n7️⃣ Getting index statistics...');
        const statsResult = await pineconeService.getIndexStats();
        
        if (statsResult.success) {
            console.log('✅ Index stats retrieved successfully');
            console.log(`   Total vectors: ${statsResult.data.totalVectorCount || 'N/A'}`);
            console.log(`   Index fullness: ${statsResult.data.indexFullness || 'N/A'}`);
        } else {
            console.error(`❌ Failed to get index stats: ${statsResult.message}`);
        }

        // Cleanup test data
        console.log('\n8️⃣ Cleaning up test data...');
        await pineconeService.deleteFood(999999);
        console.log('✅ Test data cleaned up');

        console.log('\n🎉 All tests passed! Pinecone sync is working correctly.');
        console.log('💡 You can now run the full sync: POST /api/ai-menu/sync-foods');

    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Check your Pinecone API key and environment in .env');
        console.log('2. Ensure your Pinecone index has 1024 dimensions');
        console.log('3. Check database connection');
        console.log('4. Verify food_info table has data');
    }
}

// Run test if this file is executed directly
if (require.main === module) {
    testPineconeSync().then(() => {
        process.exit(0);
    }).catch((error) => {
        console.error('Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = testPineconeSync;
