const axios = require('axios');

// Test chức năng export Excel
async function testExport() {
    try {
        console.log('🧪 Bắt đầu test chức năng export Excel...');
        
        // Test export cho viem-gan-mt1
        console.log('📊 Test export viem-gan-mt1...');
        const response = await axios.get('http://localhost:3000/patient/export/viem-gan-mt1', {
            responseType: 'arraybuffer',
            headers: {
                'Cookie': 'your-session-cookie-here' // Cần thay thế bằng cookie thực tế
            }
        });
        
        if (response.status === 200 && response.data.byteLength > 0) {
            console.log(`✅ Export thành công - Kích thước file: ${response.data.byteLength} bytes`);
            console.log(`📁 Content-Type: ${response.headers['content-type']}`);
            console.log(`📄 Content-Disposition: ${response.headers['content-disposition']}`);
        } else {
            console.log('❌ Export thất bại - <PERSON>hông có dữ liệu');
        }
        
    } catch (error) {
        if (error.response) {
            console.error('❌ Lỗi HTTP:', error.response.status, error.response.data.toString());
        } else {
            console.error('❌ Lỗi kết nối:', error.message);
        }
    }
}

// Chạy test
if (require.main === module) {
    testExport();
}

module.exports = { testExport };

