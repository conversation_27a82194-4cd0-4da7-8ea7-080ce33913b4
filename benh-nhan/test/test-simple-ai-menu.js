const aiMenuService = require('../services/aiMenuService');

async function testSimpleAIMenu() {
    console.log('🧪 Testing Simple AI Menu Generation...\n');

    try {
        // Test với yêu cầu đơn giản
        console.log('1️⃣ Testing simple menu...');
        const request = {
            requirements: "Tạo thực đơn cân bằng dinh dưỡng",
            preferences: [],
            restrictions: [],
            meal_count: 3,
            target_nutrition: {
                energy: 1200,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };

        console.log('Request:', JSON.stringify(request, null, 2));

        const result = await aiMenuService.generateSmartMenu(request);

        if (result.success) {
            const menu = result.data.menu;
            const nutrition = result.data.nutrition_summary;

            console.log(`   ✅ Menu generated: ${menu.name}`);
            console.log(`   📊 Energy: ${nutrition.energy} kcal (target: 1200)`);

            // Check for invalid IDs
            let invalidIds = 0;
            let validItems = 0;

            menu.detail.forEach(meal => {
                console.log(`\n   🍽️  ${meal.name}:`);
                meal.listFood.forEach(food => {
                    if (food.energy === 0 && food.protein === 0) {
                        invalidIds++;
                        console.log(`      - ❌ ${food.name} (ID: ${food.id_food}) - INVALID`);
                    } else {
                        validItems++;
                        console.log(`      - ✅ ${food.name} (${food.energy} kcal)`);
                    }
                });
            });

            console.log(`\n   📊 Valid items: ${validItems}`);
            console.log(`   📊 Invalid IDs: ${invalidIds}`);

            if (invalidIds === 0) {
                console.log('   🎉 SUCCESS: All IDs are valid!');
            } else {
                console.log('   ⚠️  ISSUE: Some IDs are invalid');
            }

            if (nutrition.energy > 0) {
                console.log('   🎉 SUCCESS: Nutrition calculation working!');
            } else {
                console.log('   ⚠️  ISSUE: Nutrition calculation failed');
            }

        } else {
            console.log(`   ❌ Menu generation failed: ${result.message}`);
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testSimpleAIMenu();
