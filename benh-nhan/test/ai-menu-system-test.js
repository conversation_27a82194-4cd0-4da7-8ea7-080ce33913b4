const aiMenuService = require('../services/aiMenuService');
const pineconeService = require('../services/pineconeService');
const geminiService = require('../services/geminiService');
const commonService = require('../services/commonService');

// Test configuration
const TEST_CONFIG = {
    TIMEOUT: 30000, // 30 seconds
    SAMPLE_FOOD_COUNT: 10,
    TEST_CASES: [
        {
            name: 'Balanced Menu',
            requirements: 'Tạo thực đơn cân bằng dinh dưỡng cho người trưởng thành khỏe mạnh',
            preferences: ['đa dạng thực phẩm'],
            restrictions: [],
            target_nutrition: { energy: 2000, protein: 80 }
        },
        {
            name: 'Diabetes Menu',
            requirements: 'Tạo thực đơn cho bệnh nhân tiểu đường, ít đường, nhiều chất xơ',
            preferences: ['ít đường', 'nhiều chất xơ'],
            restrictions: ['đường', 'kẹo'],
            target_nutrition: { energy: 1800, protein: 90 }
        },
        {
            name: 'Weight Loss Menu',
            requirements: 'Tạo thực đơn giảm cân, ít calo, nhiều protein',
            preferences: ['ít calo', 'nhiều protein'],
            restrictions: ['đồ chiên'],
            target_nutrition: { energy: 1500, protein: 100 }
        }
    ]
};

class AIMenuSystemTest {
    constructor() {
        this.results = {
            total: 0,
            passed: 0,
            failed: 0,
            errors: []
        };
    }

    async runAllTests() {
        console.log('🚀 Starting AI Menu System Tests...\n');
        
        try {
            // Test 1: Service Initialization
            await this.testServiceInitialization();
            
            // Test 2: Database Connection
            await this.testDatabaseConnection();
            
            // Test 3: Pinecone Service
            await this.testPineconeService();
            
            // Test 4: Gemini Service
            await this.testGeminiService();
            
            // Test 5: AI Menu Service
            await this.testAIMenuService();
            
            // Test 6: Menu Generation
            await this.testMenuGeneration();
            
            // Test 7: Error Handling
            await this.testErrorHandling();
            
            // Test 8: Performance
            await this.testPerformance();
            
        } catch (error) {
            console.error('❌ Test suite failed:', error);
            this.results.errors.push(`Test suite error: ${error.message}`);
        }
        
        this.printResults();
    }

    async testServiceInitialization() {
        console.log('📋 Testing Service Initialization...');
        
        try {
            const result = await aiMenuService.initialize();
            this.assert(result === true, 'AI Menu Service should initialize successfully');
            console.log('✅ Service initialization passed\n');
        } catch (error) {
            this.fail('Service initialization failed', error);
        }
    }

    async testDatabaseConnection() {
        console.log('📋 Testing Database Connection...');
        
        try {
            const sql = 'SELECT COUNT(*) as count FROM food_info WHERE active = 1 LIMIT 1';
            const result = await commonService.getListTable(sql, []);
            
            this.assert(result.success === true, 'Database query should succeed');
            this.assert(result.data && result.data.length > 0, 'Database should return data');
            
            console.log('✅ Database connection passed\n');
        } catch (error) {
            this.fail('Database connection failed', error);
        }
    }

    async testPineconeService() {
        console.log('📋 Testing Pinecone Service...');
        
        try {
            // Test initialization
            const initResult = await pineconeService.initialize();
            this.assert(initResult === true, 'Pinecone should initialize');
            
            // Test sample food upload (if we have sample data)
            const sampleFood = {
                id: 999999,
                name: 'Test Food',
                type: 'raw',
                type_year: '2017',
                energy: 100,
                protein: 5,
                carbohydrate: 20,
                lipid: 2,
                fiber: 3
            };
            
            const uploadResult = await pineconeService.upsertFood(sampleFood);
            this.assert(uploadResult.success === true, 'Should upload sample food to Pinecone');
            
            // Test search
            const searchResult = await pineconeService.searchFoods('test food', { topK: 5 });
            this.assert(searchResult.success === true, 'Should search foods in Pinecone');
            
            // Cleanup
            await pineconeService.deleteFood(999999);
            
            console.log('✅ Pinecone service passed\n');
        } catch (error) {
            this.fail('Pinecone service failed', error);
        }
    }

    async testGeminiService() {
        console.log('📋 Testing Gemini Service...');
        
        try {
            // Test initialization
            const initResult = await geminiService.initialize();
            this.assert(initResult === true, 'Gemini should initialize');
            
            // Test simple menu generation with mock data
            const mockFoods = [
                {
                    food_id: 1,
                    name: 'Cơm trắng',
                    type: 'cooked',
                    energy: 130,
                    protein: 2.7,
                    carbohydrate: 29,
                    lipid: 0.3,
                    fiber: 0.4
                },
                {
                    food_id: 2,
                    name: 'Thịt gà',
                    type: 'cooked',
                    energy: 165,
                    protein: 31,
                    carbohydrate: 0,
                    lipid: 3.6,
                    fiber: 0
                }
            ];
            
            const menuResult = await geminiService.generateMenu(
                'Tạo thực đơn đơn giản với cơm và thịt gà',
                mockFoods,
                { energy: 500, protein: 30 }
            );
            
            this.assert(menuResult.success === true, 'Should generate menu with Gemini');
            this.assert(menuResult.data && menuResult.data.menu, 'Should return menu data');
            
            console.log('✅ Gemini service passed\n');
        } catch (error) {
            this.fail('Gemini service failed', error);
        }
    }

    async testAIMenuService() {
        console.log('📋 Testing AI Menu Service...');
        
        try {
            // Test initialization
            const initResult = await aiMenuService.initialize();
            this.assert(initResult === true, 'AI Menu Service should initialize');
            
            // Test system stats
            const statsResult = await aiMenuService.getSystemStats();
            this.assert(statsResult.success === true, 'Should get system stats');
            
            console.log('✅ AI Menu Service passed\n');
        } catch (error) {
            this.fail('AI Menu Service failed', error);
        }
    }

    async testMenuGeneration() {
        console.log('📋 Testing Menu Generation...');
        
        for (const testCase of TEST_CONFIG.TEST_CASES) {
            try {
                console.log(`  Testing: ${testCase.name}`);
                
                const startTime = Date.now();
                const result = await aiMenuService.generateSmartMenu(testCase);
                const duration = Date.now() - startTime;
                
                this.assert(result.success === true, `${testCase.name} should generate successfully`);
                this.assert(result.data && result.data.menu, `${testCase.name} should return menu data`);
                this.assert(result.data.nutrition_summary, `${testCase.name} should return nutrition summary`);
                
                // Validate menu structure
                const menu = result.data.menu;
                this.assert(menu.name && menu.name.length > 0, `${testCase.name} should have menu name`);
                this.assert(Array.isArray(menu.detail), `${testCase.name} should have detail array`);
                this.assert(menu.detail.length > 0, `${testCase.name} should have at least one meal`);
                
                // Validate meals
                menu.detail.forEach((meal, index) => {
                    this.assert(meal.name && meal.name.length > 0, `${testCase.name} meal ${index + 1} should have name`);
                    this.assert(Array.isArray(meal.listFood), `${testCase.name} meal ${index + 1} should have listFood array`);
                });
                
                console.log(`    ✅ ${testCase.name} passed (${duration}ms)`);
                
            } catch (error) {
                this.fail(`Menu generation failed for ${testCase.name}`, error);
            }
        }
        
        console.log('✅ Menu generation tests passed\n');
    }

    async testErrorHandling() {
        console.log('📋 Testing Error Handling...');
        
        try {
            // Test with empty requirements
            const emptyResult = await aiMenuService.generateSmartMenu({
                requirements: '',
                preferences: [],
                restrictions: []
            });
            this.assert(emptyResult.success === false, 'Should fail with empty requirements');
            
            // Test with invalid nutrition targets
            const invalidResult = await aiMenuService.generateSmartMenu({
                requirements: 'Test menu',
                target_nutrition: { energy: -1000 }
            });
            // This might still succeed depending on AI handling, so we just check it doesn't crash
            
            console.log('✅ Error handling tests passed\n');
        } catch (error) {
            this.fail('Error handling test failed', error);
        }
    }

    async testPerformance() {
        console.log('📋 Testing Performance...');
        
        try {
            const testCase = TEST_CONFIG.TEST_CASES[0]; // Use balanced menu
            const iterations = 3;
            const times = [];
            
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                const result = await aiMenuService.generateSmartMenu(testCase);
                const duration = Date.now() - startTime;
                
                if (result.success) {
                    times.push(duration);
                }
            }
            
            if (times.length > 0) {
                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                const maxTime = Math.max(...times);
                const minTime = Math.min(...times);
                
                console.log(`    Average time: ${avgTime.toFixed(2)}ms`);
                console.log(`    Min time: ${minTime}ms`);
                console.log(`    Max time: ${maxTime}ms`);
                
                this.assert(avgTime < TEST_CONFIG.TIMEOUT, `Average response time should be under ${TEST_CONFIG.TIMEOUT}ms`);
            }
            
            console.log('✅ Performance tests passed\n');
        } catch (error) {
            this.fail('Performance test failed', error);
        }
    }

    assert(condition, message) {
        this.results.total++;
        if (condition) {
            this.results.passed++;
        } else {
            this.results.failed++;
            this.results.errors.push(message);
            throw new Error(message);
        }
    }

    fail(message, error) {
        this.results.total++;
        this.results.failed++;
        const errorMsg = `${message}: ${error ? error.message : 'Unknown error'}`;
        this.results.errors.push(errorMsg);
        console.log(`❌ ${errorMsg}\n`);
    }

    printResults() {
        console.log('📊 Test Results Summary:');
        console.log('========================');
        console.log(`Total tests: ${this.results.total}`);
        console.log(`Passed: ${this.results.passed}`);
        console.log(`Failed: ${this.results.failed}`);
        console.log(`Success rate: ${((this.results.passed / this.results.total) * 100).toFixed(2)}%`);
        
        if (this.results.errors.length > 0) {
            console.log('\n❌ Errors:');
            this.results.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error}`);
            });
        }
        
        if (this.results.failed === 0) {
            console.log('\n🎉 All tests passed!');
        } else {
            console.log(`\n⚠️  ${this.results.failed} test(s) failed.`);
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new AIMenuSystemTest();
    tester.runAllTests().then(() => {
        process.exit(tester.results.failed > 0 ? 1 : 0);
    }).catch((error) => {
        console.error('Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = AIMenuSystemTest;
