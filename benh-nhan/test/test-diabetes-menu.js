#!/usr/bin/env node

/**
 * Test script để kiểm tra trường hợp cụ thể: 5 bữa ăn, 1800 kcal cho bệnh nhân tiểu đường
 */

const aiMenuService = require('../services/aiMenuService');

async function testDiabetesMenu() {
    console.log('🧪 Testing Diabetes Menu: 5 meals, 1800 kcal');
    console.log('='.repeat(50));
    
    try {
        const request = {
            requirements: 'Tạo thực đơn cho bệnh nhân tiểu đường, 1800 kcal/ngày, ít đường, nhiều chất xơ',
            preferences: ['ít đường', 'nhiều chất xơ'],
            restrictions: ['đường', 'kẹo', 'bánh ngọt'],
            meal_count: 5,
            target_nutrition: {
                energy: 1800,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };
        
        console.log('Request:', JSON.stringify(request, null, 2));
        console.log('\nGenerating menu...');
        
        const result = await aiMenuService.generateSmartMenu(request);
        
        if (!result.success) {
            console.log('❌ FAILED: Menu generation failed');
            console.log('Error:', result.message);
            return false;
        }
        
        const menu = result.data.menu;
        const nutrition = result.data.nutrition_summary;
        
        console.log('\n📋 Generated Menu:');
        console.log('Name:', menu.name);
        console.log('Meals:', menu.detail.length);
        
        menu.detail.forEach((meal, index) => {
            console.log(`\n${index + 1}. ${meal.name}:`);
            console.log(`   Courses: ${meal.courses?.length || 0}`);
            console.log(`   Foods: ${meal.listFood.length}`);
            
            meal.listFood.forEach((food, foodIndex) => {
                console.log(`     ${foodIndex + 1}. ${food.name}: ${food.weight}g = ${food.energy} kcal`);
            });
        });
        
        console.log('\n📊 Nutrition Summary:');
        console.log(`Energy: ${nutrition.total_energy} kcal (target: 1800 kcal)`);
        console.log(`Protein: ${nutrition.total_protein} g`);
        console.log(`Carbohydrate: ${nutrition.total_carbohydrate} g`);
        console.log(`Lipid: ${nutrition.total_lipid} g`);
        console.log(`Fiber: ${nutrition.total_fiber} g`);
        
        // Validation
        const energyDiff = Math.abs(nutrition.total_energy - 1800);
        const mealsCorrect = menu.detail.length === 5;
        const energyCorrect = energyDiff <= 100;
        
        let coursesCorrect = true;
        menu.detail.forEach((meal, index) => {
            if (!meal.courses || !Array.isArray(meal.courses) || meal.courses.length === 0) {
                coursesCorrect = false;
                console.log(`   ⚠️  Meal ${index + 1} missing courses`);
            }
            
            meal.listFood.forEach((food, foodIndex) => {
                if (!food.course_id) {
                    coursesCorrect = false;
                    console.log(`   ⚠️  Food ${foodIndex + 1} in meal ${index + 1} missing course_id`);
                }
            });
        });
        
        console.log('\n✅ Validation Results:');
        console.log(`Meals: ${menu.detail.length}/5 ${mealsCorrect ? '✅' : '❌'}`);
        console.log(`Energy: ${nutrition.total_energy}/1800 kcal (diff: ${energyDiff.toFixed(1)}) ${energyCorrect ? '✅' : '❌'}`);
        console.log(`Courses structure: ${coursesCorrect ? '✅' : '❌'}`);
        
        const allPassed = mealsCorrect && energyCorrect && coursesCorrect;
        console.log(`\n${allPassed ? '🎉 ALL TESTS PASSED!' : '❌ SOME TESTS FAILED'}`);
        
        if (!allPassed) {
            console.log('\n🔧 Issues found:');
            if (!mealsCorrect) console.log('- Wrong number of meals');
            if (!energyCorrect) console.log('- Energy target not met');
            if (!coursesCorrect) console.log('- Missing courses structure');
        }
        
        return allPassed;
        
    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        return false;
    }
}

// Run the test
if (require.main === module) {
    testDiabetesMenu()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

module.exports = testDiabetesMenu;
