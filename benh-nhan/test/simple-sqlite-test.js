/**
 * Simple SQLite Test
 */

console.log('🧪 Testing SQLite Service...');

async function testBasic() {
    try {
        const sqliteService = require('../services/sqliteService');
        
        console.log('1. Testing createProjectDatabase...');
        
        const dbPath = sqliteService.createProjectDatabase('Test Project', 123);
        
        if (dbPath) {
            console.log('✅ Database created:', dbPath);
        } else {
            console.log('❌ Failed to create database');
        }
        
    } catch (error) {
        console.log('❌ Error:', error.message);
    }
}

testBasic();
