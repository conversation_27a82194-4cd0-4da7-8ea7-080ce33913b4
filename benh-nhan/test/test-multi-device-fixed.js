const axios = require('axios');

// Cấu hình test
const BASE_URL = 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'password123';

// Test data
let tokens = [];
let devices = [];

async function testMultiDeviceFixed() {
    console.log('🧪 Test tính năng Multi-Device đã được sửa...\n');

    try {
        // 1. Đăng nhập thiết bị thứ nhất
        console.log('1️⃣ Đăng nhập thiết bị thứ nhất...');
        const login1Response = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token'
        });

        if (login1Response.data.success) {
            console.log('✅ Đăng nhập thiết bị 1 thành công');
            const token1 = login1Response.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];
            tokens.push(token1);
        } else {
            console.log('❌ Đăng nhập thiết bị 1 thất bại:', login1Response.data.message);
            return;
        }

        // 2. Kiểm tra danh sách thiết bị sau đăng nhập thứ nhất
        console.log('\n2️⃣ Kiểm tra danh sách thiết bị sau đăng nhập thứ nhất...');
        const devices1Response = await axios.get(`${BASE_URL}/devices`, {
            headers: { Cookie: `token=${tokens[0]}` }
        });

        if (devices1Response.data.success) {
            devices = devices1Response.data.data.devices;
            console.log(`✅ Thiết bị 1: ${devices.length} thiết bị đang hoạt động`);
            devices.forEach((device, index) => {
                console.log(`   ${index + 1}. ${device.deviceName} - ${device.isCurrentSession ? 'Hiện tại' : 'Hoạt động'}`);
            });
        }

        // 3. Đăng nhập thiết bị thứ hai (với User-Agent khác)
        console.log('\n3️⃣ Đăng nhập thiết bị thứ hai...');
        const login2Response = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token'
        }, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
            }
        });

        if (login2Response.data.success) {
            console.log('✅ Đăng nhập thiết bị 2 thành công');
            const token2 = login2Response.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];
            tokens.push(token2);
        } else {
            console.log('❌ Đăng nhập thiết bị 2 thất bại:', login2Response.data.message);
            return;
        }

        // 4. Kiểm tra danh sách thiết bị sau đăng nhập thứ hai
        console.log('\n4️⃣ Kiểm tra danh sách thiết bị sau đăng nhập thứ hai...');
        const devices2Response = await axios.get(`${BASE_URL}/devices`, {
            headers: { Cookie: `token=${tokens[1]}` }
        });

        if (devices2Response.data.success) {
            const devices2 = devices2Response.data.data.devices;
            console.log(`✅ Thiết bị 2: ${devices2.length} thiết bị đang hoạt động`);
            
            if (devices2.length > devices.length) {
                console.log('✅ SUCCESS: Thiết bị thứ 2 đã được thêm vào danh sách (multi-device hoạt động)');
                devices2.forEach((device, index) => {
                    console.log(`   ${index + 1}. ${device.deviceName} - ${device.isCurrentSession ? 'Hiện tại' : 'Hoạt động'}`);
                });
            } else {
                console.log('❌ FAIL: Thiết bị thứ 2 không được thêm vào danh sách (vẫn single device)');
            }
        }

        // 5. Kiểm tra thiết bị thứ nhất vẫn hoạt động
        console.log('\n5️⃣ Kiểm tra thiết bị thứ nhất vẫn hoạt động...');
        try {
            const checkDevice1Response = await axios.get(`${BASE_URL}/devices`, {
                headers: { Cookie: `token=${tokens[0]}` }
            });

            if (checkDevice1Response.data.success) {
                console.log('✅ SUCCESS: Thiết bị thứ nhất vẫn hoạt động bình thường');
            } else {
                console.log('❌ FAIL: Thiết bị thứ nhất bị logout');
            }
        } catch (error) {
            console.log('❌ FAIL: Thiết bị thứ nhất bị logout hoặc lỗi:', error.message);
        }

        // 6. Đăng nhập thiết bị thứ ba
        console.log('\n6️⃣ Đăng nhập thiết bị thứ ba...');
        const login3Response = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token'
        }, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
            }
        });

        if (login3Response.data.success) {
            console.log('✅ Đăng nhập thiết bị 3 thành công');
            const token3 = login3Response.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];
            tokens.push(token3);
        } else {
            console.log('❌ Đăng nhập thiết bị 3 thất bại:', login3Response.data.message);
        }

        // 7. Kiểm tra danh sách thiết bị cuối cùng
        console.log('\n7️⃣ Kiểm tra danh sách thiết bị cuối cùng...');
        const finalDevicesResponse = await axios.get(`${BASE_URL}/devices`, {
            headers: { Cookie: `token=${tokens[2]}` }
        });

        if (finalDevicesResponse.data.success) {
            const finalDevices = finalDevicesResponse.data.data.devices;
            console.log(`✅ Danh sách cuối cùng: ${finalDevices.length} thiết bị`);
            
            if (finalDevices.length >= 3) {
                console.log('🎉 HOÀN HẢO: Multi-device đã hoạt động đúng!');
                finalDevices.forEach((device, index) => {
                    console.log(`   ${index + 1}. ${device.deviceName} (${device.deviceType}) - ${device.isCurrentSession ? 'Hiện tại' : 'Hoạt động'}`);
                });
            } else {
                console.log('⚠️  Vẫn có vấn đề với multi-device');
            }
        }

        // 8. Test logout từng thiết bị
        console.log('\n8️⃣ Test logout từng thiết bị...');
        const currentDevicesResponse = await axios.get(`${BASE_URL}/devices`, {
            headers: { Cookie: `token=${tokens[0]}` }
        });

        if (currentDevicesResponse.data.success) {
            const currentDevices = currentDevicesResponse.data.data.devices;
            const otherDevices = currentDevices.filter(d => !d.isCurrentSession);
            
            if (otherDevices.length > 0) {
                console.log(`   Logout thiết bị: ${otherDevices[0].deviceName}...`);
                const logoutResponse = await axios.post(`${BASE_URL}/devices/logout`, {
                    tokenId: otherDevices[0].tokenId
                }, {
                    headers: { Cookie: `token=${tokens[0]}` }
                });

                if (logoutResponse.data.success) {
                    console.log(`   ✅ Đã logout thiết bị ${otherDevices[0].deviceName} thành công`);
                } else {
                    console.log(`   ❌ Lỗi khi logout: ${logoutResponse.data.message}`);
                }
            }
        }

        console.log('\n🎉 Test hoàn thành!');

    } catch (error) {
        console.error('❌ Lỗi trong quá trình test:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Test cài đặt multi-device
async function testMultiDeviceSettings() {
    console.log('\n🧪 Test cài đặt multi-device...\n');

    try {
        // Đăng nhập
        const loginResponse = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token'
        });

        if (!loginResponse.data.success) {
            console.log('❌ Không thể đăng nhập để test cài đặt');
            return;
        }

        const authToken = loginResponse.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];

        // 1. Lấy cài đặt hiện tại
        console.log('1️⃣ Lấy cài đặt hiện tại...');
        const settingsResponse = await axios.get(`${BASE_URL}/devices/settings`, {
            headers: { Cookie: `token=${authToken}` }
        });

        if (settingsResponse.data.success) {
            const settings = settingsResponse.data.data;
            console.log('✅ Cài đặt hiện tại:');
            console.log(`   - Cho phép nhiều thiết bị: ${settings.allow_multiple_devices ? 'Có' : 'Không'}`);
            console.log(`   - Số session tối đa: ${settings.max_sessions}`);
            console.log(`   - Timeout: ${settings.session_timeout_hours} giờ`);
        }

        // 2. Cập nhật cài đặt
        console.log('\n2️⃣ Cập nhật cài đặt...');
        const updateResponse = await axios.post(`${BASE_URL}/devices/settings`, {
            allow_multiple_devices: 1,
            max_sessions: 5,
            session_timeout_hours: 24
        }, {
            headers: { Cookie: `token=${authToken}` }
        });

        if (updateResponse.data.success) {
            console.log('✅ Cập nhật cài đặt thành công');
        } else {
            console.log('❌ Lỗi khi cập nhật cài đặt:', updateResponse.data.message);
        }

        // 3. Kiểm tra lại cài đặt
        console.log('\n3️⃣ Kiểm tra lại cài đặt...');
        const checkSettingsResponse = await axios.get(`${BASE_URL}/devices/settings`, {
            headers: { Cookie: `token=${authToken}` }
        });

        if (checkSettingsResponse.data.success) {
            const newSettings = checkSettingsResponse.data.data;
            console.log('✅ Cài đặt sau khi cập nhật:');
            console.log(`   - Cho phép nhiều thiết bị: ${newSettings.allow_multiple_devices ? 'Có' : 'Không'}`);
            console.log(`   - Số session tối đa: ${newSettings.max_sessions}`);
            console.log(`   - Timeout: ${newSettings.session_timeout_hours} giờ`);
        }

        console.log('\n🎉 Test cài đặt hoàn thành!');

    } catch (error) {
        console.error('❌ Lỗi trong quá trình test cài đặt:', error.message);
    }
}

// Chạy test
if (require.main === module) {
    testMultiDeviceFixed().then(() => {
        return testMultiDeviceSettings();
    });
}

module.exports = { testMultiDeviceFixed, testMultiDeviceSettings }; 