#!/usr/bin/env node

/**
 * Test script for the new @google/genai API
 * This script tests the updated Gemini service with the new API
 */

const geminiService = require('../services/geminiService');

async function testNewGeminiAPI() {
    console.log('🧪 Testing New Gemini API (@google/genai)');
    console.log('='.repeat(50));
    
    try {
        // Test 1: Service Initialization
        console.log('\n1️⃣ Testing service initialization...');
        const initResult = await geminiService.initialize();
        
        if (!initResult) {
            throw new Error('Failed to initialize Gemini service');
        }
        console.log('✅ Gemini service initialized successfully');
        
        // Test 2: Simple Menu Generation
        console.log('\n2️⃣ Testing simple menu generation...');
        
        const mockFoods = [
            {
                food_id: 1,
                name: 'Cơm trắng',
                type: 'cooked',
                energy: 130,
                protein: 2.7,
                carbohydrate: 29,
                lipid: 0.3,
                fiber: 0.4
            },
            {
                food_id: 2,
                name: '<PERSON><PERSON><PERSON><PERSON> g<PERSON> luộ<PERSON>',
                type: 'cooked',
                energy: 165,
                protein: 31,
                carbohydrate: 0,
                lipid: 3.6,
                fiber: 0
            },
            {
                food_id: 3,
                name: '<PERSON><PERSON> c<PERSON><PERSON> xanh',
                type: 'cooked',
                energy: 22,
                protein: 2.9,
                carbohydrate: 3.5,
                lipid: 0.2,
                fiber: 2.6
            }
        ];
        
        const requirements = 'Tạo thực đơn cân bằng dinh dưỡng cho người bình thường, 1500 kcal';
        const targetNutrition = {
            energy: 1500,
            protein: 75,
            carbohydrate: 200,
            lipid: 50
        };
        
        console.log('📝 Requirements:', requirements);
        console.log('🎯 Target nutrition:', targetNutrition);
        console.log('🥘 Available foods:', mockFoods.length);
        
        const menuResult = await geminiService.generateMenu(requirements, mockFoods, targetNutrition);
        
        if (menuResult.success) {
            console.log('✅ Menu generation successful!');
            console.log('📋 Menu name:', menuResult.data.menu.name);
            console.log('🍽️ Number of meals:', menuResult.data.menu.detail.length);
            
            // Display nutrition summary
            if (menuResult.data.nutrition_summary) {
                console.log('\n📊 Nutrition Summary:');
                console.log(`   Energy: ${menuResult.data.nutrition_summary.total_energy} kcal`);
                console.log(`   Protein: ${menuResult.data.nutrition_summary.total_protein} g`);
                console.log(`   Carbohydrate: ${menuResult.data.nutrition_summary.total_carbohydrate} g`);
                console.log(`   Lipid: ${menuResult.data.nutrition_summary.total_lipid} g`);
            }
            
            // Display meals
            console.log('\n🍽️ Meals:');
            menuResult.data.menu.detail.forEach((meal, index) => {
                console.log(`   ${index + 1}. ${meal.name}: ${meal.listFood.length} foods`);
            });
            
        } else {
            console.log('❌ Menu generation failed:', menuResult.message);
            return false;
        }
        
        // Test 3: Error Handling
        console.log('\n3️⃣ Testing error handling...');
        
        const errorResult = await geminiService.generateMenu('', [], null);
        if (!errorResult.success) {
            console.log('✅ Error handling works correctly');
        } else {
            console.log('⚠️ Error handling might need improvement');
        }
        
        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📝 Summary:');
        console.log('   ✅ New @google/genai package is working');
        console.log('   ✅ gemini-2.0-flash-001 model is accessible');
        console.log('   ✅ Menu generation produces valid JSON');
        console.log('   ✅ Nutrition calculations are working');
        console.log('   ✅ Error handling is functional');
        
        return true;
        
    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        console.error('\n🔧 Troubleshooting:');
        console.error('1. Check your GEMINI_API_KEY in .env file');
        console.error('2. Ensure you have internet connection');
        console.error('3. Verify the API key has proper permissions');
        console.error('4. Check if the gemini-2.0-flash-001 model is available');
        
        return false;
    }
}

// Run the test
if (require.main === module) {
    testNewGeminiAPI()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

module.exports = testNewGeminiAPI;
