// Script để tối ưu hóa các controller còn lại
// Chạy script này để áp dụng pattern DataTableService cho các controller chưa được tối ưu

const fs = require('fs');
const path = require('path');

// Danh sách các controller cần tối ưu
const controllersToOptimize = [
    'hepatitisController.js',
    'tetanusController.js', 
    'standardController.js',
    'hepstitisMt1Controller.js',
    'userController.js'
];

// Template pattern để thay thế
const oldPattern = /var parameter = \{[\s\S]*?\};[\s\S]*?commonService\.getDataTableData\(parameter\)\.then\([\s\S]*?\}\);/g;

const newPatternTemplate = `const dataTableService = require('../services/dataTableService');
        
        // C<PERSON>u hình DataTable
        const config = {
            table: 'TABLE_NAME',
            columns: COLUMNS_ARRAY,
            primaryKey: 'id',
            active: ACTIVE_VALUE,
            activeOperator: 'ACTIVE_OPERATOR',
            filters: FILTERS_OBJECT,
            searchColumns: SEARCH_COLUMNS,
            columnsMapping: COLUMNS_MAPPING,
            defaultOrder: DEFAULT_ORDER,
            checkRole: CHECK_ROLE
        };

        // Xử lý request
        dataTableService.handleDataTableRequest(req, res, config);`;

function analyzeController(filePath) {
    console.log(`\n=== Analyzing ${path.basename(filePath)} ===`);
    
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Tìm các method có sử dụng getDataTableData
        const getDataTableMatches = content.match(/getDataTableData\(parameter\)/g);
        if (getDataTableMatches) {
            console.log(`Found ${getDataTableMatches.length} getDataTableData usage(s)`);
        } else {
            console.log('No getDataTableData usage found');
            return;
        }
        
        // Tìm các parameter object
        const parameterMatches = content.match(/var parameter = \{[\s\S]*?\};/g);
        if (parameterMatches) {
            console.log(`Found ${parameterMatches.length} parameter object(s)`);
            parameterMatches.forEach((match, index) => {
                console.log(`\nParameter ${index + 1}:`);
                console.log(match.substring(0, 200) + '...');
            });
        }
        
        // Tìm các method list hoặc listData
        const methodMatches = content.match(/(list|listData|getListTable):\s*function\s*\([^)]*\)\s*\{/g);
        if (methodMatches) {
            console.log(`\nFound methods: ${methodMatches.join(', ')}`);
        }
        
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error.message);
    }
}

function generateOptimizationSuggestions(controllerName) {
    console.log(`\n=== Optimization Suggestions for ${controllerName} ===`);
    
    const suggestions = {
        'hepatitisController.js': {
            table: 'patients',
            searchColumns: ['fullname', 'phone', 'ma_benh_an'],
            columnsMapping: ['', 'fullname', 'phone', 'chuan_doan', 'created_at'],
            defaultOrder: [{ column: 'id', dir: 'DESC' }]
        },
        'tetanusController.js': {
            table: 'patients', 
            searchColumns: ['fullname', 'phone', 'ma_benh_an'],
            columnsMapping: ['', 'fullname', 'phone', 'chuan_doan', 'created_at'],
            defaultOrder: [{ column: 'id', dir: 'DESC' }]
        },
        'standardController.js': {
            table: 'patients',
            searchColumns: ['fullname', 'phone', 'ma_benh_an'],
            columnsMapping: ['', 'fullname', 'phone', 'chuan_doan', 'created_at'],
            defaultOrder: [{ column: 'id', dir: 'DESC' }]
        },
        'hepstitisMt1Controller.js': {
            table: 'patients',
            searchColumns: ['fullname', 'phone', 'ma_benh_an'],
            columnsMapping: ['', 'fullname', 'phone', 'dieu_tra_vien', 'created_at'],
            defaultOrder: [{ column: 'id', dir: 'DESC' }]
        },
        'userController.js': {
            table: 'user',
            searchColumns: ['fullname', 'email', 'phone'],
            columnsMapping: ['', 'fullname', 'email', 'active', 'created_at'],
            defaultOrder: [{ column: 'id', dir: 'DESC' }]
        }
    };
    
    const suggestion = suggestions[controllerName];
    if (suggestion) {
        console.log('Suggested configuration:');
        console.log(JSON.stringify(suggestion, null, 2));
        
        console.log('\nExample implementation:');
        console.log(`
const config = {
    table: '${suggestion.table}',
    columns: ['id', 'fullname', 'phone', 'created_at'], // Adjust as needed
    primaryKey: 'id',
    active: 0,
    activeOperator: '!=',
    filters: securityService.applyRoleBasedFiltering(req.user, {}),
    searchColumns: ${JSON.stringify(suggestion.searchColumns)},
    columnsMapping: ${JSON.stringify(suggestion.columnsMapping)},
    defaultOrder: ${JSON.stringify(suggestion.defaultOrder)},
    checkRole: true
};

dataTableService.handleDataTableRequest(req, res, config);
        `);
    }
}

// Main execution
console.log('=== DataTable Controllers Optimization Analysis ===');

const controllersDir = path.join(__dirname, '../controllers');

controllersToOptimize.forEach(controllerFile => {
    const filePath = path.join(controllersDir, controllerFile);
    
    if (fs.existsSync(filePath)) {
        analyzeController(filePath);
        generateOptimizationSuggestions(controllerFile);
    } else {
        console.log(`\n${controllerFile} not found`);
    }
});

console.log('\n=== Analysis Complete ===');
console.log('\nNext steps:');
console.log('1. Review the analysis above');
console.log('2. Apply the dataTableService pattern to each controller');
console.log('3. Update corresponding views with orderable/searchable config');
console.log('4. Test each DataTable functionality');
console.log('5. Verify order parsing works correctly');

console.log('\nExample view optimization:');
console.log(`
columns: [
    {
        data: null,
        orderable: false,
        searchable: false,
        render: function(data, type, row) {
            // Actions column
        }
    },
    {
        data: 'column_name',
        orderable: true,
        searchable: true
    }
],
order: [], // Let server handle order
searching: true,
ordering: true
`);
