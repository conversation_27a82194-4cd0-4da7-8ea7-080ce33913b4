const commonService = require('../services/commonService');
const db = require('../config/db');

async function testFoodInfo() {
    try {
        // Khởi tạo kết nối database
        db.connect('test');
        
        console.log('Testing food_info table...');
        
        // Test 1: <PERSON><PERSON><PERSON> kiếm với từ "thịt"
        console.log('\n1. Tìm kiếm với từ "thịt":');
        const result1 = await commonService.getAllDataTable('food_info', { 
            name: { op: 'LIKE', value: '%thịt%' } 
        }, {}, 'AND');
        
        console.log('Success:', result1.success);
        console.log('Message:', result1.message);
        console.log('Data count:', result1.data ? result1.data.length : 0);
        
        if (result1.data && result1.data.length > 0) {
            console.log('Sample data:', result1.data[0]);
        }
        
        // Test 2: <PERSON><PERSON><PERSON> t<PERSON><PERSON> cả dữ liệu
        console.log('\n2. <PERSON><PERSON><PERSON> tất cả dữ liệu:');
        const result2 = await commonService.getAllDataTable('food_info', {}, {}, 'AND', 'id, name, energy, protein');
        
        console.log('Success:', result2.success);
        console.log('Message:', result2.message);
        console.log('Total records:', result2.data ? result2.data.length : 0);
        
        if (result2.data && result2.data.length > 0) {
            console.log('First 3 records:');
            result2.data.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.name} - Energy: ${item.energy}, Protein: ${item.protein}`);
            });
        }
        
    } catch (error) {
        console.error('Error:', error);
    }
}

testFoodInfo(); 