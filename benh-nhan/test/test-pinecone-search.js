const pineconeService = require('../services/pineconeService');

async function testPineconeSearch() {
    console.log('🧪 Testing Pinecone Search...\n');

    try {
        // 1. Test dish search
        console.log('1️⃣ Testing dish search...');
        const dishResult = await pineconeService.searchDishes('cơm gà', { topK: 5 });
        
        console.log('Dish search result:', {
            success: dishResult.success,
            count: dishResult.data?.length || 0,
            message: dishResult.message
        });
        
        if (dishResult.success && dishResult.data.length > 0) {
            console.log('   🍽️  Dishes found:');
            dishResult.data.forEach(dish => {
                console.log(`      - ${dish.name} (${dish.total_energy} kcal)`);
            });
        }

        // 2. Test food search
        console.log('\n2️⃣ Testing food search...');
        const foodResult = await pineconeService.searchFoods('cơm gà', { topK: 5 });
        
        console.log('Food search result:', {
            success: foodResult.success,
            count: foodResult.data?.length || 0,
            message: foodResult.message
        });
        
        if (foodResult.success && foodResult.data.length > 0) {
            console.log('   🥘 Foods found:');
            foodResult.data.slice(0, 3).forEach(food => {
                console.log(`      - ${food.name} (${food.energy} kcal/100g)`);
            });
        }

        // 3. Test combined search
        console.log('\n3️⃣ Testing combined search...');
        const combinedResult = await pineconeService.searchFoodsAndDishes('cơm gà', {
            topK: 10,
            foodLimit: 5,
            dishLimit: 5
        });
        
        console.log('Combined search result:', {
            success: combinedResult.success,
            count: combinedResult.data?.length || 0,
            metadata: combinedResult.metadata,
            message: combinedResult.message
        });
        
        if (combinedResult.success && combinedResult.data.length > 0) {
            const foods = combinedResult.data.filter(item => item.type === 'food');
            const dishes = combinedResult.data.filter(item => item.type === 'dish');
            
            console.log(`   📊 Results: ${foods.length} foods + ${dishes.length} dishes`);
            
            if (foods.length > 0) {
                console.log('   🥘 Foods:');
                foods.slice(0, 2).forEach(food => {
                    console.log(`      - ${food.name} (${food.energy} kcal/100g)`);
                });
            }
            
            if (dishes.length > 0) {
                console.log('   🍽️  Dishes:');
                dishes.forEach(dish => {
                    console.log(`      - ${dish.name} (${dish.total_energy} kcal)`);
                });
            }
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testPineconeSearch();
