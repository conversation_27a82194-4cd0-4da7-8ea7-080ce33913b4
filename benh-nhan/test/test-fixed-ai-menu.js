const aiMenuService = require('../services/aiMenuService');

async function testFixedAIMenu() {
    console.log('🧪 Testing Fixed AI Menu Generation...\n');

    try {
        // Test với yêu cầu bệnh nhân ung thư dạ dày
        console.log('1️⃣ Testing cancer patient menu...');
        const request = {
            requirements: "Tạo thực đơn cho bệnh nhân ung thư dạ dày",
            preferences: [],
            restrictions: [],
            meal_count: 5,
            target_nutrition: {
                energy: 1500,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };

        console.log('Request:', JSON.stringify(request, null, 2));

        const result = await aiMenuService.generateSmartMenu(request);

        if (result.success) {
            const menu = result.data.menu;
            const nutrition = result.data.nutrition_summary;

            console.log(`   ✅ Menu generated: ${menu.name}`);
            console.log(`   📊 Meals: ${menu.detail.length}`);
            console.log(`   📊 Energy: ${nutrition.energy} kcal (target: 1500)`);
            console.log(`   📊 Protein: ${nutrition.protein}g`);

            // Check meal structure
            const mealIds = menu.detail.map(m => m.id);
            console.log(`   📊 Meal IDs: [${mealIds.join(', ')}]`);
            console.log(`   📊 Expected: [3, 4, 5, 6, 7] (5 bữa)`);

            if (JSON.stringify(mealIds) === JSON.stringify([3, 4, 5, 6, 7])) {
                console.log('   ✅ MenuTime mapping CORRECT');
            } else {
                console.log('   ❌ MenuTime mapping INCORRECT');
            }

            // Check food diversity and repetition
            const foodUsage = {};
            let totalItems = 0;
            let totalFoods = 0;
            let totalDishes = 0;
            let invalidIds = 0;

            menu.detail.forEach(meal => {
                console.log(`\n   🍽️  ${meal.name}:`);
                meal.listFood.forEach(food => {
                    totalItems++;
                    
                    // Count usage
                    if (!foodUsage[food.name]) {
                        foodUsage[food.name] = 0;
                    }
                    foodUsage[food.name]++;

                    // Check if ID is valid (not causing "not found" error)
                    if (food.energy === 0 && food.protein === 0) {
                        invalidIds++;
                        console.log(`      - ❌ ${food.name} (ID: ${food.id_food}) - INVALID ID`);
                    } else {
                        if (food.id_food && food.id_food.toString().startsWith('dish_')) {
                            totalDishes++;
                            console.log(`      - 🍲 ${food.name} (${food.weight} khẩu phần, ${food.energy} kcal)`);
                        } else {
                            totalFoods++;
                            console.log(`      - 🥘 ${food.name} (${food.weight}g, ${food.energy} kcal)`);
                        }
                    }
                });
            });

            console.log(`\n   📊 Total items: ${totalItems}`);
            console.log(`   📊 Foods: ${totalFoods}, Dishes: ${totalDishes}`);
            console.log(`   📊 Invalid IDs: ${invalidIds}`);

            // Check for repetition
            const repeatedFoods = Object.entries(foodUsage).filter(([name, count]) => count > 2);
            if (repeatedFoods.length > 0) {
                console.log(`   ⚠️  Repeated foods (>2 times):`);
                repeatedFoods.forEach(([name, count]) => {
                    console.log(`      - ${name}: ${count} times`);
                });
            } else {
                console.log('   ✅ Good food diversity - no excessive repetition');
            }

            // Check energy accuracy
            const energyDiff = Math.abs(nutrition.energy - 1500);
            const energyAccuracy = ((1500 - energyDiff) / 1500) * 100;
            console.log(`   📊 Energy accuracy: ${energyAccuracy.toFixed(1)}%`);

            if (energyDiff <= 100) {
                console.log('   ✅ Energy target EXCELLENT (±100 kcal)');
            } else if (energyDiff <= 200) {
                console.log('   ✅ Energy target GOOD (±200 kcal)');
            } else {
                console.log('   ⚠️  Energy target needs improvement');
            }

            // Overall assessment
            console.log('\n   🎯 ASSESSMENT:');
            if (invalidIds === 0) {
                console.log('   ✅ All IDs are valid');
            } else {
                console.log(`   ❌ ${invalidIds} invalid IDs found`);
            }

            if (repeatedFoods.length === 0) {
                console.log('   ✅ Good food diversity');
            } else {
                console.log(`   ⚠️  ${repeatedFoods.length} foods repeated too much`);
            }

        } else {
            console.log(`   ❌ Menu generation failed: ${result.message}`);
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testFixedAIMenu();
