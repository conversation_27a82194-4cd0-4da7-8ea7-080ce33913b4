#!/usr/bin/env node

/**
 * Test script để kiểm tra toàn bộ flow: AI generation → Integration → Menu structure
 */

const aiMenuService = require('../services/aiMenuService');

async function testFullIntegration() {
    console.log('🧪 Testing Full AI Menu Integration');
    console.log('='.repeat(60));
    
    try {
        // Test case: 5 bữa ăn, 1800 kcal cho bệnh nhân tiểu đường
        const request = {
            requirements: 'Tạo thực đơn cho bệnh nhân tiểu đường, 1800 kcal/ngày, ít đường, nhiều chất xơ',
            preferences: ['ít đường', 'nhiều chất xơ'],
            restrictions: ['đường', 'kẹo', 'bánh ngọt'],
            meal_count: 5,
            target_nutrition: {
                energy: 1800,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };
        
        console.log('📝 Step 1: Generate AI Menu');
        console.log('Request:', JSON.stringify(request, null, 2));
        
        const result = await aiMenuService.generateSmartMenu(request);
        
        if (!result.success) {
            console.log('❌ FAILED: Menu generation failed');
            console.log('Error:', result.message);
            return false;
        }
        
        console.log('✅ AI Menu generated successfully');
        
        const menu = result.data.menu;
        const nutrition = result.data.nutrition_summary;
        
        console.log('\n📋 Step 2: Validate Menu Structure');
        
        // Kiểm tra cấu trúc cơ bản
        const basicChecks = {
            hasName: !!menu.name,
            hasDetail: Array.isArray(menu.detail),
            correctMealCount: menu.detail.length === 5,
            hasNutritionSummary: !!nutrition
        };
        
        console.log('Basic structure checks:');
        Object.entries(basicChecks).forEach(([key, value]) => {
            console.log(`  ${key}: ${value ? '✅' : '❌'}`);
        });
        
        // Kiểm tra từng bữa ăn
        let mealStructureValid = true;
        console.log('\nMeal structure checks:');
        
        menu.detail.forEach((meal, index) => {
            const checks = {
                hasId: !!meal.id,
                hasName: !!meal.name,
                hasCourses: Array.isArray(meal.courses) && meal.courses.length > 0,
                hasListFood: Array.isArray(meal.listFood) && meal.listFood.length > 0
            };
            
            const mealValid = Object.values(checks).every(v => v);
            if (!mealValid) mealStructureValid = false;
            
            console.log(`  Meal ${index + 1} (${meal.name}): ${mealValid ? '✅' : '❌'}`);
            if (!mealValid) {
                Object.entries(checks).forEach(([key, value]) => {
                    if (!value) console.log(`    - Missing ${key}`);
                });
            }
            
            // Kiểm tra foods trong meal
            meal.listFood.forEach((food, foodIndex) => {
                const foodChecks = {
                    hasId: !!food.id,
                    hasIdFood: !!food.id_food,
                    hasName: !!food.name,
                    hasWeight: typeof food.weight === 'number' && food.weight > 0,
                    hasEnergy: typeof food.energy === 'number',
                    hasCourseId: !!food.course_id
                };
                
                const foodValid = Object.values(foodChecks).every(v => v);
                if (!foodValid) {
                    console.log(`    Food ${foodIndex + 1}: ❌`);
                    Object.entries(foodChecks).forEach(([key, value]) => {
                        if (!value) console.log(`      - Missing ${key}`);
                    });
                    mealStructureValid = false;
                }
            });
        });
        
        console.log(`\nOverall meal structure: ${mealStructureValid ? '✅' : '❌'}`);
        
        // Kiểm tra dinh dưỡng
        const energyDiff = Math.abs(nutrition.total_energy - 1800);
        const nutritionValid = energyDiff <= 100;
        
        console.log('\n📊 Step 3: Validate Nutrition');
        console.log(`Energy: ${nutrition.total_energy}/1800 kcal (diff: ${energyDiff.toFixed(1)}) ${nutritionValid ? '✅' : '❌'}`);
        console.log(`Protein: ${nutrition.total_protein} g`);
        console.log(`Carbohydrate: ${nutrition.total_carbohydrate} g`);
        console.log(`Lipid: ${nutrition.total_lipid} g`);
        console.log(`Fiber: ${nutrition.total_fiber} g`);
        
        // Kiểm tra tương thích với hệ thống
        console.log('\n🔗 Step 4: Check System Compatibility');
        
        const compatibilityChecks = {
            menuHasRequiredFields: !!(menu.name && menu.detail && menu.created_at),
            mealsHaveIds: menu.detail.every(meal => !!meal.id),
            mealsHaveNames: menu.detail.every(meal => !!meal.name),
            mealsHaveCourses: menu.detail.every(meal => Array.isArray(meal.courses)),
            foodsHaveRequiredFields: menu.detail.every(meal => 
                meal.listFood.every(food => 
                    !!(food.id && food.id_food && food.name && food.weight)
                )
            ),
            foodsHaveNutrition: menu.detail.every(meal => 
                meal.listFood.every(food => 
                    typeof food.energy === 'number' && 
                    typeof food.protein === 'number' &&
                    typeof food.carbohydrate === 'number' &&
                    typeof food.lipid === 'number'
                )
            )
        };
        
        console.log('System compatibility checks:');
        Object.entries(compatibilityChecks).forEach(([key, value]) => {
            console.log(`  ${key}: ${value ? '✅' : '❌'}`);
        });
        
        const allCompatible = Object.values(compatibilityChecks).every(v => v);
        
        // Tổng kết
        console.log('\n' + '='.repeat(60));
        console.log('📊 FINAL RESULTS:');
        
        const allChecks = {
            'Basic Structure': Object.values(basicChecks).every(v => v),
            'Meal Structure': mealStructureValid,
            'Nutrition Target': nutritionValid,
            'System Compatibility': allCompatible
        };
        
        Object.entries(allChecks).forEach(([category, passed]) => {
            console.log(`${category}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        });
        
        const overallSuccess = Object.values(allChecks).every(v => v);
        
        console.log(`\n${overallSuccess ? '🎉 ALL TESTS PASSED!' : '❌ SOME TESTS FAILED'}`);
        
        if (overallSuccess) {
            console.log('\n✨ The AI menu is ready for integration into the system!');
            console.log('Users can now click "Sử dụng thực đơn này" without issues.');
        } else {
            console.log('\n🔧 Issues need to be resolved before integration.');
        }
        
        // Hiển thị sample menu structure để debug
        if (!overallSuccess) {
            console.log('\n🔍 Sample menu structure for debugging:');
            console.log(JSON.stringify({
                name: menu.name,
                detail: menu.detail.slice(0, 1).map(meal => ({
                    ...meal,
                    listFood: meal.listFood.slice(0, 1)
                }))
            }, null, 2));
        }
        
        return overallSuccess;
        
    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        console.error('Stack trace:', error.stack);
        return false;
    }
}

// Run the test
if (require.main === module) {
    testFullIntegration()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

module.exports = testFullIntegration;
