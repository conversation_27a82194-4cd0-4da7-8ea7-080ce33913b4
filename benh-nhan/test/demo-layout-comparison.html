<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Layout Comparison - Responsive Table</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/table-config.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fc;
            font-family: 'Nunito', sans-serif;
        }
        .demo-container {
            max-width: 400px;
            margin: 20px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
        }
        .comparison-section {
            padding: 20px;
        }
        .layout-old {
            border: 2px solid #dc3545;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 15px;
        }
        .layout-new {
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
        }
        .layout-title {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        .old-style {
            color: #dc3545;
        }
        .new-style {
            color: #28a745;
        }
        
        /* Old layout simulation */
        .old-layout-cell {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f1f1;
            position: relative;
        }
        .old-layout-cell .label {
            width: 35%;
            font-weight: 600;
            color: #5a5c69;
            font-size: 0.85rem;
            text-transform: uppercase;
            padding-right: 10px;
        }
        .old-layout-cell .content {
            flex: 1;
            color: #3a3b45;
        }
        
        /* New layout simulation */
        .new-layout-cell {
            padding: 12px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .new-layout-cell .label {
            display: block;
            font-weight: 600;
            color: #5a5c69;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }
        .new-layout-cell .content {
            display: block;
            color: #3a3b45;
            font-size: 0.95rem;
            line-height: 1.4;
        }
        
        .btn-demo {
            padding: 4px 8px;
            font-size: 0.75rem;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-mobile-alt"></i> 
                    Demo Layout Comparison - Mobile Table Cards
                </h1>
                <p class="text-center text-muted">So sánh layout cũ (cùng dòng) vs layout mới (hai dòng riêng biệt)</p>
            </div>
        </div>
        
        <div class="row">
            <!-- Layout cũ -->
            <div class="col-md-6">
                <div class="demo-container">
                    <div class="demo-header">
                        <h5 class="mb-0 old-style">
                            <i class="fas fa-times-circle"></i> Layout Cũ (Cùng dòng)
                        </h5>
                    </div>
                    <div class="comparison-section">
                        <div class="layout-old">
                            <div class="layout-title old-style">Card 1</div>
                            
                            <div class="old-layout-cell">
                                <div class="label">Thao tác:</div>
                                <div class="content">
                                    <button class="btn btn-info btn-demo">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-demo">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="old-layout-cell">
                                <div class="label">Họ tên:</div>
                                <div class="content">Nguyễn Văn A</div>
                            </div>
                            
                            <div class="old-layout-cell">
                                <div class="label">Điện thoại:</div>
                                <div class="content">0123456789</div>
                            </div>
                            
                            <div class="old-layout-cell">
                                <div class="label">Chẩn đoán:</div>
                                <div class="content">Viêm gan B mạn tính</div>
                            </div>
                            
                            <div class="old-layout-cell" style="border-bottom: none;">
                                <div class="label">Ngày khám:</div>
                                <div class="content">15/03/2024</div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <small>
                                <strong>Vấn đề:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Khó đọc khi text dài</li>
                                    <li>Layout bị chật chội</li>
                                    <li>Label và content chen chúc</li>
                                </ul>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Layout mới -->
            <div class="col-md-6">
                <div class="demo-container">
                    <div class="demo-header">
                        <h5 class="mb-0 new-style">
                            <i class="fas fa-check-circle"></i> Layout Mới (Hai dòng)
                        </h5>
                    </div>
                    <div class="comparison-section">
                        <div class="layout-new">
                            <div class="layout-title new-style">Card 1</div>
                            
                            <div class="new-layout-cell">
                                <div class="label">Thao tác</div>
                                <div class="content">
                                    <button class="btn btn-info btn-demo">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-demo">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="new-layout-cell">
                                <div class="label">Họ tên</div>
                                <div class="content">Nguyễn Văn A</div>
                            </div>
                            
                            <div class="new-layout-cell">
                                <div class="label">Điện thoại</div>
                                <div class="content">0123456789</div>
                            </div>
                            
                            <div class="new-layout-cell">
                                <div class="label">Chẩn đoán</div>
                                <div class="content">Viêm gan B mạn tính</div>
                            </div>
                            
                            <div class="new-layout-cell" style="border-bottom: none;">
                                <div class="label">Ngày khám</div>
                                <div class="content">15/03/2024</div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success">
                            <small>
                                <strong>Ưu điểm:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Dễ đọc và rõ ràng hơn</li>
                                    <li>Layout thoáng đãng</li>
                                    <li>Phù hợp với text dài</li>
                                    <li>Tách biệt rõ ràng label/content</li>
                                </ul>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-code"></i> Thay đổi CSS
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6>Thay đổi chính trong <code>table-config.css</code>:</h6>
                        <pre class="bg-light p-3 rounded"><code>/* Từ layout cùng dòng */
.table-responsive table.table tbody td {
    padding: 8px 0 8px 40%;
    display: flex;
    align-items: center;
}

.table-responsive table.table tbody td:before {
    position: absolute;
    left: 0;
    width: 35%;
}

/* Thành layout hai dòng */
.table-responsive table.table tbody td {
    padding: 12px 0;
    display: block;
}

.table-responsive table.table tbody td:before {
    display: block;
    margin-bottom: 4px;
}</code></pre>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-lightbulb"></i> Kết quả:</h6>
                            <p class="mb-0">
                                Bây giờ trên mobile, mỗi cell sẽ hiển thị với <strong>tiêu đề trên một dòng</strong> 
                                và <strong>nội dung trên dòng tiếp theo</strong>, tạo layout dễ đọc và thoáng đãng hơn.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
