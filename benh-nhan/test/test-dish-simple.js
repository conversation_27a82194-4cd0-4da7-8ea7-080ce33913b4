const db = require('../config/db');
const dishService = require('../services/dishService');
const pineconeService = require('../services/pineconeService');

async function testDishFeatures() {
    console.log('🧪 Testing Dish Features...\n');

    try {
        // 1. Connect to database
        console.log('1️⃣ Connecting to database...');
        await db.connect();
        console.log('   ✅ Database connected\n');

        // 2. Test dish service
        console.log('2️⃣ Testing dish service...');
        const dishesResult = await dishService.getAllDishesWithNutrition();
        
        if (dishesResult.success && dishesResult.data && dishesResult.data.length > 0) {
            console.log(`   ✅ Found ${dishesResult.data.length} dishes`);
            
            const sampleDish = dishesResult.data[0];
            console.log(`   📊 Sample: ${sampleDish.name}`);
            console.log(`   🍽️  Energy: ${sampleDish.total_energy}kcal`);
            console.log(`   🥘 Ingredients: ${sampleDish.ingredients?.length || 0} items\n`);
        } else {
            console.log('   ⚠️  No dishes found or error occurred\n');
        }

        // 3. Test pinecone methods exist
        console.log('3️⃣ Testing pinecone methods...');
        const methods = [
            'createDishDescription',
            'upsertDish', 
            'upsertDishes',
            'searchDishes',
            'searchFoodsAndDishes'
        ];
        
        methods.forEach(method => {
            if (typeof pineconeService[method] === 'function') {
                console.log(`   ✅ ${method} method exists`);
            } else {
                console.log(`   ❌ ${method} method missing`);
            }
        });

        // 4. Test dish description creation
        if (dishesResult.success && dishesResult.data && dishesResult.data.length > 0) {
            console.log('\n4️⃣ Testing dish description creation...');
            const dish = dishesResult.data[0];
            const description = pineconeService.createDishDescription(dish);
            console.log(`   ✅ Description created: ${description.substring(0, 100)}...`);
        }

        // 5. Test menuTime mapping
        console.log('\n5️⃣ Testing menuTime mapping...');
        const geminiService = require('../services/geminiService');
        
        const testMenu = {
            name: 'Test Menu',
            detail: [
                { id: 1, name: 'Sáng', listFood: [] },
                { id: 2, name: 'Trưa', listFood: [] },
                { id: 3, name: 'Tối', listFood: [] }
            ]
        };
        
        const fixedMenu = geminiService.validateAndFixMenuStructure(testMenu, 3);
        const menuIds = fixedMenu.detail.map(meal => meal.id);
        const expectedIds = [3, 5, 7]; // Sáng, Trưa, Tối
        
        console.log(`   📅 Menu IDs: [${menuIds.join(', ')}]`);
        console.log(`   📅 Expected: [${expectedIds.join(', ')}]`);
        
        if (JSON.stringify(menuIds.sort()) === JSON.stringify(expectedIds.sort())) {
            console.log('   ✅ MenuTime mapping correct');
        } else {
            console.log('   ❌ MenuTime mapping incorrect');
        }

        console.log('\n🎉 Dish features test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        // Close database connection
        try {
            await db.close();
            console.log('📝 Database connection closed');
        } catch (closeError) {
            console.error('Error closing database:', closeError.message);
        }
    }
}

// Run test
if (require.main === module) {
    testDishFeatures().catch(console.error);
}

module.exports = testDishFeatures;
