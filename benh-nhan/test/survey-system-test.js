/**
 * Survey System Test Script
 * Test toàn bộ luồng khảo sát từ cấu hình đến xuất Excel
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
    username: 'admin',
    password: 'admin123'
};

class SurveySystemTester {
    constructor() {
        this.cookies = '';
        this.projectId = null;
        this.surveyConfigId = null;
        this.surveySlug = null;
    }

    /**
     * Login to get session
     */
    async login() {
        try {
            console.log('🔐 Đăng nhập...');
            const response = await axios.post(`${BASE_URL}/login`, TEST_USER);
            
            if (response.headers['set-cookie']) {
                this.cookies = response.headers['set-cookie'].join('; ');
                console.log('✅ Đăng nhập thành công');
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Lỗi đăng nhập:', error.message);
            return false;
        }
    }

    /**
     * Tạo dự án test
     */
    async createTestProject() {
        try {
            console.log('📁 Tạo dự án test...');
            const projectData = {
                name: `Test Project ${Date.now()}`,
                description: 'Dự án test hệ thống khảo sát',
                status: 'active',
                start_date: new Date().toISOString().split('T')[0],
                end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            };

            const response = await axios.post(`${BASE_URL}/projects/create`, projectData, {
                headers: { Cookie: this.cookies }
            });

            if (response.data.success) {
                this.projectId = response.data.data.id;
                console.log(`✅ Tạo dự án thành công - ID: ${this.projectId}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Lỗi tạo dự án:', error.message);
            return false;
        }
    }

    /**
     * Tạo cấu hình khảo sát
     */
    async createSurveyConfig() {
        try {
            console.log('📝 Tạo cấu hình khảo sát...');
            const surveyData = {
                project_id: this.projectId,
                name: `Test Survey ${Date.now()}`,
                description: 'Khảo sát test hệ thống',
                survey_url_slug: `test-survey-${Date.now()}`,
                active: 1,
                allow_multiple_responses: 1,
                require_email: 1,
                success_message: 'Cảm ơn bạn đã tham gia khảo sát test!'
            };

            const response = await axios.post(`${BASE_URL}/survey-configs/create`, surveyData, {
                headers: { Cookie: this.cookies }
            });

            if (response.data.success) {
                this.surveyConfigId = response.data.data.id;
                this.surveySlug = surveyData.survey_url_slug;
                console.log(`✅ Tạo cấu hình khảo sát thành công - ID: ${this.surveyConfigId}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Lỗi tạo cấu hình khảo sát:', error.message);
            return false;
        }
    }

    /**
     * Cấu hình trường khảo sát
     */
    async configSurveyFields() {
        try {
            console.log('⚙️ Cấu hình trường khảo sát...');
            const fields = [
                {
                    field_name: 'ho_ten',
                    field_label: 'Họ và tên',
                    field_type: 'text',
                    is_required: true,
                    placeholder: 'Nhập họ và tên của bạn',
                    help_text: '',
                    field_options: [],
                    validation_rules: {},
                    field_settings: {}
                },
                {
                    field_name: 'email',
                    field_label: 'Email',
                    field_type: 'email',
                    is_required: true,
                    placeholder: '<EMAIL>',
                    help_text: '',
                    field_options: [],
                    validation_rules: {},
                    field_settings: {}
                },
                {
                    field_name: 'tuoi',
                    field_label: 'Tuổi',
                    field_type: 'number',
                    is_required: false,
                    placeholder: 'Nhập tuổi',
                    help_text: '',
                    field_options: [],
                    validation_rules: {},
                    field_settings: {}
                },
                {
                    field_name: 'gioi_tinh',
                    field_label: 'Giới tính',
                    field_type: 'radio',
                    is_required: true,
                    placeholder: '',
                    help_text: '',
                    field_options: [
                        { value: 'nam', label: 'Nam' },
                        { value: 'nu', label: 'Nữ' },
                        { value: 'khac', label: 'Khác' }
                    ],
                    validation_rules: {},
                    field_settings: {}
                },
                {
                    field_name: 'so_thich',
                    field_label: 'Sở thích',
                    field_type: 'checkbox',
                    is_required: false,
                    placeholder: '',
                    help_text: 'Chọn nhiều sở thích',
                    field_options: [
                        { value: 'doc_sach', label: 'Đọc sách' },
                        { value: 'xem_phim', label: 'Xem phim' },
                        { value: 'the_thao', label: 'Thể thao' },
                        { value: 'du_lich', label: 'Du lịch' }
                    ],
                    validation_rules: {},
                    field_settings: {}
                },
                {
                    field_name: 'ghi_chu',
                    field_label: 'Ghi chú',
                    field_type: 'textarea',
                    is_required: false,
                    placeholder: 'Nhập ghi chú của bạn',
                    help_text: '',
                    field_options: [],
                    validation_rules: {},
                    field_settings: {}
                }
            ];

            const response = await axios.post(`${BASE_URL}/survey-configs/save-fields`, {
                survey_config_id: this.surveyConfigId,
                fields: fields
            }, {
                headers: { Cookie: this.cookies }
            });

            if (response.data.success) {
                console.log('✅ Cấu hình trường thành công');
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Lỗi cấu hình trường:', error.message);
            return false;
        }
    }

    /**
     * Test gửi khảo sát
     */
    async submitSurvey() {
        try {
            console.log('📤 Test gửi khảo sát...');
            const surveyData = {
                ho_ten: 'Nguyễn Văn Test',
                email: '<EMAIL>',
                tuoi: '25',
                gioi_tinh: 'nam',
                so_thich: ['doc_sach', 'the_thao'],
                ghi_chu: 'Đây là test khảo sát từ script tự động'
            };

            const response = await axios.post(`${BASE_URL}/survey/${this.surveySlug}/submit`, surveyData);

            if (response.data.success) {
                console.log('✅ Gửi khảo sát thành công');
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Lỗi gửi khảo sát:', error.message);
            return false;
        }
    }

    /**
     * Test xem phản hồi
     */
    async viewResponses() {
        try {
            console.log('👀 Test xem phản hồi...');
            const response = await axios.get(`${BASE_URL}/survey-configs/${this.surveyConfigId}/responses`, {
                headers: { Cookie: this.cookies }
            });

            if (response.status === 200) {
                console.log('✅ Xem phản hồi thành công');
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Lỗi xem phản hồi:', error.message);
            return false;
        }
    }

    /**
     * Test xuất Excel
     */
    async exportExcel() {
        try {
            console.log('📊 Test xuất Excel...');
            const response = await axios.get(`${BASE_URL}/projects/${this.projectId}/survey-data/export`, {
                headers: { Cookie: this.cookies },
                responseType: 'arraybuffer'
            });

            if (response.status === 200 && response.data.byteLength > 0) {
                // Lưu file test
                const fileName = `test-export-${Date.now()}.xlsx`;
                const filePath = path.join(__dirname, fileName);
                fs.writeFileSync(filePath, response.data);
                console.log(`✅ Xuất Excel thành công - File: ${fileName}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Lỗi xuất Excel:', error.message);
            return false;
        }
    }

    /**
     * Chạy toàn bộ test
     */
    async runFullTest() {
        console.log('🚀 Bắt đầu test toàn bộ hệ thống khảo sát...\n');

        const steps = [
            { name: 'Đăng nhập', method: this.login },
            { name: 'Tạo dự án', method: this.createTestProject },
            { name: 'Tạo cấu hình khảo sát', method: this.createSurveyConfig },
            { name: 'Cấu hình trường', method: this.configSurveyFields },
            { name: 'Gửi khảo sát', method: this.submitSurvey },
            { name: 'Xem phản hồi', method: this.viewResponses },
            { name: 'Xuất Excel', method: this.exportExcel }
        ];

        let passedSteps = 0;
        for (const step of steps) {
            const result = await step.method.call(this);
            if (result) {
                passedSteps++;
            } else {
                console.log(`❌ Bước "${step.name}" thất bại`);
                break;
            }
            console.log(''); // Empty line for readability
        }

        console.log(`\n📊 Kết quả test: ${passedSteps}/${steps.length} bước thành công`);
        
        if (passedSteps === steps.length) {
            console.log('🎉 Tất cả các bước test đều thành công!');
            console.log(`🔗 Link khảo sát: ${BASE_URL}/survey/${this.surveySlug}`);
        } else {
            console.log('⚠️ Một số bước test thất bại, cần kiểm tra lại hệ thống');
        }
    }
}

// Chạy test nếu file được gọi trực tiếp
if (require.main === module) {
    const tester = new SurveySystemTester();
    tester.runFullTest().catch(console.error);
}

module.exports = SurveySystemTester;
