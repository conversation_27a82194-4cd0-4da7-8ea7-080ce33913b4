const aiMenuService = require('../services/aiMenuService');

async function testImprovedAIMenu() {
    console.log('🧪 Testing Improved AI Menu Generation...\n');

    try {
        // Test với yêu cầu của user
        console.log('1️⃣ Testing user request...');
        const userRequest = {
            requirements: "Tạo thực đơn cho 1 bữa ăn cho học sinh trung học cơ sở",
            preferences: [],
            restrictions: [],
            meal_count: 3,
            target_nutrition: {
                energy: 1800,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };

        console.log('Request:', JSON.stringify(userRequest, null, 2));

        const result = await aiMenuService.generateSmartMenu(userRequest);

        if (result.success) {
            const menu = result.data.menu;
            const nutrition = result.data.nutrition_summary;

            console.log(`   ✅ Menu generated: ${menu.name}`);
            console.log(`   📊 Meals: ${menu.detail.length}`);
            console.log(`   📊 Energy: ${nutrition.energy} kcal (target: 1800)`);
            console.log(`   📊 Protein: ${nutrition.protein}g`);

            // Check meal structure
            const mealIds = menu.detail.map(m => m.id);
            console.log(`   📊 Meal IDs: [${mealIds.join(', ')}]`);
            console.log(`   📊 Expected: [3, 5, 7] (Sáng, Trưa, Tối)`);

            if (JSON.stringify(mealIds) === JSON.stringify([3, 5, 7])) {
                console.log('   ✅ MenuTime mapping CORRECT');
            } else {
                console.log('   ❌ MenuTime mapping INCORRECT');
            }

            // Check food/dish usage
            let totalFoods = 0;
            let totalDishes = 0;
            let totalItems = 0;

            menu.detail.forEach(meal => {
                console.log(`\n   🍽️  ${meal.name}:`);
                meal.listFood.forEach(food => {
                    totalItems++;
                    if (food.id_food && food.id_food.toString().startsWith('dish_')) {
                        totalDishes++;
                        console.log(`      - 🍲 ${food.name} (${food.weight} khẩu phần, ${food.energy} kcal)`);
                    } else {
                        totalFoods++;
                        console.log(`      - 🥘 ${food.name} (${food.weight}g, ${food.energy} kcal)`);
                    }
                });
            });

            console.log(`\n   📊 Total items: ${totalItems}`);
            console.log(`   📊 Foods: ${totalFoods}, Dishes: ${totalDishes}`);

            // Check energy accuracy
            const energyDiff = Math.abs(nutrition.energy - 1800);
            const energyAccuracy = ((1800 - energyDiff) / 1800) * 100;
            console.log(`   📊 Energy accuracy: ${energyAccuracy.toFixed(1)}%`);

            if (energyDiff <= 200) {
                console.log('   ✅ Energy target ACCEPTABLE (±200 kcal)');
            } else {
                console.log('   ⚠️  Energy target needs improvement');
            }

            // Check nutrition summary display
            if (nutrition.energy > 0 && nutrition.protein > 0) {
                console.log('   ✅ Nutrition summary working');
            } else {
                console.log('   ❌ Nutrition summary not working');
            }

        } else {
            console.log(`   ❌ Menu generation failed: ${result.message}`);
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testImprovedAIMenu();
