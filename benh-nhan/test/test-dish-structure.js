const pineconeService = require('../services/pineconeService');

async function testDishStructure() {
    console.log('🧪 Testing Dish Data Structure...\n');

    try {
        // 1. Test dish search and examine structure
        console.log('1️⃣ Testing dish search structure...');
        const dishResult = await pineconeService.searchDishes('cơm gà', { topK: 3 });
        
        if (dishResult.success && dishResult.data.length > 0) {
            console.log(`   ✅ Found ${dishResult.data.length} dishes`);
            
            dishResult.data.forEach((dish, index) => {
                console.log(`\n   🍽️  Dish ${index + 1}:`);
                console.log('      Structure:', JSON.stringify(dish, null, 2));
            });
        }

        // 2. Test combined search and examine structure
        console.log('\n2️⃣ Testing combined search structure...');
        const combinedResult = await pineconeService.searchFoodsAndDishes('cơm gà', {
            topK: 6,
            foodLimit: 3,
            dishLimit: 3
        });
        
        if (combinedResult.success && combinedResult.data.length > 0) {
            console.log(`   ✅ Found ${combinedResult.data.length} items total`);
            console.log('   📊 Metadata:', combinedResult.metadata);
            
            combinedResult.data.forEach((item, index) => {
                console.log(`\n   📦 Item ${index + 1}:`);
                console.log(`      Type: ${item.type}`);
                console.log(`      Name: ${item.name}`);
                if (item.type === 'dish') {
                    console.log(`      Energy: ${item.total_energy} kcal`);
                } else {
                    console.log(`      Energy: ${item.energy} kcal/100g`);
                }
            });
            
            // Count by type
            const foods = combinedResult.data.filter(item => item.type === 'food');
            const dishes = combinedResult.data.filter(item => item.type === 'dish');
            
            console.log(`\n   📊 Final count: ${foods.length} foods + ${dishes.length} dishes`);
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack:', error.stack);
    }
}

testDishStructure();
