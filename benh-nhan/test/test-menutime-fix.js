const aiMenuService = require('../services/aiMenuService');

async function testMenuTimeMapping() {
    console.log('🧪 Testing MenuTime Mapping Fix...\n');

    try {
        // Test 3 meals - should be [3, 5, 7] (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
        console.log('1️⃣ Testing 3 meals (should be Sáng=3, Trưa=5, Tối=7)...');
        const result3 = await aiMenuService.generateSmartMenu({
            requirements: 'Tạo thực đơn 3 bữa ăn cân bằng',
            meal_count: 3,
            target_nutrition: { energy: 1800 }
        });

        if (result3.success) {
            const mealIds = result3.data.menu.detail.map(meal => meal.id);
            const mealNames = result3.data.menu.detail.map(meal => meal.name);
            console.log(`   📊 Meal IDs: [${mealIds.join(', ')}]`);
            console.log(`   📊 Meal Names: [${mealNames.join(', ')}]`);
            console.log(`   📊 Expected: [3, 5, 7] (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)`);
            
            if (JSON.stringify(mealIds) === JSON.stringify([3, 5, 7])) {
                console.log('   ✅ 3 meals mapping CORRECT');
            } else {
                console.log('   ❌ 3 meals mapping INCORRECT');
            }
        } else {
            console.log('   ❌ Failed to generate 3-meal menu');
        }

        // Test 5 meals - should be [3, 4, 5, 6, 7] (Sáng, Phụ 1, Trưa, Phụ 2, Tối)
        console.log('\n2️⃣ Testing 5 meals (should be Sáng=3, Phụ 1=4, Trưa=5, Phụ 2=6, Tối=7)...');
        const result5 = await aiMenuService.generateSmartMenu({
            requirements: 'Tạo thực đơn 5 bữa ăn cho bệnh nhân tiểu đường',
            meal_count: 5,
            target_nutrition: { energy: 1800 }
        });

        if (result5.success) {
            const mealIds = result5.data.menu.detail.map(meal => meal.id);
            const mealNames = result5.data.menu.detail.map(meal => meal.name);
            console.log(`   📊 Meal IDs: [${mealIds.join(', ')}]`);
            console.log(`   📊 Meal Names: [${mealNames.join(', ')}]`);
            console.log(`   📊 Expected: [3, 4, 5, 6, 7] (Sáng, Phụ 1, Trưa, Phụ 2, Tối)`);
            
            if (JSON.stringify(mealIds) === JSON.stringify([3, 4, 5, 6, 7])) {
                console.log('   ✅ 5 meals mapping CORRECT');
            } else {
                console.log('   ❌ 5 meals mapping INCORRECT');
            }
        } else {
            console.log('   ❌ Failed to generate 5-meal menu');
        }

        console.log('\n🎉 MenuTime mapping test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testMenuTimeMapping();
