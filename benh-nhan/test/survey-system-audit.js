/**
 * Survey System Audit Script
 * Kiểm tra toàn bộ hệ thống survey để tìm và khắc phục các lỗi
 */

const fs = require('fs');
const path = require('path');

class SurveySystemAuditor {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.suggestions = [];
    }

    /**
     * Chạy audit toàn bộ hệ thống
     */
    async runFullAudit() {
        console.log('🔍 Bắt đầu audit hệ thống survey...\n');

        // Kiểm tra cấu trúc file
        this.checkFileStructure();
        
        // Kiểm tra database schema
        this.checkDatabaseSchema();
        
        // Kiểm tra controllers
        this.checkControllers();
        
        // Kiểm tra routes
        this.checkRoutes();
        
        // Kiểm tra views
        this.checkViews();
        
        // Kiểm tra JavaScript
        this.checkJavaScript();
        
        // Kiểm tra CSS
        this.checkCSS();
        
        // Tạo báo cáo
        this.generateReport();
    }

    /**
     * <PERSON>ể<PERSON> tra cấu trúc file
     */
    checkFileStructure() {
        console.log('📁 Kiểm tra cấu trúc file...');
        
        const requiredFiles = [
            'controllers/surveyController.js',
            'controllers/surveyConfigController.js',
            'controllers/surveyDataController.js',
            'services/sqliteService.js',
            'services/googleSheetsService.js',
            'views/survey/public-form.ejs',
            'views/survey-configs/index.ejs',
            'views/survey-configs/create.ejs',
            'views/survey-configs/edit.ejs',
            'views/survey-configs/fields-config.ejs',
            'views/survey-configs/responses.ejs',
            'public/js/survey-system.js',
            'public/css/survey-system.css',
            'database/migrations/2025_08_19_survey_system.sql',
            'database/migrations/2025_08_28_survey_enhancements.sql'
        ];

        requiredFiles.forEach(file => {
            if (!fs.existsSync(file)) {
                this.errors.push(`❌ File thiếu: ${file}`);
            } else {
                console.log(`✅ ${file}`);
            }
        });
    }

    /**
     * Kiểm tra database schema
     */
    checkDatabaseSchema() {
        console.log('\n🗄️ Kiểm tra database schema...');
        
        const migrationFile = 'database/migrations/2025_08_28_survey_enhancements.sql';
        if (fs.existsSync(migrationFile)) {
            const content = fs.readFileSync(migrationFile, 'utf8');
            
            // Kiểm tra các bảng cần thiết
            const requiredTables = [
                'survey_configs',
                'survey_fields', 
                'survey_responses',
                'survey_response_data',
                'survey_field_groups'
            ];
            
            requiredTables.forEach(table => {
                if (content.includes(table)) {
                    console.log(`✅ Bảng ${table} có trong migration`);
                } else {
                    this.warnings.push(`⚠️ Bảng ${table} không tìm thấy trong migration`);
                }
            });

            // Kiểm tra các cột mới
            const newColumns = ['field_group', 'conditional_logic', 'group_instance'];
            newColumns.forEach(column => {
                if (content.includes(column)) {
                    console.log(`✅ Cột ${column} đã được thêm`);
                } else {
                    this.warnings.push(`⚠️ Cột ${column} chưa được thêm`);
                }
            });
        } else {
            this.errors.push('❌ File migration enhancement không tồn tại');
        }
    }

    /**
     * Kiểm tra controllers
     */
    checkControllers() {
        console.log('\n🎮 Kiểm tra controllers...');
        
        const controllers = [
            'controllers/surveyController.js',
            'controllers/surveyConfigController.js', 
            'controllers/surveyDataController.js'
        ];

        controllers.forEach(controllerPath => {
            if (fs.existsSync(controllerPath)) {
                const content = fs.readFileSync(controllerPath, 'utf8');
                
                // Kiểm tra các method cần thiết
                const requiredMethods = {
                    'surveyController.js': ['getPublicSurvey', 'submitPublicSurvey'],
                    'surveyConfigController.js': ['getList', 'create', 'getFieldsConfig', 'saveFieldsConfig', 'getResponses'],
                    'surveyDataController.js': ['getList', 'getListTable', 'exportExcel']
                };

                const fileName = path.basename(controllerPath);
                if (requiredMethods[fileName]) {
                    requiredMethods[fileName].forEach(method => {
                        if (content.includes(method)) {
                            console.log(`✅ ${fileName}: ${method}`);
                        } else {
                            this.errors.push(`❌ ${fileName}: Thiếu method ${method}`);
                        }
                    });
                }

                // Kiểm tra xử lý lỗi
                if (!content.includes('try {') || !content.includes('catch')) {
                    this.warnings.push(`⚠️ ${fileName}: Thiếu xử lý lỗi try-catch`);
                }

                // Kiểm tra security
                if (!content.includes('securityService')) {
                    this.warnings.push(`⚠️ ${fileName}: Thiếu security service`);
                }
            }
        });
    }

    /**
     * Kiểm tra routes
     */
    checkRoutes() {
        console.log('\n🛣️ Kiểm tra routes...');
        
        if (fs.existsSync('routes/index.js')) {
            const content = fs.readFileSync('routes/index.js', 'utf8');
            
            const requiredRoutes = [
                '/survey/:slug',
                '/survey/:slug/submit',
                '/survey-configs/:id/fields',
                '/survey-configs/save-fields',
                '/survey-configs/:id/responses',
                '/projects/:projectId/survey-data/export'
            ];

            requiredRoutes.forEach(route => {
                if (content.includes(route.replace(/:/g, ''))) {
                    console.log(`✅ Route: ${route}`);
                } else {
                    this.errors.push(`❌ Route thiếu: ${route}`);
                }
            });
        } else {
            this.errors.push('❌ File routes/index.js không tồn tại');
        }
    }

    /**
     * Kiểm tra views
     */
    checkViews() {
        console.log('\n👁️ Kiểm tra views...');
        
        const views = [
            'views/survey/public-form.ejs',
            'views/survey-configs/fields-config.ejs',
            'views/survey-configs/responses.ejs'
        ];

        views.forEach(viewPath => {
            if (fs.existsSync(viewPath)) {
                const content = fs.readFileSync(viewPath, 'utf8');
                
                // Kiểm tra các element cần thiết
                if (viewPath.includes('public-form')) {
                    if (!content.includes('survey-field')) {
                        this.warnings.push(`⚠️ ${viewPath}: Thiếu class survey-field`);
                    }
                    if (!content.includes('required')) {
                        this.warnings.push(`⚠️ ${viewPath}: Thiếu xử lý required fields`);
                    }
                }

                if (viewPath.includes('fields-config')) {
                    if (!content.includes('field-label')) {
                        this.warnings.push(`⚠️ ${viewPath}: Thiếu input field-label`);
                    }
                    if (!content.includes('field-name')) {
                        this.warnings.push(`⚠️ ${viewPath}: Thiếu input field-name`);
                    }
                }

                console.log(`✅ ${viewPath}`);
            } else {
                this.errors.push(`❌ View thiếu: ${viewPath}`);
            }
        });
    }

    /**
     * Kiểm tra JavaScript
     */
    checkJavaScript() {
        console.log('\n📜 Kiểm tra JavaScript...');
        
        if (fs.existsSync('public/js/survey-system.js')) {
            const content = fs.readFileSync('public/js/survey-system.js', 'utf8');
            
            const requiredFunctions = [
                'initializeFieldConfiguration',
                'generateFieldNameFromLabel',
                'submitPublicSurvey',
                'initializeConditionalLogic',
                'saveFieldConfiguration'
            ];

            requiredFunctions.forEach(func => {
                if (content.includes(func)) {
                    console.log(`✅ Function: ${func}`);
                } else {
                    this.errors.push(`❌ Function thiếu: ${func}`);
                }
            });

            // Kiểm tra xử lý double submission
            if (content.includes('prop(\'disabled\')')) {
                console.log('✅ Có xử lý double submission');
            } else {
                this.warnings.push('⚠️ Thiếu xử lý double submission');
            }

            // Kiểm tra conditional logic
            if (content.includes('conditional_logic')) {
                console.log('✅ Có hỗ trợ conditional logic');
            } else {
                this.warnings.push('⚠️ Thiếu hỗ trợ conditional logic');
            }
        } else {
            this.errors.push('❌ File public/js/survey-system.js không tồn tại');
        }
    }

    /**
     * Kiểm tra CSS
     */
    checkCSS() {
        console.log('\n🎨 Kiểm tra CSS...');
        
        if (fs.existsSync('public/css/survey-system.css')) {
            const content = fs.readFileSync('public/css/survey-system.css', 'utf8');
            
            const requiredClasses = [
                '.survey-field',
                '.required',
                '.field-item',
                '.survey-success'
            ];

            requiredClasses.forEach(className => {
                if (content.includes(className)) {
                    console.log(`✅ CSS class: ${className}`);
                } else {
                    this.warnings.push(`⚠️ CSS class thiếu: ${className}`);
                }
            });

            // Kiểm tra responsive design
            if (content.includes('@media')) {
                console.log('✅ Có responsive design');
            } else {
                this.warnings.push('⚠️ Thiếu responsive design');
            }
        } else {
            this.errors.push('❌ File public/css/survey-system.css không tồn tại');
        }
    }

    /**
     * Tạo báo cáo
     */
    generateReport() {
        console.log('\n📊 Báo cáo audit:\n');
        
        console.log(`🔴 Lỗi nghiêm trọng: ${this.errors.length}`);
        this.errors.forEach(error => console.log(error));
        
        console.log(`\n🟡 Cảnh báo: ${this.warnings.length}`);
        this.warnings.forEach(warning => console.log(warning));
        
        console.log(`\n💡 Đề xuất: ${this.suggestions.length}`);
        this.suggestions.forEach(suggestion => console.log(suggestion));

        // Tạo file báo cáo
        const report = {
            timestamp: new Date().toISOString(),
            errors: this.errors,
            warnings: this.warnings,
            suggestions: this.suggestions,
            summary: {
                total_errors: this.errors.length,
                total_warnings: this.warnings.length,
                total_suggestions: this.suggestions.length,
                status: this.errors.length === 0 ? 'PASS' : 'FAIL'
            }
        };

        fs.writeFileSync('test/survey-audit-report.json', JSON.stringify(report, null, 2));
        console.log('\n📄 Báo cáo đã được lưu vào test/survey-audit-report.json');

        if (this.errors.length === 0) {
            console.log('\n🎉 Hệ thống survey đã sẵn sàng!');
        } else {
            console.log('\n⚠️ Cần khắc phục các lỗi trước khi sử dụng!');
        }
    }
}

// Chạy audit nếu file được gọi trực tiếp
if (require.main === module) {
    const auditor = new SurveySystemAuditor();
    auditor.runFullAudit().catch(console.error);
}

module.exports = SurveySystemAuditor;
