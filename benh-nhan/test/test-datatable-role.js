// File test để kiểm tra format dữ liệu role trong DataTable
const commonService = require('../services/commonService');

async function testDataTableRole() {
    console.log('=== Testing DataTable Role Format ===');
    
    try {
        // Simulate DataTable request parameters
        const parameter = {
            table: 'user',
            columns: ['id', 'fullname', 'email', 'active'],
            primaryKey: 'id',
            active: -1,
            activeOperator: '!=',
            filters: {},
            search: { value: '' },
            order: [{ column: 1, dir: 'DESC' }],
            start: 0,
            length: 15,
            draw: 1
        };
        
        console.log('\n1. Testing getDataTableData...');
        const responseData = await commonService.getDataTableData(parameter);
        
        if (responseData.success && responseData.data) {
            console.log('DataTable response success');
            console.log('Number of users:', responseData.data.length);
            
            // Simulate role processing
            console.log('\n2. Testing role processing...');
            const mockRoleUser = [
                { user_id: 1, role_id: 1 },
                { user_id: 1, role_id: 2 },
                { user_id: 2, role_id: 3 },
                { user_id: 3, role_id: 1 }
            ];
            
            for(let item of responseData.data){
                let userRoles = mockRoleUser.filter(r => item.id == r.user_id).map(r => r.role_id);
                item['role'] = userRoles;
                console.log(`User ${item.id} (${item.fullname}): roles =`, userRoles);
            }
            
            // Ensure all users have role property
            console.log('\n3. Ensuring all users have role property...');
            for(let item of responseData.data){
                if(!item.hasOwnProperty('role')){
                    item['role'] = [];
                }
                console.log(`User ${item.id}: role property exists =`, item.hasOwnProperty('role'), 'value =', item.role);
            }
            
            // Test DataTable render function
            console.log('\n4. Testing DataTable render function...');
            const roleLabels = {
                1: 'Admin',
                2: 'Khách hàng', 
                3: 'Viêm gan',
                4: 'Uốn ván',
                5: 'Hội chuẩn',
                6: 'Viêm gan Mt1',
                7: 'Đánh giá KPA',
                8: 'Phiếu hội chuẩn'
            };
            
            for(let item of responseData.data){
                const renderResult = (() => {
                    if (!item.role || !Array.isArray(item.role)) {
                        return '';
                    }
                    return item.role.map(roleId => roleLabels[roleId] || `Role ${roleId}`).join(', ');
                })();
                console.log(`User ${item.id}: render result = "${renderResult}"`);
            }
            
        } else {
            console.log('DataTable response failed:', responseData.message);
        }
        
    } catch (error) {
        console.error('Error testing DataTable role format:', error);
    }
    
    console.log('\n=== DataTable Role Format Test Completed ===');
}

// Chạy test
testDataTableRole(); 