<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Action Column Fix & Bold Labels</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/table-config.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fc;
            font-family: 'Nunito', sans-serif;
        }
        .demo-container {
            max-width: 400px;
            margin: 20px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
        }
        .comparison-section {
            padding: 20px;
        }
        .layout-before {
            border: 2px solid #dc3545;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 15px;
        }
        .layout-after {
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
        }
        .layout-title {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        .before-style {
            color: #dc3545;
        }
        .after-style {
            color: #28a745;
        }
        
        /* Before styling */
        .before-cell {
            padding: 12px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .before-cell .label {
            display: block;
            font-weight: 600;
            color: #5a5c69;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }
        .before-cell .content {
            display: block;
            color: #3a3b45;
            font-size: 0.95rem;
        }
        
        /* After styling */
        .after-cell {
            padding: 12px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .after-cell .label {
            display: block;
            font-weight: 700;
            color: #2c3e50;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            margin-bottom: 6px;
            font-family: 'Nunito', sans-serif;
        }
        .after-cell .content {
            display: block;
            color: #3a3b45;
            font-size: 0.95rem;
            line-height: 1.4;
        }
        
        .btn-demo {
            padding: 4px 8px;
            font-size: 0.75rem;
            margin-right: 5px;
        }
        
        .highlight-action {
            background: #fff3cd;
            border: 2px dashed #ffc107;
            border-radius: 4px;
            padding: 8px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-sort"></i> 
                    Demo Action Column Fix & Bold Labels
                </h1>
                <p class="text-center text-muted">So sánh trước và sau khi sửa thứ tự cột "Thao tác" và bôi đậm tiêu đề</p>
            </div>
        </div>
        
        <div class="row">
            <!-- Trước khi sửa -->
            <div class="col-md-6">
                <div class="demo-container">
                    <div class="demo-header">
                        <h5 class="mb-0 before-style">
                            <i class="fas fa-times-circle"></i> Trước khi sửa
                        </h5>
                    </div>
                    <div class="comparison-section">
                        <div class="layout-before">
                            <div class="layout-title before-style">Mobile Card</div>
                            
                            <div class="before-cell highlight-action">
                                <div class="label">Thao tác</div>
                                <div class="content">
                                    <button class="btn btn-info btn-demo">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-demo">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="before-cell">
                                <div class="label">Họ tên</div>
                                <div class="content">Nguyễn Văn A</div>
                            </div>
                            
                            <div class="before-cell">
                                <div class="label">Điện thoại</div>
                                <div class="content">0123456789</div>
                            </div>
                            
                            <div class="before-cell" style="border-bottom: none;">
                                <div class="label">Chẩn đoán</div>
                                <div class="content">Viêm gan B mạn tính</div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <small>
                                <strong>Vấn đề:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>❌ Thao tác ở đầu thay vì cuối</li>
                                    <li>❌ Labels không đủ đậm</li>
                                    <li>❌ Khó phân biệt label/content</li>
                                </ul>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sau khi sửa -->
            <div class="col-md-6">
                <div class="demo-container">
                    <div class="demo-header">
                        <h5 class="mb-0 after-style">
                            <i class="fas fa-check-circle"></i> Sau khi sửa
                        </h5>
                    </div>
                    <div class="comparison-section">
                        <div class="layout-after">
                            <div class="layout-title after-style">Mobile Card</div>
                            
                            <div class="after-cell">
                                <div class="label">Họ tên</div>
                                <div class="content">Nguyễn Văn A</div>
                            </div>
                            
                            <div class="after-cell">
                                <div class="label">Điện thoại</div>
                                <div class="content">0123456789</div>
                            </div>
                            
                            <div class="after-cell">
                                <div class="label">Chẩn đoán</div>
                                <div class="content">Viêm gan B mạn tính</div>
                            </div>
                            
                            <div class="after-cell highlight-action" style="border-bottom: none;">
                                <div class="label">Thao tác</div>
                                <div class="content">
                                    <button class="btn btn-info btn-demo">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-demo">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success">
                            <small>
                                <strong>Đã sửa:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>✅ Thao tác chuyển xuống cuối</li>
                                    <li>✅ Labels đậm hơn (font-weight: 700)</li>
                                    <li>✅ Font size lớn hơn (0.9rem)</li>
                                    <li>✅ Contrast tốt hơn</li>
                                </ul>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-code"></i> Thay đổi kỹ thuật
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-js"></i> JavaScript Changes:</h6>
                                <pre class="bg-light p-3 rounded"><code>// Thêm function reorder cells
reorderCellsForMobile(row, cells, headerTexts) {
    if (window.innerWidth <= 768) {
        const actionColumnIndex = 
            this.findActionColumnIndex(headerTexts);
        
        if (actionColumnIndex === 0) {
            // Move action cell to end
            const actionCell = cellsArray[0];
            actionCell.remove();
            row.appendChild(actionCell);
        }
    }
}</code></pre>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-css3-alt"></i> CSS Changes:</h6>
                                <pre class="bg-light p-3 rounded"><code>/* Bold labels */
td:before {
    font-weight: 700;      /* 600 → 700 */
    color: #2c3e50;        /* Darker */
    font-size: 0.9rem;     /* Larger */
    letter-spacing: 0.8px; /* More space */
    margin-bottom: 6px;    /* More space */
    font-family: 'Nunito'; /* Consistent */
}</code></pre>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-lightbulb"></i> Kết quả:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Thứ tự cột:</strong></p>
                                    <ul class="mb-0">
                                        <li>Tự động detect cột "Thao tác"</li>
                                        <li>Chuyển từ đầu xuống cuối trên mobile</li>
                                        <li>Giữ nguyên thứ tự trên desktop</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Labels đậm:</strong></p>
                                    <ul class="mb-0">
                                        <li>Font weight tăng từ 600 → 700</li>
                                        <li>Font size tăng từ 0.85rem → 0.9rem</li>
                                        <li>Màu sắc tương phản cao hơn</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-mobile-alt"></i> Cách test
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Mở table view:</strong> Truy cập bất kỳ view nào có table (ví dụ: /patient/list)</li>
                            <li><strong>Chuyển mobile:</strong> Mở Developer Tools (F12) → Toggle device toolbar (Ctrl+Shift+M)</li>
                            <li><strong>Set mobile width:</strong> Chọn mobile device hoặc set width < 768px</li>
                            <li><strong>Kiểm tra:</strong>
                                <ul>
                                    <li>✅ Cột "Thao tác" xuất hiện ở cuối card</li>
                                    <li>✅ Labels được bôi đậm và rõ ràng</li>
                                    <li>✅ Layout dễ đọc và thoáng đãng</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
