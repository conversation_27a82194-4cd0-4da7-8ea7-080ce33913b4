const commonService = require('../services/commonService');

async function testDatabaseSchema() {
    console.log('🧪 Testing Database Schema...\n');

    try {
        // Connect to database
        await commonService.connect();
        console.log('✅ Database connected');

        // Test food_info table
        console.log('\n1️⃣ Testing food_info table...');
        const foodResult = await commonService.getAllDataTable('food_info', { id: 1 });
        
        if (foodResult.success && foodResult.data.length > 0) {
            const food = foodResult.data[0];
            console.log('   Sample food record:');
            console.log(`   - ID: ${food.id}`);
            console.log(`   - Name: ${food.name}`);
            console.log(`   - Energy: ${food.energy}`);
            console.log(`   - Protein: ${food.protein}`);
            console.log(`   - Carbohydrate: ${food.carbohydrate}`);
            console.log(`   - Fat: ${food.fat}`);
            console.log(`   - Lipid: ${food.lipid}`);
            
            const hasFat = typeof food.fat !== 'undefined';
            const hasLipid = typeof food.lipid !== 'undefined';
            
            console.log(`   📊 Has fat field: ${hasFat}`);
            console.log(`   📊 Has lipid field: ${hasLipid}`);
            
            if (hasFat && !hasLipid) {
                console.log('   ✅ Database schema updated to fat');
            } else if (!hasFat && hasLipid) {
                console.log('   ⚠️  Database still uses lipid');
            } else if (hasFat && hasLipid) {
                console.log('   ⚠️  Database has both fat and lipid');
            } else {
                console.log('   ❌ Database has neither fat nor lipid');
            }
        }

        // Test dishes table
        console.log('\n2️⃣ Testing dishes table...');
        const dishResult = await commonService.getAllDataTable('dishes', { id: 1 });
        
        if (dishResult.success && dishResult.data.length > 0) {
            const dish = dishResult.data[0];
            console.log('   Sample dish record:');
            console.log(`   - ID: ${dish.id}`);
            console.log(`   - Name: ${dish.name}`);
            
            // Check if dishes table has nutrition fields
            const hasNutritionFields = typeof dish.total_energy !== 'undefined';
            console.log(`   📊 Has nutrition fields: ${hasNutritionFields}`);
        }

        console.log('\n🎉 Database schema test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testDatabaseSchema();
