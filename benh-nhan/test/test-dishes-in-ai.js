const aiMenuService = require('../services/aiMenuService');
const pineconeService = require('../services/pineconeService');
const dishService = require('../services/dishService');

async function testDishesInAI() {
    console.log('🧪 Testing Dishes in AI Menu Generation...\n');

    try {
        // 1. Check if dishes exist in Pinecone
        console.log('1️⃣ Checking dishes in Pinecone...');
        const dishSearchResult = await pineconeService.searchDishes('canh chua', { topK: 5 });
        
        if (dishSearchResult.success && dishSearchResult.data.length > 0) {
            console.log(`   ✅ Found ${dishSearchResult.data.length} dishes in Pinecone`);
            dishSearchResult.data.forEach((dish, index) => {
                console.log(`   🍽️  ${index + 1}. ${dish.name} (${dish.total_energy} kcal)`);
            });
        } else {
            console.log('   ❌ No dishes found in Pinecone - need to sync first');
            
            // Try to sync dishes
            console.log('   🔄 Attempting to sync dishes...');
            const syncResult = await dishService.syncDishesToPinecone();
            if (syncResult.success) {
                console.log('   ✅ Dishes synced successfully');
            } else {
                console.log(`   ❌ Dish sync failed: ${syncResult.message}`);
            }
        }

        // 2. Test combined search (foods + dishes)
        console.log('\n2️⃣ Testing combined search...');
        const combinedResult = await pineconeService.searchFoodsAndDishes('cơm gà canh', {
            topK: 10,
            foodLimit: 6,
            dishLimit: 4
        });

        if (combinedResult.success) {
            const foods = combinedResult.data.filter(item => item.type === 'food');
            const dishes = combinedResult.data.filter(item => item.type === 'dish');
            
            console.log(`   ✅ Combined search: ${foods.length} foods + ${dishes.length} dishes`);
            
            if (dishes.length > 0) {
                console.log('   🍽️  Dishes found:');
                dishes.forEach(dish => {
                    console.log(`      - ${dish.name} (${dish.total_energy} kcal)`);
                });
            }
        } else {
            console.log(`   ❌ Combined search failed: ${combinedResult.message}`);
        }

        // 3. Test AI menu generation with dishes
        console.log('\n3️⃣ Testing AI menu with dishes...');
        const menuResult = await aiMenuService.generateSmartMenu({
            requirements: 'Tạo thực đơn có cơm gà và canh chua cho học sinh',
            meal_count: 3,
            target_nutrition: { energy: 1800 }
        });

        if (menuResult.success) {
            const menu = menuResult.data.menu;
            console.log(`   ✅ Menu generated: ${menu.name}`);
            
            let totalFoods = 0;
            let totalDishes = 0;
            
            menu.detail.forEach(meal => {
                console.log(`   🍽️  ${meal.name}:`);
                meal.listFood.forEach(food => {
                    if (food.id_food && food.id_food.toString().startsWith('dish_')) {
                        totalDishes++;
                        console.log(`      - 🍲 ${food.name} (món ăn)`);
                    } else {
                        totalFoods++;
                        console.log(`      - 🥘 ${food.name} (thực phẩm)`);
                    }
                });
            });
            
            console.log(`   📊 Total: ${totalFoods} foods + ${totalDishes} dishes`);
            
            if (totalDishes > 0) {
                console.log('   ✅ AI is using dishes!');
            } else {
                console.log('   ⚠️  AI is not using dishes yet');
            }

            // Check nutrition summary
            const nutrition = menuResult.data.nutrition_summary;
            console.log(`   📊 Nutrition: ${nutrition.energy} kcal, ${nutrition.protein}g protein`);
            
            if (nutrition.energy > 0) {
                console.log('   ✅ Nutrition summary working');
            } else {
                console.log('   ❌ Nutrition summary not working');
            }

        } else {
            console.log(`   ❌ Menu generation failed: ${menuResult.message}`);
        }

        console.log('\n🎉 Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testDishesInAI();
