const aiMenuService = require('../services/aiMenuService');

async function testSystemIntegration() {
    console.log('🧪 Testing Complete System Integration...\n');

    try {
        // Test 1: AI Menu Generation
        console.log('1️⃣ Testing AI Menu Generation...');
        const request = {
            requirements: "Tạo thực đơn cho học sinh trung học",
            preferences: [],
            restrictions: [],
            meal_count: 3,
            target_nutrition: {
                energy: 1500,
                protein: null,
                carbohydrate: null,
                lipid: null
            }
        };

        const result = await aiMenuService.generateSmartMenu(request);

        if (result.success) {
            const menu = result.data.menu;
            const nutrition = result.data.nutrition_summary;

            console.log(`   ✅ Menu generated: ${menu.name}`);
            console.log(`   📊 Energy: ${nutrition.energy} kcal (target: 1500)`);
            console.log(`   📊 Protein: ${nutrition.protein}g`);
            console.log(`   📊 Lipid: ${nutrition.lipid}g`);

            // Test 2: MenuTime Structure
            console.log('\n2️⃣ Testing MenuTime Structure...');
            const mealIds = menu.detail.map(m => m.id);
            console.log(`   📊 Meal IDs: [${mealIds.join(', ')}]`);
            
            if (JSON.stringify(mealIds) === JSON.stringify([3, 5, 7])) {
                console.log('   ✅ MenuTime mapping CORRECT for 3 meals');
            } else {
                console.log('   ❌ MenuTime mapping INCORRECT');
            }

            // Test 3: Food/Dish Usage
            console.log('\n3️⃣ Testing Food/Dish Usage...');
            let totalFoods = 0;
            let totalDishes = 0;
            let invalidIds = 0;

            menu.detail.forEach(meal => {
                meal.listFood.forEach(food => {
                    if (food.energy === 0 && food.protein === 0) {
                        invalidIds++;
                    } else {
                        if (food.id_food && food.id_food.toString().startsWith('dish_')) {
                            totalDishes++;
                        } else {
                            totalFoods++;
                        }
                    }
                });
            });

            console.log(`   📊 Foods: ${totalFoods}, Dishes: ${totalDishes}`);
            console.log(`   📊 Invalid IDs: ${invalidIds}`);

            if (invalidIds === 0) {
                console.log('   ✅ All IDs are valid');
            } else {
                console.log('   ❌ Some IDs are invalid');
            }

            if (totalDishes > 0) {
                console.log('   ✅ AI is using dishes');
            } else {
                console.log('   ⚠️  AI is not using dishes');
            }

            // Test 4: Lipid to Fat Mapping
            console.log('\n4️⃣ Testing Lipid to Fat Mapping...');
            
            // Simulate addAIMenuToSystem function
            const testMenu = JSON.parse(JSON.stringify(menu)); // Deep copy
            
            testMenu.detail.forEach(meal => {
                meal.listFood.forEach(food => {
                    // Apply mapping logic
                    if (typeof food.lipid !== 'undefined' && typeof food.fat === 'undefined') {
                        food.fat = food.lipid;
                    }
                });
            });

            let hasFatField = false;
            testMenu.detail.forEach(meal => {
                meal.listFood.forEach(food => {
                    if (typeof food.fat !== 'undefined') {
                        hasFatField = true;
                    }
                });
            });

            if (hasFatField) {
                console.log('   ✅ Lipid to Fat mapping works');
            } else {
                console.log('   ❌ Lipid to Fat mapping failed');
            }

            // Test 5: Nutrition Summary
            console.log('\n5️⃣ Testing Nutrition Summary...');
            
            if (nutrition.energy > 0) {
                console.log('   ✅ Energy calculation working');
            } else {
                console.log('   ❌ Energy calculation failed');
            }

            if (nutrition.protein > 0) {
                console.log('   ✅ Protein calculation working');
            } else {
                console.log('   ❌ Protein calculation failed');
            }

            // Test 6: Energy Accuracy
            console.log('\n6️⃣ Testing Energy Accuracy...');
            const energyDiff = Math.abs(nutrition.energy - 1500);
            const energyAccuracy = ((1500 - energyDiff) / 1500) * 100;
            
            console.log(`   📊 Energy accuracy: ${energyAccuracy.toFixed(1)}%`);
            
            if (energyDiff <= 100) {
                console.log('   ✅ Energy target EXCELLENT (±100 kcal)');
            } else if (energyDiff <= 200) {
                console.log('   ✅ Energy target GOOD (±200 kcal)');
            } else if (energyDiff <= 300) {
                console.log('   ⚠️  Energy target ACCEPTABLE (±300 kcal)');
            } else {
                console.log('   ❌ Energy target needs improvement');
            }

            // Overall Assessment
            console.log('\n🎯 OVERALL ASSESSMENT:');
            
            const scores = {
                menuGeneration: result.success ? 1 : 0,
                menuTimeMapping: JSON.stringify(mealIds) === JSON.stringify([3, 5, 7]) ? 1 : 0,
                validIds: invalidIds === 0 ? 1 : 0,
                dishUsage: totalDishes > 0 ? 1 : 0,
                lipidMapping: hasFatField ? 1 : 0,
                nutritionSummary: nutrition.energy > 0 && nutrition.protein > 0 ? 1 : 0,
                energyAccuracy: energyDiff <= 300 ? 1 : 0
            };

            const totalScore = Object.values(scores).reduce((a, b) => a + b, 0);
            const maxScore = Object.keys(scores).length;
            const percentage = (totalScore / maxScore) * 100;

            console.log(`   📊 Score: ${totalScore}/${maxScore} (${percentage.toFixed(1)}%)`);

            if (percentage >= 90) {
                console.log('   🏆 EXCELLENT - System working perfectly!');
            } else if (percentage >= 80) {
                console.log('   🎉 GOOD - System working well with minor issues');
            } else if (percentage >= 70) {
                console.log('   ⚠️  ACCEPTABLE - System working but needs improvement');
            } else {
                console.log('   ❌ POOR - System needs significant fixes');
            }

        } else {
            console.log(`   ❌ Menu generation failed: ${result.message}`);
        }

        console.log('\n🎉 Integration test completed!');

    } catch (error) {
        console.error('❌ Integration test failed:', error.message);
    }
}

testSystemIntegration();
