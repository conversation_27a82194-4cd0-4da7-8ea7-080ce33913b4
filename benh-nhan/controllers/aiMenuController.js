const aiMenuService = require('../services/aiMenuService');
const commonService = require('../services/commonService');

const aiMenuController = {
    // T<PERSON><PERSON> thực đơn bằng AI
    generateMenu: async (req, res) => {
        try {
            const user = req.user;
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: '<PERSON>ui lòng đăng nhập để sử dụng tính năng này'
                });
            }

            // Kiểm tra quyền (chỉ admin và role có quyền tạo thực đơn)
            if (!user.isAdmin && !user.role_id.includes(6)) {
                return res.status(403).json({
                    success: false,
                    message: '<PERSON>ạn không có quyền sử dụng tính năng AI tạo thực đơn'
                });
            }

            const {
                requirements,
                preferences = [],
                restrictions = [],
                meal_count = 3,
                target_nutrition = null
            } = req.body;

            // Validate input
            if (!requirements || requirements.trim().length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '<PERSON><PERSON> lòng nhập yêu cầu tạo thực đơn'
                });
            }

            console.log(`AI Menu generation requested by user ${user.id}: ${requirements}`);

            // Gọi service tạo thực đơn
            const result = await aiMenuService.generateSmartMenu({
                requirements: requirements.trim(),
                preferences,
                restrictions,
                meal_count,
                target_nutrition
            });

            if (result.success) {
                // Log thành công
                await commonService.saveLog(req, `AI Menu generated successfully: ${requirements}`, null);
                
                res.json({
                    success: true,
                    message: 'Tạo thực đơn AI thành công',
                    data: result.data,
                    metadata: result.metadata
                });
            } else {
                // Log lỗi
                await commonService.saveLog(req, `AI Menu generation failed: ${result.message}`, null);
                
                res.status(500).json({
                    success: false,
                    message: result.message || 'Không thể tạo thực đơn AI'
                });
            }

        } catch (error) {
            console.error('Error in generateMenu:', error);
            await commonService.saveLog(req, error.message, error.stack);
            
            res.status(500).json({
                success: false,
                message: 'Có lỗi xảy ra khi tạo thực đơn AI'
            });
        }
    },

    // Tạo thực đơn mẫu
    generateSampleMenu: async (req, res) => {
        try {
            const user = req.user;
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Vui lòng đăng nhập để sử dụng tính năng này'
                });
            }

            const { type = 'balanced' } = req.query;
            
            console.log(`Sample AI Menu generation requested by user ${user.id}: ${type}`);

            const result = await aiMenuService.generateSampleMenu(type);

            if (result.success) {
                res.json({
                    success: true,
                    message: 'Tạo thực đơn mẫu thành công',
                    data: result.data,
                    metadata: result.metadata
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message || 'Không thể tạo thực đơn mẫu'
                });
            }

        } catch (error) {
            console.error('Error in generateSampleMenu:', error);
            res.status(500).json({
                success: false,
                message: 'Có lỗi xảy ra khi tạo thực đơn mẫu'
            });
        }
    },

    // Đồng bộ dữ liệu food_info lên Pinecone
    syncFoods: async (req, res) => {
        try {
            // const user = req.user;
            // if (!user || !user.isAdmin) {
            //     return res.status(403).json({
            //         success: false,
            //         message: 'Chỉ admin mới có quyền đồng bộ dữ liệu'
            //     });
            // }

            console.log(`Food sync requested by admin`);

            const result = await aiMenuService.syncFoodData();

            if (result.success) {
                await commonService.saveLog(req, 'Food data synced to Pinecone successfully', null);
                
                res.json({
                    success: true,
                    message: 'Đồng bộ dữ liệu thực phẩm thành công',
                    data: result
                });
            } else {
                await commonService.saveLog(req, `Food sync failed: ${result.message}`, null);
                
                res.status(500).json({
                    success: false,
                    message: result.message || 'Không thể đồng bộ dữ liệu'
                });
            }

        } catch (error) {
            console.error('Error in syncFoods:', error);
            await commonService.saveLog(req, error.message, error.stack);
            
            res.status(500).json({
                success: false,
                message: 'Có lỗi xảy ra khi đồng bộ dữ liệu'
            });
        }
    },

    // Đồng bộ dữ liệu món ăn lên Pinecone
    syncDishes: async (req, res) => {
        try {
            // const user = req.user;
            // if (!user || !user.isAdmin) {
            //     return res.status(403).json({
            //         success: false,
            //         message: 'Chỉ admin mới có quyền đồng bộ dữ liệu'
            //     });
            // }

            console.log(`Dish sync requested by admin`);

            const dishService = require('../services/dishService');
            const result = await dishService.syncDishesToPinecone();

            if (result.success) {
                await commonService.saveLog(req, 'Dish data synced to Pinecone successfully', null);

                res.json({
                    success: true,
                    message: 'Đồng bộ dữ liệu món ăn thành công',
                    data: result
                });
            } else {
                await commonService.saveLog(req, `Dish sync failed: ${result.message}`, null);

                res.status(500).json({
                    success: false,
                    message: result.message || 'Không thể đồng bộ dữ liệu món ăn'
                });
            }

        } catch (error) {
            console.error('Error in syncDishes:', error);
            await commonService.saveLog(req, error.message, error.stack);

            res.status(500).json({
                success: false,
                message: 'Có lỗi xảy ra khi đồng bộ dữ liệu món ăn'
            });
        }
    },

    // Tìm kiếm thực phẩm bằng vector search
    searchFoods: async (req, res) => {
        try {
            const user = req.user;
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Vui lòng đăng nhập để sử dụng tính năng này'
                });
            }

            const { 
                query, 
                limit = 10,
                type = null,
                type_year = null 
            } = req.query;

            if (!query || query.trim().length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Vui lòng nhập từ khóa tìm kiếm'
                });
            }

            // Tạo filter cho Pinecone
            const filter = {};
            if (type) filter.type = type;
            if (type_year) filter.type_year = type_year;

            const result = await aiMenuService.searchRelevantFoods(
                query.trim(),
                [],
                [],
                parseInt(limit)
            );

            if (result.success) {
                res.json({
                    success: true,
                    message: 'Tìm kiếm thành công',
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message || 'Không thể tìm kiếm thực phẩm'
                });
            }

        } catch (error) {
            console.error('Error in searchFoods:', error);
            res.status(500).json({
                success: false,
                message: 'Có lỗi xảy ra khi tìm kiếm'
            });
        }
    },

    // Lấy thống kê hệ thống AI
    getStats: async (req, res) => {
        try {
            const user = req.user;
            if (!user || !user.isAdmin) {
                return res.status(403).json({
                    success: false,
                    message: 'Chỉ admin mới có quyền xem thống kê'
                });
            }

            const result = await aiMenuService.getSystemStats();

            if (result.success) {
                res.json({
                    success: true,
                    message: 'Lấy thống kê thành công',
                    data: result.data
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: result.message || 'Không thể lấy thống kê'
                });
            }

        } catch (error) {
            console.error('Error in getStats:', error);
            res.status(500).json({
                success: false,
                message: 'Có lỗi xảy ra khi lấy thống kê'
            });
        }
    },

    // Test kết nối AI services
    testConnection: async (req, res) => {
        try {
            const user = req.user;
            if (!user || !user.isAdmin) {
                return res.status(403).json({
                    success: false,
                    message: 'Chỉ admin mới có quyền test kết nối'
                });
            }

            const results = {
                aiMenuService: false,
                pinecone: false,
                gemini: false,
                pineconeDetails: null
            };

            // Test AI Menu Service
            try {
                const aiResult = await aiMenuService.initialize();
                results.aiMenuService = aiResult === true;
            } catch (error) {
                console.error('AI Menu Service test failed:', error);
            }

            // Test Pinecone with detailed info
            try {
                const pineconeResult = await pineconeService.initialize();
                results.pinecone = pineconeResult === true;

                if (pineconeResult) {
                    // Get index stats for debugging
                    const statsResult = await pineconeService.getIndexStats();
                    if (statsResult.success) {
                        results.pineconeDetails = {
                            indexName: process.env.PINECONE_INDEX_NAME,
                            totalVectors: statsResult.data.totalVectorCount || 0,
                            dimension: 1024,
                            metric: 'cosine'
                        };
                    }
                }
            } catch (error) {
                console.error('Pinecone test failed:', error);
                results.pineconeDetails = { error: error.message };
            }

            // Test Gemini
            try {
                const geminiResult = await geminiService.initialize();
                results.gemini = geminiResult === true;
            } catch (error) {
                console.error('Gemini test failed:', error);
            }

            const allConnected = results.aiMenuService && results.pinecone && results.gemini;

            res.json({
                success: allConnected,
                message: allConnected ? 'Tất cả services đã kết nối' : 'Một số services chưa kết nối được',
                data: results
            });

        } catch (error) {
            console.error('Error in testConnection:', error);
            res.status(500).json({
                success: false,
                message: 'Có lỗi xảy ra khi test kết nối: ' + error.message
            });
        }
    }
};

module.exports = aiMenuController;
