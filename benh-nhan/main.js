var listFollow = [];
var listLike = [];
var token = "eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************.-oziP7vyujmbMCX0rt4HknqV-HNkFhbAy-2x9mCzeXU";

function generateHeaders() {
    const myHeaders = new Headers();
    myHeaders.append("sessionToken", token);
    myHeaders.append("Content-Type", "application/json");

     // Thêm các headers thường có trong browser
    myHeaders.append("Accept", "application/json, text/plain, */*");
    myHeaders.append("Accept-Language", "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7");
    myHeaders.append("Origin", "https://fandomyeunuoc.kenh14.vn"); // Thay bằng domain thực tế
    myHeaders.append("Referer", "https://fandomyeunuoc.kenh14.vn/"); // Thay bằng URL thực tế
    myHeaders.append("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
    
    myHeaders.append("sec-ch-ua", "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
    myHeaders.append("sec-ch-ua-mobile", "?0");
    myHeaders.append("sec-ch-ua-platform", "\"Windows\"");
    myHeaders.append("Sec-Fetch-Dest", "empty");
    myHeaders.append("Sec-Fetch-Mode", "cors");
    myHeaders.append("Sec-Fetch-Site", "cross-site");
    myHeaders.append("sec-fetch-storage-access", "active");
    myHeaders.append("sec-gpc", "1");
    // Có thể server yêu cầu header đặc biệt
    myHeaders.append("X-Requested-With", "XMLHttpRequest");

    return myHeaders;
}

async function likePage(id, type){
    const myHeaders = generateHeaders(); 

    const requestOptions = {
        method: "POST",
        headers: myHeaders,
        redirect: "follow"
    };

    await fetch("https://idol14-api.net-solutions.vn/v1/fandom/"+ id +"/react?type=" + type, requestOptions)
    .then((response) => response.text())
    .then((result) => console.log(result))
    .catch((error) => console.error(error));
}

async function followPage(id){
    const myHeaders = generateHeaders();

    const requestOptions = {
    method: "POST",
    headers: myHeaders,
    redirect: "follow"
    };

   await fetch("https://idol14-api.net-solutions.vn/v1/fandom/"+ id +"/follow", requestOptions)
    .then((response) => response.text())
    .then((result) => console.log(result))
    .catch((error) => console.error(error));
}

async function getListFandom(){
    const myHeaders = generateHeaders();

    const requestOptions = {
    method: "GET",
    headers: myHeaders,
    redirect: "follow"
    };

    await fetch("https://idol14-api.net-solutions.vn/v1/home/<USER>", requestOptions)
    .then((response) => response.text())
    .then((result) => {
        console.log("result: ", result);
        const data = JSON.parse(result);
        console.log("data: ", data);
        if(data.status && data.data){
            for(let item of data.data){
                listFollow.push(item.fandomId);
                listLike.push(...item.stories.map(s=>s.id))
            }
        }
    })
    .catch((error) => console.error(error));
}

async function exchangeSpin(){
    while(true) {
        await delay(1000); // Đợi 1 giây trước khi chạy tiếp
        const myHeaders = generateHeaders();
        const raw = JSON.stringify({
            "spinCount": 1
        });
        const requestOptions = {
            method: "POST",
            headers: myHeaders,
            body: raw,
            redirect: "follow"
        };
        
        try {
            const response = await fetch("https://idol14-api.net-solutions.vn/v1/fan/exchange-spin", requestOptions);
            const resultText = await response.text();
            const result = JSON.parse(resultText);
            
            console.log("result exchange spin: ", result);
            
            if(result.status){
                console.log("Status = true, tiếp tục exchange...");
                // Tiếp tục vòng lặp
            } else {
                console.log("Status = false, dừng lại");
                return result;
            }
        } catch (error) {
            console.error("Error:", error);
            return false;
        }
    }
}

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

async function main(){
    await getListFandom();

    if(listFollow.length > 0){
        // Cho follow
        for(let item of listFollow){
            await followPage(item);
            await delay(1000); // Đợi 1 giây trước khi chạy tiếp
        }
    }

    if(listLike.length > 0){
        // Cho like
        for(let item of listLike){
            await likePage(item, 0);
            await delay(1000);
            await likePage(item, 1);
            await delay(1000);
        }
    }
}

async function exchangePrize(){
        while(true) {
            await delay(1000); // Đợi 1 giây trước khi chạy tiếp
            const myHeaders = generateHeaders();

            const requestOptions = {
                method: "POST",
                headers: myHeaders,
                redirect: "follow"
            };

            try {
                const response = await fetch("https://idol14-api.net-solutions.vn/v1/store/item/exchange", requestOptions);
                const resultText = await response.text();
                const result = JSON.parse(resultText);
                
                console.log("result exchange spin: ", result);
                
                if(result.message == "Đổi vật phẩm thành công"){
                    console.log("Status = true, tiếp tục exchangePrize...");
                    // Tiếp tục vòng lặp
                } else {
                    console.log("Status = false, dừng lại exchangePrize");
                    return false;
                }
            } catch (error) {
                console.error("Error:", error);
                return false;
            }
        }
}



// console.log("listFollow: ", listFollow, listLike)



const result_exchange_spin = await exchangePrize();
console.log('Kết quả cuối cùng:', result_exchange_spin);

// main();