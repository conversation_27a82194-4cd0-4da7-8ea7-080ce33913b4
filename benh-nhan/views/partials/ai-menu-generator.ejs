<!-- AI Menu Generator Modal -->
<div class="modal fade" id="aiMenuModal" tabindex="-1" role="dialog" aria-labelledby="aiMenuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="aiMenuModalLabel">
                    <i class="fas fa-robot"></i> AI Menu Generator
                </h5>
                <button type="button" class="close text-white" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- AI Menu Generator Form -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-edit"></i> Tạo thực đơn tùy chỉnh</h6>
                            </div>
                            <div class="card-body">
                                <!-- Requirements -->
                                <div class="form-group">
                                    <label for="aiRequirements">Yêu cầu tạo thực đơn <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="aiRequirements" rows="3" 
                                        placeholder="Ví dụ: Tạo thực đơn cho bệnh nhân tiểu đường, 1800 kcal/ngày, ít đường, nhiều chất xơ..."></textarea>
                                    <small class="form-text text-muted">Mô tả chi tiết yêu cầu về thực đơn, bệnh lý, mục tiêu dinh dưỡng</small>
                                </div>

                                <!-- Preferences -->
                                <div class="form-group">
                                    <label>Sở thích / Ưu tiên</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="preferenceInput" 
                                            placeholder="Ví dụ: nhiều rau xanh, ít dầu mỡ...">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-primary" type="button" id="addPreference">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="preferenceTags" class="mt-2"></div>
                                </div>

                                <!-- Restrictions -->
                                <div class="form-group">
                                    <label>Hạn chế / Kiêng kỵ</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="restrictionInput" 
                                            placeholder="Ví dụ: không sữa, không hải sản...">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-danger" type="button" id="addRestriction">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="restrictionTags" class="mt-2"></div>
                                </div>

                                <!-- Meal Count -->
                                <div class="form-group">
                                    <label for="mealCount">Số bữa ăn</label>
                                    <select class="form-control" id="mealCount">
                                        <option value="3">3 bữa (Sáng, Trưa, Tối)</option>
                                        <option value="4">4 bữa (Sáng, Trưa, Phụ, Tối)</option>
                                        <option value="5">5 bữa (Sáng, Phụ sáng, Trưa, Phụ chiều, Tối)</option>
                                    </select>
                                </div>

                                <!-- Target Nutrition -->
                                <div class="form-group">
                                    <label>Mục tiêu dinh dưỡng (tùy chọn)</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="number" class="form-control mb-2" id="targetEnergy" 
                                                placeholder="Năng lượng (kcal)">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="number" class="form-control mb-2" id="targetProtein" 
                                                placeholder="Protein (g)">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="number" class="form-control mb-2" id="targetCarbohydrate" 
                                                placeholder="Carbohydrate (g)">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="number" class="form-control mb-2" id="targetLipid" 
                                                placeholder="Lipid (g)">
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="text-center">
                                    <button type="button" class="btn btn-primary" id="generateAIMenu">
                                        <i class="fas fa-magic"></i> Tạo thực đơn AI
                                    </button>
                                    <button type="button" class="btn btn-secondary ml-2" id="clearAIForm">
                                        <i class="fas fa-eraser"></i> Xóa form
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <!-- Sample Menus -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-star"></i> Thực đơn mẫu</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">Tạo nhanh thực đơn mẫu cho các trường hợp phổ biến</p>
                                <div class="row" id="sampleMenuTypes">
                                    <!-- Sample menu types will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Admin Tools (only for admin users) -->
                        <% if (user && user.isAdmin) { %>
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-cog"></i> Công cụ quản trị</h6>
                            </div>
                            <div class="card-body">
                                <div class="btn-group-vertical w-100">
                                    <button type="button" class="btn btn-info mb-2" id="syncFoodsBtn">
                                        <i class="fas fa-sync"></i> Đồng bộ dữ liệu thực phẩm
                                    </button>
                                    <button type="button" class="btn btn-success mb-2" id="syncDishesBtn">
                                        <i class="fas fa-utensils"></i> Đồng bộ dữ liệu món ăn
                                    </button>
                                    <button type="button" class="btn btn-warning mb-2" id="testConnectionBtn">
                                        <i class="fas fa-plug"></i> Test kết nối AI
                                    </button>
                                </div>
                                <small class="text-muted">
                                    Đồng bộ thực phẩm: Upload tất cả thực phẩm lên Pinecone vector database<br>
                                    Đồng bộ món ăn: Upload tất cả món ăn lên Pinecone vector database<br>
                                    Test kết nối: Kiểm tra kết nối với Gemini AI và Pinecone
                                </small>
                            </div>
                        </div>
                        <% } %>
                    </div>
                </div>

                <!-- AI Menu Result -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div id="aiMenuResult" style="display: none;"></div>
                    </div>
                </div>

                <!-- Nutrition Summary -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div id="nutritionSummary" style="display: none;"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="useAIMenu" style="display: none;">
                    <i class="fas fa-check"></i> Sử dụng thực đơn này
                </button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<!-- AI Menu Generator CSS -->
<style>
.tag {
    display: inline-block;
    position: relative;
}

.tag .remove-tag {
    font-size: 14px;
    line-height: 1;
    cursor: pointer;
}

.tag .remove-tag:hover {
    opacity: 0.7;
}

.meal-section {
    border-left: 3px solid #007bff;
    padding-left: 15px;
}

.card-header h6 {
    margin-bottom: 0;
}

#aiMenuResult .table th {
    background-color: #f8f9fa;
    font-size: 0.875rem;
}

#aiMenuResult .table td {
    font-size: 0.875rem;
}

.nutrition-card {
    text-align: center;
    padding: 1rem;
}

.nutrition-card h4 {
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.nutrition-card small {
    color: #6c757d;
    font-size: 0.75rem;
}

.modal-xl {
    max-width: 1200px;
}

@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* Loading animation */
.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Custom scrollbar for modal */
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>

<!-- Include AI Menu Generator JavaScript -->
<script src="/js/aiMenuGenerator.js?v=<%= assetVersion %>"></script>
