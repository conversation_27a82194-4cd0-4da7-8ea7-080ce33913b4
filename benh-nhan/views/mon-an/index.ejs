<!DOCTYPE html>
<html lang="en">

<head>
    <%- include('../layout/head') %>
    <title><%= isEdit ? 'Chỉnh sửa món ăn' : 'Thêm món ăn mới' %> - Patients</title>
    <link href="/css/dish-admin.css" rel="stylesheet">
    <style>
        .weight-edit-container {
            position: relative;
        }
        
        .weight-input {
            width: 80px;
            display: inline-block;
        }
        
        .weight-display {
            display: inline-block;
            padding: 6px 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            min-width: 60px;
            text-align: center;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .me-1 {
            margin-right: 0.25rem;
        }
        
        .nutrition-value {
            text-align: center;
            font-weight: 500;
        }
        
        /* CSS cho dropdown gợi ý món ăn */
        .dish-suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }
        
        .suggestion-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s ease;
        }
        
        .suggestion-item:hover {
            background-color: #f8f9fa;
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
        
        .suggestion-name {
            font-weight: 500;
            color: #495057;
            margin-bottom: 3px;
        }
        
        .suggestion-description {
            font-size: 0.85em;
            color: #6c757d;
            line-height: 1.3;
        }
        
        /* Đảm bảo input có position relative để dropdown hiển thị đúng */
        .form-group {
            position: relative;
        }
    </style>
</head>

<body>
    <!-- Page Wrapper -->
    <div id="wrapper">
        <%- include('../layout/sidebar') %>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <%- include('../layout/header') %>
            
            <!-- Begin Page Content -->
            <% if(errors.length > 0){ %>
                <div class="container">
                    <div class="box mt-3">
                        <% for(let item of errors){ %>
                            <div class="alert alert-danger"><%- JSON.stringify(item) %></div>
                        <% } %>
                    </div>
                </div>
            <% } else { %>
                <div class="container-fluid">
                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <%= isEdit ? 'Chỉnh sửa món ăn' : 'Thêm món ăn mới' %>
                        </h1>
                        <a href="/admin/mon-an" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Quay lại danh sách
                        </a>
                    </div>

                    <!-- Form Card -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Thông tin món ăn</h6>
                        </div>
                        <div class="card-body">
                            <form id="dishForm">
                                <input type="hidden" id="dishId" name="id" value="<%= dishData ? dishData.id : '' %>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="dishName">Tên món ăn <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="dishName" name="name" 
                                                   value="<%= dishData ? dishData.name : '' %>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="dishCategory">Loại món ăn</label>
                                            <select class="form-control" id="dishCategory" name="category">
                                                <option value="">Chọn loại món ăn</option>
                                                <option value="chính" <%= dishData && dishData.category === 'chính' ? 'selected' : '' %>>Món chính</option>
                                                <option value="phụ" <%= dishData && dishData.category === 'phụ' ? 'selected' : '' %>>Món phụ</option>
                                                <option value="canh" <%= dishData && dishData.category === 'canh' ? 'selected' : '' %>>Canh</option>
                                                <option value="tráng miệng" <%= dishData && dishData.category === 'tráng miệng' ? 'selected' : '' %>>Tráng miệng</option>
                                                <option value="nước uống" <%= dishData && dishData.category === 'nước uống' ? 'selected' : '' %>>Nước uống</option>
                                                <option value="khác" <%= dishData && dishData.category === 'khác' ? 'selected' : '' %>>Khác</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="dishDescription">Mô tả món ăn</label>
                                    <textarea class="form-control" id="dishDescription" name="description" rows="3"
                                              placeholder="Mô tả cách chế biến, nguyên liệu chính..."><%= dishData ? dishData.description : '' %></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dishShare" name="share" 
                                               value="1" <%= dishData && dishData.share == 1 ? 'checked' : '' %>>
                                        <label class="form-check-label" for="dishShare">
                                            <i class="fas fa-share-alt text-primary"></i> Chia sẻ món ăn này
                                        </label>
                                        <small class="form-text text-muted">Khi được chia sẻ, món ăn này sẽ hiển thị cho tất cả người dùng</small>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Food Selection Card -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Thêm thực phẩm vào món ăn</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3 align-items-end">
                                <div class="col-sm-12 col-md-4">
                                    <label for="foodType">Loại thực phẩm</label>
                                    <select class="form-control" id="foodType">
                                        <option value="">Tất cả</option>
                                        <option value="raw">Sống</option>
                                        <option value="cooked">Chín ĐP</option>
                                        <option value="cooked_vdd">Chín VDD</option>
                                        <option value="milk">Sữa</option>
                                        <option value="ddd">Dịch DD</option>
                                    </select>
                                </div>
                                <div class="col-sm-12 col-md-4">
                                    <label for="foodYear">Năm dữ liệu</label>
                                    <select class="form-control" id="foodYear">
                                        <option value="">Tất cả</option>
                                        <option value="2000">2000</option>
                                        <option value="2017">2017</option>
                                        <option value="2025">2025</option>
                                    </select>
                                </div>
                                
                                <div class="col-sm-12 col-md-4">
                                    <label for="foodWeight">Khối lượng (g)</label>
                                    <input type="number" class="form-control" id="foodWeight" 
                                           placeholder="100" min="0.1" step="0.1">
                                </div>
                                <div class="col-sm-12 col-md-12">
                                    <label for="foodSelect">Chọn thực phẩm</label>
                                    <div id="foodSelect" style="max-width:100%; display:block;"></div>
                                </div>
                                <div class="col-md-12">
                                    <button type="button" class="btn btn-success btn-add-food" onclick="addFoodToDish()">
                                        <i class="fas fa-plus"></i> Thêm thực phẩm
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Food List Card -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Danh sách thực phẩm trong món ăn</h6>
                            <div class="total-summary">
                                <small>Tổng: <span id="totalWeight">0</span>g | 
                                       <span id="totalEnergy">0</span> kcal</small>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered dish-foods-table" id="dishFoodsTable">
                                    <thead>
                                        <tr>
                                            <th width="5%">#</th>
                                            <th width="25%">Tên thực phẩm</th>
                                            <th width="10%">Mã</th>
                                            <th width="10%">Loại</th>
                                            <th width="10%">Khối lượng (g)</th>
                                            <th width="10%">Năng lượng (kcal)</th>
                                            <th width="10%">Protein (g)</th>
                                            <th width="10%">Fat (g)</th>
                                            <th width="10%">Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dishFoodsBody">
                                        <!-- Danh sách thực phẩm sẽ được thêm vào đây -->
                                    </tbody>
                                </table>
                                <div id="emptyMessage" class="empty-state">
                                    <i class="fas fa-utensils fa-2x"></i>
                                    <p>Chưa có thực phẩm nào trong món ăn</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="card shadow mb-4">
                        <div class="card-body text-center">
                            <button type="button" class="btn btn-primary btn-lg btn-save-dish" onclick="saveDish()">
                                <i class="fas fa-save"></i> 
                                <%= isEdit ? 'Cập nhật món ăn' : 'Lưu món ăn' %>
                            </button>
                            <a href="/admin/mon-an" class="btn btn-secondary btn-lg ml-3">
                                <i class="fas fa-times"></i> Hủy
                            </a>
                        </div>
                    </div>
                </div>
            <% } %>
            
            <%- include('../layout/footer') %>
        </div>
    </div>
    
    <!-- Dữ liệu từ server -->
    <script>
        <%
            const isEditJS = isEdit ? true : false;
            const existingFoodsJS = dishFoods || [];
        %>
        window.dishData = {
            isEdit: <%= isEditJS %>,
            existingFoods: <%- JSON.stringify(existingFoodsJS) %>
        };
    </script>
    
    <script src="/js/dish-admin.js?v=<%= assetVersion %>"></script>
</body>
</html>