<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Menu Generator Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-card {
            transition: transform 0.2s;
        }
        .test-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .result-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        .nutrition-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
        }
        .meal-card {
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        .food-item {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 text-primary">
                        <i class="fas fa-robot"></i> AI Menu Generator Test Suite
                    </h1>
                    <div>
                        <button class="btn btn-info" onclick="testAllConnections()">
                            <i class="fas fa-plug"></i> Test All Connections
                        </button>
                        <button class="btn btn-warning" onclick="syncAllFoods()">
                            <i class="fas fa-sync"></i> Sync Foods
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Connection Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-signal"></i> Connection Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="connectionStatus">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="spinner-border text-secondary" role="status" id="aiServiceSpinner">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div id="aiServiceStatus" class="mt-2">
                                        <span class="badge bg-secondary">Checking...</span>
                                        <div>AI Menu Service</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="spinner-border text-secondary" role="status" id="pineconeSpinner">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div id="pineconeStatus" class="mt-2">
                                        <span class="badge bg-secondary">Checking...</span>
                                        <div>Pinecone Vector DB</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="spinner-border text-secondary" role="status" id="geminiSpinner">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div id="geminiStatus" class="mt-2">
                                        <span class="badge bg-secondary">Checking...</span>
                                        <div>Google Gemini AI</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Cases -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card test-card h-100">
                    <div class="card-header bg-success text-white position-relative">
                        <h6 class="mb-0"><i class="fas fa-heart"></i> Thực đơn cân bằng</h6>
                        <span class="badge bg-light text-dark status-badge" id="balanced-status">Ready</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tạo thực đơn cân bằng dinh dưỡng cho người khỏe mạnh, 2000 kcal/ngày.</p>
                        <div class="mb-3">
                            <small class="text-muted">
                                <strong>Yêu cầu:</strong> Cân bằng dinh dưỡng, đa dạng thực phẩm<br>
                                <strong>Mục tiêu:</strong> 2000 kcal, 80g protein
                            </small>
                        </div>
                        <button class="btn btn-success w-100" onclick="testBalancedMenu()">
                            <i class="fas fa-play"></i> Test Balanced Menu
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card test-card h-100">
                    <div class="card-header bg-warning text-dark position-relative">
                        <h6 class="mb-0"><i class="fas fa-heartbeat"></i> Thực đơn tiểu đường</h6>
                        <span class="badge bg-light text-dark status-badge" id="diabetes-status">Ready</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tạo thực đơn cho bệnh nhân tiểu đường, ít đường, nhiều chất xơ.</p>
                        <div class="mb-3">
                            <small class="text-muted">
                                <strong>Hạn chế:</strong> Đường, bánh ngọt<br>
                                <strong>Ưu tiên:</strong> Chất xơ, ít carbohydrate<br>
                                <strong>Mục tiêu:</strong> 1800 kcal
                            </small>
                        </div>
                        <button class="btn btn-warning w-100" onclick="testDiabetesMenu()">
                            <i class="fas fa-play"></i> Test Diabetes Menu
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card test-card h-100">
                    <div class="card-header bg-info text-white position-relative">
                        <h6 class="mb-0"><i class="fas fa-weight"></i> Thực đơn giảm cân</h6>
                        <span class="badge bg-light text-dark status-badge" id="weightloss-status">Ready</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Tạo thực đơn giảm cân, ít calo, nhiều protein và rau xanh.</p>
                        <div class="mb-3">
                            <small class="text-muted">
                                <strong>Hạn chế:</strong> Đồ chiên, đồ ngọt<br>
                                <strong>Ưu tiên:</strong> Protein, rau xanh<br>
                                <strong>Mục tiêu:</strong> 1500 kcal
                            </small>
                        </div>
                        <button class="btn btn-info w-100" onclick="testWeightLossMenu()">
                            <i class="fas fa-play"></i> Test Weight Loss Menu
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom Test -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs"></i> Custom Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="customRequirements">Yêu cầu tùy chỉnh</label>
                                    <textarea class="form-control" id="customRequirements" rows="3" 
                                        placeholder="Nhập yêu cầu tạo thực đơn..."></textarea>
                                </div>
                                <div class="form-group mb-3">
                                    <label for="customPreferences">Sở thích (phân cách bằng dấu phẩy)</label>
                                    <input type="text" class="form-control" id="customPreferences" 
                                        placeholder="nhiều rau, ít dầu mỡ, ...">
                                </div>
                                <div class="form-group mb-3">
                                    <label for="customRestrictions">Hạn chế (phân cách bằng dấu phẩy)</label>
                                    <input type="text" class="form-control" id="customRestrictions" 
                                        placeholder="không sữa, không hải sản, ...">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label>Mục tiêu dinh dưỡng</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="number" class="form-control mb-2" id="customEnergy" 
                                                placeholder="Năng lượng (kcal)">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control mb-2" id="customProtein" 
                                                placeholder="Protein (g)">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control mb-2" id="customCarb" 
                                                placeholder="Carbohydrate (g)">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control mb-2" id="customLipid" 
                                                placeholder="Lipid (g)">
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary w-100" onclick="testCustomMenu()">
                                    <i class="fas fa-rocket"></i> Test Custom Menu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <div class="text-center text-muted">
                                <i class="fas fa-info-circle fa-2x mb-3"></i>
                                <p>Chạy test để xem kết quả tại đây</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Test functions will be loaded here
        let testResults = [];

        // Initialize page
        $(document).ready(function() {
            testAllConnections();
        });

        // Test connection to all services
        async function testAllConnections() {
            try {
                const response = await fetch('/api/ai-menu/test');
                const result = await response.json();
                
                if (result.success) {
                    updateConnectionStatus('aiServiceStatus', 'aiServiceSpinner', result.data.aiMenuService);
                    updateConnectionStatus('pineconeStatus', 'pineconeSpinner', result.data.pinecone);
                    updateConnectionStatus('geminiStatus', 'geminiSpinner', result.data.gemini);
                } else {
                    updateConnectionStatus('aiServiceStatus', 'aiServiceSpinner', false);
                    updateConnectionStatus('pineconeStatus', 'pineconeSpinner', false);
                    updateConnectionStatus('geminiStatus', 'geminiSpinner', false);
                }
            } catch (error) {
                console.error('Connection test failed:', error);
                updateConnectionStatus('aiServiceStatus', 'aiServiceSpinner', false);
                updateConnectionStatus('pineconeStatus', 'pineconeSpinner', false);
                updateConnectionStatus('geminiStatus', 'geminiSpinner', false);
            }
        }

        function updateConnectionStatus(statusId, spinnerId, isConnected) {
            const statusEl = document.getElementById(statusId);
            const spinnerEl = document.getElementById(spinnerId);
            
            spinnerEl.style.display = 'none';
            
            if (isConnected) {
                statusEl.innerHTML = '<span class="badge bg-success">Connected</span><div>' + statusEl.textContent.split('\n')[1] + '</div>';
            } else {
                statusEl.innerHTML = '<span class="badge bg-danger">Failed</span><div>' + statusEl.textContent.split('\n')[1] + '</div>';
            }
        }

        // Test balanced menu
        async function testBalancedMenu() {
            await runTest('balanced', 'balanced-status', {
                requirements: 'Tạo thực đơn cân bằng dinh dưỡng cho người trưởng thành khỏe mạnh, 3 bữa ăn trong ngày',
                preferences: ['đa dạng thực phẩm', 'cân bằng dinh dưỡng'],
                restrictions: [],
                target_nutrition: {
                    energy: 2000,
                    protein: 80,
                    carbohydrate: 250,
                    lipid: 70
                }
            });
        }

        // Test diabetes menu
        async function testDiabetesMenu() {
            await runTest('diabetes', 'diabetes-status', {
                requirements: 'Tạo thực đơn cho bệnh nhân tiểu đường, ít đường, nhiều chất xơ, kiểm soát carbohydrate',
                preferences: ['ít đường', 'nhiều chất xơ', 'ít carbohydrate'],
                restrictions: ['đường', 'kẹo', 'bánh ngọt'],
                target_nutrition: {
                    energy: 1800,
                    protein: 90,
                    carbohydrate: 180,
                    lipid: 60
                }
            });
        }

        // Test weight loss menu
        async function testWeightLossMenu() {
            await runTest('weightloss', 'weightloss-status', {
                requirements: 'Tạo thực đơn giảm cân, ít calo, nhiều protein, nhiều rau xanh',
                preferences: ['ít calo', 'nhiều protein', 'rau xanh'],
                restrictions: ['đồ chiên', 'đồ ngọt'],
                target_nutrition: {
                    energy: 1500,
                    protein: 100,
                    carbohydrate: 150,
                    lipid: 50
                }
            });
        }

        // Test custom menu
        async function testCustomMenu() {
            const requirements = document.getElementById('customRequirements').value.trim();
            if (!requirements) {
                alert('Vui lòng nhập yêu cầu tạo thực đơn');
                return;
            }

            const preferences = document.getElementById('customPreferences').value
                .split(',').map(s => s.trim()).filter(s => s);
            const restrictions = document.getElementById('customRestrictions').value
                .split(',').map(s => s.trim()).filter(s => s);
            
            const target_nutrition = {};
            const energy = document.getElementById('customEnergy').value;
            const protein = document.getElementById('customProtein').value;
            const carb = document.getElementById('customCarb').value;
            const lipid = document.getElementById('customLipid').value;
            
            if (energy) target_nutrition.energy = parseInt(energy);
            if (protein) target_nutrition.protein = parseInt(protein);
            if (carb) target_nutrition.carbohydrate = parseInt(carb);
            if (lipid) target_nutrition.lipid = parseInt(lipid);

            await runTest('custom', null, {
                requirements,
                preferences,
                restrictions,
                target_nutrition: Object.keys(target_nutrition).length > 0 ? target_nutrition : null
            });
        }

        // Run test function
        async function runTest(testType, statusId, requestData) {
            const startTime = Date.now();
            
            if (statusId) {
                document.getElementById(statusId).textContent = 'Running...';
                document.getElementById(statusId).className = 'badge bg-warning text-dark status-badge';
            }

            try {
                const response = await fetch('/api/ai-menu/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                const duration = Date.now() - startTime;

                if (result.success) {
                    if (statusId) {
                        document.getElementById(statusId).textContent = 'Success';
                        document.getElementById(statusId).className = 'badge bg-success status-badge';
                    }
                    
                    displayTestResult(testType, result.data, duration, true);
                } else {
                    if (statusId) {
                        document.getElementById(statusId).textContent = 'Failed';
                        document.getElementById(statusId).className = 'badge bg-danger status-badge';
                    }
                    
                    displayTestResult(testType, { error: result.message }, duration, false);
                }

            } catch (error) {
                const duration = Date.now() - startTime;
                
                if (statusId) {
                    document.getElementById(statusId).textContent = 'Error';
                    document.getElementById(statusId).className = 'badge bg-danger status-badge';
                }
                
                displayTestResult(testType, { error: error.message }, duration, false);
            }
        }

        // Display test result
        function displayTestResult(testType, data, duration, success) {
            const resultsContainer = document.getElementById('testResults');
            
            let html = `
                <div class="test-result mb-4 p-3 border rounded ${success ? 'border-success' : 'border-danger'}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <span class="badge ${success ? 'bg-success' : 'bg-danger'} me-2">
                                ${success ? 'SUCCESS' : 'FAILED'}
                            </span>
                            Test: ${testType.toUpperCase()}
                        </h6>
                        <small class="text-muted">Duration: ${duration}ms</small>
                    </div>
            `;

            if (success && data.menu) {
                html += `
                    <div class="nutrition-summary mb-3">
                        <div class="row text-center">
                            <div class="col-3">
                                <h4>${data.nutrition_summary?.total_energy || 0}</h4>
                                <small>kcal</small>
                            </div>
                            <div class="col-3">
                                <h4>${data.nutrition_summary?.total_protein || 0}</h4>
                                <small>Protein (g)</small>
                            </div>
                            <div class="col-3">
                                <h4>${data.nutrition_summary?.total_carbohydrate || 0}</h4>
                                <small>Carb (g)</small>
                            </div>
                            <div class="col-3">
                                <h4>${data.nutrition_summary?.total_lipid || 0}</h4>
                                <small>Lipid (g)</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="menu-details">
                        <h6><i class="fas fa-utensils"></i> ${data.menu.name}</h6>
                `;

                data.menu.detail.forEach(meal => {
                    html += `
                        <div class="meal-card card mb-2">
                            <div class="card-body">
                                <h6 class="card-title text-primary">${meal.name}</h6>
                    `;
                    
                    meal.listFood.forEach(food => {
                        html += `
                            <div class="food-item">
                                <strong>${food.name}</strong> - ${food.weight}g
                                <small class="text-muted ms-2">
                                    ${food.energy}kcal, ${food.protein}g protein
                                </small>
                            </div>
                        `;
                    });
                    
                    html += `
                            </div>
                        </div>
                    `;
                });

                html += `</div>`;
            } else {
                html += `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${data.error || 'Unknown error occurred'}
                    </div>
                `;
            }

            html += `</div>`;

            if (resultsContainer.innerHTML.includes('Chạy test để xem kết quả')) {
                resultsContainer.innerHTML = html;
            } else {
                resultsContainer.innerHTML = html + resultsContainer.innerHTML;
            }
        }

        // Sync all foods
        async function syncAllFoods() {
            if (!confirm('Bạn có chắc muốn đồng bộ tất cả dữ liệu thực phẩm? Quá trình này có thể mất vài phút.')) {
                return;
            }

            try {
                const response = await fetch('/api/ai-menu/sync-foods', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('Đồng bộ dữ liệu thành công!');
                } else {
                    alert('Lỗi đồng bộ: ' + result.message);
                }
            } catch (error) {
                alert('Có lỗi xảy ra: ' + error.message);
            }
        }
    </script>
</body>
</html>
