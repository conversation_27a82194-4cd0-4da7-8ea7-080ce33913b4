<!DOCTYPE html>
<html lang="vi">
<head>
    <%- include('../layout/head') %>
    <title>Demo Responsive Table</title>
    <style>
        .demo-instructions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .demo-instructions h4 {
            color: white;
            margin-bottom: 15px;
        }
        .demo-instructions .btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
        }
        .demo-instructions .btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-urgent { background: #f8d7da; color: #721c24; }
        .status-inactive { background: #e2e3e5; color: #383d41; }
    </style>
</head>
<body id="page-top">
    <div id="wrapper">
        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-mobile-alt"></i> Demo Responsive Table
                        </h1>
                        <a href="/" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                    
                    <!-- Instructions -->
                    <div class="demo-instructions">
                        <h4><i class="fas fa-info-circle"></i> Hướng dẫn test Responsive Table</h4>
                        <div class="row">
                            <div class="col-md-8">
                                <p><strong>Desktop:</strong> Bảng hiển thị dạng thông thường với các cột và hàng</p>
                                <p><strong>Mobile:</strong> Mỗi hàng chuyển thành card với thông tin được sắp xếp dọc</p>
                                <p><strong>Cách test:</strong></p>
                                <ol>
                                    <li>Mở Developer Tools (F12)</li>
                                    <li>Toggle device toolbar (Ctrl+Shift+M)</li>
                                    <li>Chọn mobile device hoặc set width < 768px</li>
                                    <li>Xem bảng chuyển thành dạng card</li>
                                </ol>
                            </div>
                            <div class="col-md-4 text-center">
                                <button class="btn btn-outline-light" onclick="toggleMobileView()">
                                    <i class="fas fa-mobile-alt"></i> Toggle Mobile View
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- DataTables Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-table"></i> Danh sách bệnh nhân (Demo)
                            </h6>
                            <button class="btn btn-success btn-circle" title="Thêm mới">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="demoTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Thao tác</th>
                                            <th>Họ tên</th>
                                            <th>Số điện thoại</th>
                                            <th>Số phòng</th>
                                            <th>Chẩn đoán</th>
                                            <th>Ngày khám</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Features -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-success">
                                        <i class="fas fa-check-circle"></i> Features
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> Tự động responsive</li>
                                        <li><i class="fas fa-check text-success"></i> Card layout trên mobile</li>
                                        <li><i class="fas fa-check text-success"></i> DataTables integration</li>
                                        <li><i class="fas fa-check text-success"></i> Touch-friendly buttons</li>
                                        <li><i class="fas fa-check text-success"></i> Auto-generated labels</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-info">
                                        <i class="fas fa-cog"></i> Technical Details
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><strong>Breakpoint:</strong> 768px</li>
                                        <li><strong>CSS:</strong> table-config.css</li>
                                        <li><strong>JS:</strong> responsive-table.js</li>
                                        <li><strong>Framework:</strong> Bootstrap 5</li>
                                        <li><strong>Library:</strong> DataTables</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <%- include('../layout/footer') %>

    <script>
        let dataTable;
        
        $(document).ready(function() {
            // Initialize DataTable
            dataTable = $('#demoTable').DataTable({
                dom: '<"top d-flex flex-wrap gap-2 justify-content-between align-items-center mb-2"pf>rt<"bottom d-flex flex-wrap gap-2 justify-content-between align-items-center mt-2"il><"clear">',
                serverSide: true,
                processing: true,
                responsive: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50],
                paging: true,
                scrollX: true,
                ajax: {
                    url: '/demo/responsive-table/data',
                    method: 'POST',
                    dataType: "json",
                    beforeSend: function() {
                        // Optional loading indicator
                    },
                    complete: function() {
                        // Optional complete handler
                    },
                    dataSrc: function(response){
                        return response.data || [];
                    },
                },
                columns: [
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        render: function (data, type, row) {
                            return `
                                <div class="d-flex gap-2">
                                    <a class="btn btn-info btn-sm btn-circle" href="#" title="Sửa">
                                        <i class="fas fa-pen-square"></i>
                                    </a>
                                    <button class="btn btn-danger btn-sm btn-circle" onclick="deleteDemo(${row.id}, '${row.fullname}')" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            `;
                        }
                    },
                    {
                        data: 'fullname',
                        orderable: true,
                        searchable: true,
                        render: function(data, type, row) {
                            let classAttr = row.status === 'urgent' ? 'class="text-danger"' : '';
                            return `<a ${classAttr} href="#">${data}</a>`;
                        }
                    },
                    {
                        data: 'phone',
                        orderable: true,
                        searchable: true,
                        render: function(data, type, row) {
                            return `<a href="tel:${data}">${data}</a>`;
                        }
                    },
                    {
                        data: 'room',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'diagnosis',
                        orderable: true,
                        searchable: true,
                        className: 'min-width-150'
                    },
                    {
                        data: 'date',
                        orderable: true,
                        searchable: false,
                        render: function(data, type, row) {
                            return moment(data).format('DD/MM/YYYY');
                        }
                    },
                    {
                        data: 'status',
                        orderable: true,
                        searchable: true,
                        render: function(data, type, row) {
                            const statusMap = {
                                'active': '<span class="status-badge status-active">Đang điều trị</span>',
                                'urgent': '<span class="status-badge status-urgent">Khẩn cấp</span>',
                                'inactive': '<span class="status-badge status-inactive">Đã xuất viện</span>'
                            };
                            return statusMap[data] || data;
                        }
                    }
                ],
                order: [[1, 'asc']],
                searching: true,
                ordering: true,
                language: {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sSearch": "Tìm:",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                }
            });
        });

        function deleteDemo(id, name) {
            Swal.fire({
                title: 'Xác nhận xóa',
                text: `Bạn có chắc chắn muốn xóa bệnh nhân "${name}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire('Đã xóa!', 'Bệnh nhân đã được xóa (demo).', 'success');
                }
            });
        }

        function toggleMobileView() {
            const wrapper = document.getElementById('wrapper');
            if (wrapper.style.maxWidth === '767px') {
                wrapper.style.maxWidth = '';
                wrapper.style.margin = '';
            } else {
                wrapper.style.maxWidth = '767px';
                wrapper.style.margin = '0 auto';
            }
        }
    </script>
</body>
</html>
