# Survey System Layout Fixes

## Vấn đề đã khắc phụ<PERSON> file EJS của hệ thống khảo sát ban đầu sử dụng layout không đúng với cấu trúc hiện tại của dự án. <PERSON><PERSON> thực hiện các sửa đổi sau:

## Thay đổi Layout

### 1. Header Structure
**Trước:**
```html
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>...</title>
    <!-- Custom fonts and styles -->
</head>
<body id="page-top">
```

**Sau:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <%- include('../layout/head') %>
    <title>...</title>
    <!-- Custom styles for survey system -->
    <link href="/css/survey-system.css" rel="stylesheet">
</head>
<body>
```

### 2. Topbar → Header
**Trước:**
```html
<!-- Topbar -->
<%- include('../layout/topbar') %>
```

**Sau:**
```html
<!-- Header -->
<%- include('../layout/header') %>
```

### 3. Loại bỏ Logout Modal
**Trước:**
```html
<!-- Logout Modal-->
<%- include('../layout/logout-modal') %>
```

**Sau:**
```html
<!-- Đã loại bỏ vì file không tồn tại -->
```

## Files đã được sửa

### 1. views/projects/index.ejs ✅
- Sử dụng `<%- include('../layout/head') %>`
- Thay `topbar` → `header`
- Loại bỏ `logout-modal`
- Giữ nguyên DataTable functionality

### 2. views/projects/create.ejs ✅
- Sử dụng layout chuẩn
- Thay `topbar` → `header`
- Loại bỏ `logout-modal`
- Giữ nguyên Flatpickr integration

### 3. views/projects/edit.ejs ✅
- Sử dụng layout chuẩn
- Thay `topbar` → `header`
- Loại bỏ `logout-modal`
- Giữ nguyên form functionality

### 4. views/survey-configs/index.ejs ✅
- Sử dụng layout chuẩn
- Thay `topbar` → `header`
- Loại bỏ `logout-modal`
- Giữ nguyên DataTable functionality

### 5. views/survey-configs/fields-config.ejs ✅
- Sử dụng layout chuẩn
- Thay `topbar` → `header`
- Loại bỏ `logout-modal`
- Giữ nguyên jQuery UI sortable

### 6. views/survey-configs/create.ejs ✅
- **File mới được tạo**
- Sử dụng layout chuẩn từ đầu
- Form tạo cấu hình khảo sát
- Auto-generate URL slug

### 7. views/survey/public-form.ejs ✅
- Giữ nguyên layout độc lập (không cần sidebar/header)
- Chỉ sửa `lang="vi"` → `lang="en"`
- Giữ nguyên Virtual Select và Flatpickr

## Layout Files hiện có

Đã kiểm tra và xác nhận các file layout tồn tại:

```
views/layout/
├── footer.ejs ✅
├── head.ejs ✅
├── header.ejs ✅
├── sidebar.ejs ✅
└── thong-tin-co-ban.ejs ✅
```

## CSS và JavaScript

### CSS Files:
- `/css/survey-system.css` - Styles riêng cho hệ thống khảo sát
- Tích hợp với `/css/sb-admin-2.css` hiện có

### JavaScript Files:
- `/js/survey-system.js` - Logic riêng cho hệ thống khảo sát
- Tích hợp với các library hiện có

### External Libraries:
- Virtual Select: `/vendor/virtual-select/`
- Flatpickr: `/vendor/flatpickr/`
- jQuery UI: CDN (cho drag & drop)

## Kiểm tra hoạt động

Sau khi sửa đổi, các trang sẽ hoạt động bình thường với:

1. **Sidebar navigation** - Từ layout hiện có
2. **Header/topbar** - Từ layout hiện có  
3. **Footer** - Từ layout hiện có
4. **Responsive design** - Từ sb-admin-2 theme
5. **Authentication** - Middleware hiện có
6. **Authorization** - Role-based access control

## Routes cần kiểm tra

Đảm bảo các routes sau hoạt động:

```
GET /projects                     ✅
GET /projects/create              ✅
GET /projects/:id/edit            ✅
GET /projects/:id/surveys         ✅
GET /projects/:id/surveys/create  ✅
GET /survey-configs/:id/fields    ✅
GET /survey/:slug                 ✅
```

## Lưu ý quan trọng

1. **Không thay đổi logic** - Chỉ sửa layout structure
2. **Giữ nguyên functionality** - DataTable, forms, validation
3. **Tương thích với theme** - Sử dụng sb-admin-2 classes
4. **Responsive design** - Hoạt động trên mobile/tablet
5. **Browser compatibility** - Tương thích với các browser chính

## Testing

Để test các thay đổi:

1. Khởi động server: `npm start`
2. Truy cập `/projects` 
3. Kiểm tra sidebar, header hiển thị đúng
4. Test tạo dự án mới
5. Test tạo khảo sát mới
6. Test form công khai

Tất cả các file EJS đã được cập nhật để sử dụng đúng layout structure của dự án hiện tại.
