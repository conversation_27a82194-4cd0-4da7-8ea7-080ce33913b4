# 🎯 Hướng dẫn sử dụng Campaign Selector cho Admin

## 📋 Tổng quan

Tính năng Campaign Selector yêu cầu admin phải chọn một campaign cụ thể để thêm, sử<PERSON>, xóa và xem danh sách dữ liệu. Admin không thể xem tất cả campaign cùng lúc.

## 🛠️ Cài đặt

### 1. Không cần chạy script database
Tính năng này sử dụng trực tiếp trường `campaign_id` trong bảng `user`.

### 2. Khởi động lại ứng dụng
```bash
npm start
```

## 🎯 Các tính năng

### 1. **Campaign Selector trong Sidebar**
- Hiển thị select dropdown cho admin trong sidebar
- Danh sách các campaign active để chọn
- Hiển thị campaign hiện tại đang được chọn

### 2. **Chuyển đổi Campaign**
- Admin bắt buộc phải chọn một campaign cụ thể
- Dữ liệu sẽ được filter theo campaign được chọn
- Tự động reload trang để áp dụng filter mới

### 3. **Lưu trữ trực tiếp**
- Campaign được chọn cập nhật trực tiếp vào `user.campaign_id`
- Thay đổi campaign_id của admin trong database
- Admin luôn làm việc với một campaign cụ thể

## 🔗 API Endpoints

### Lấy danh sách campaign
```
GET /admin/campaign/options
```

### Chuyển đổi campaign
```
POST /admin/campaign/switch
Body: { "campaign_id": 123 }
```

## 🧪 Testing

### Test thủ công:

1. **Đăng nhập với tài khoản admin**
   - Kiểm tra sidebar có hiển thị Campaign Selector không
   - Nếu chưa chọn campaign, hiển thị cảnh báo

2. **Test chọn campaign cụ thể**
   - Chọn một campaign từ dropdown
   - Kiểm tra trang có reload không
   - Xác nhận dữ liệu được filter theo campaign đã chọn

3. **Test chuyển đổi campaign**
   - Chọn campaign khác từ dropdown
   - Kiểm tra trang có reload không
   - Xác nhận dữ liệu được filter theo campaign mới

4. **Test với user thường**
   - Đăng nhập với tài khoản không phải admin
   - Kiểm tra sidebar không hiển thị Campaign Selector
   - Dữ liệu vẫn được filter theo campaign của user

### Test các module:

1. **Viêm gan** (`/viem-gan`)
2. **Uốn ván** (`/uon-van`) 
3. **Hội chẩn** (`/hoi-chan`)
4. **Viêm gan Mt1** (`/viem-gan-mt1`)
5. **Đánh giá KPA** (`/research`)
6. **Phiếu hội chẩn** (`/standard`)

## 🔧 Cấu trúc Code

### Files được thêm/sửa:

1. **routes/admin.js** - Thêm routes cho campaign options và switch
2. **controllers/adminController.js** - Thêm methods getCampaignOptions và switchCampaign
3. **app.js** - Cập nhật middleware để hỗ trợ temp_campaign_id
4. **services/securityService.js** - Cập nhật applyRoleBasedFiltering
5. **views/layout/sidebar.ejs** - Thêm Campaign Selector UI
6. **views/layout/footer.ejs** - Include JavaScript và user data
7. **public/js/campaign-selector.js** - JavaScript xử lý campaign switching
8. **database/add_temp_campaign_id.sql** - Database migration

## 🔒 Bảo mật

- Chỉ admin mới có thể sử dụng Campaign Selector
- Kiểm tra quyền admin ở cả frontend và backend
- Validate campaign_id trước khi switch
- Sử dụng session để lưu trữ tạm thời, không ảnh hưởng database user

## 🐛 Troubleshooting

### Campaign Selector không hiển thị:
- Kiểm tra user có quyền admin không
- Kiểm tra JavaScript có load không
- Kiểm tra console có lỗi không

### Không thể chuyển đổi campaign:
- Kiểm tra API endpoint có hoạt động không
- Kiểm tra campaign_id có hợp lệ không
- Kiểm tra database có trường temp_campaign_id không

### Dữ liệu không được filter:
- Kiểm tra securityService.applyRoleBasedFiltering
- Kiểm tra middleware authentication
- Kiểm tra temp_campaign_id trong session
