# Hướng dẫn Chức năng Export Excel - Tối ưu hóa

## 📋 Tổng quan thay đổi

Đã tối ưu hóa chức năng `exportToExcel` trong `patientController.js` để:
- **Tách riêng từng path**: Mỗi path có hàm export riêng biệt
- **Tối ưu hóa headers**: Bỏ text sau dấu `_` trong headers
- **Xử lý multiple choice**: Tách các giá trị array thành các cột riêng biệt
- **Cấu trúc modular**: Dễ bảo trì và mở rộng

---

## 🏗️ Cấu trúc mới

### 1. **patientController.js** - Controller chính

#### Hàm chính:
- `exportToExcel()`: Router chính, phân luồng theo path
- `exportViemGanMt1()`: Export cho viêm gan MT1
- `exportViemGan()`: Export cho viêm gan
- `exportUonVan()`: Export cho uốn ván
- `exportHoiChan()`: Export cho hội chẩn
- `exportStandard()`: Export cho standard

#### Helper functions:
- `getBasicPatientData()`: Lấy dữ liệu cơ bản của bệnh nhân
- `createExcelFile()`: Tạo file Excel với headers và data

### 2. **Các Controller chi tiết**

Mỗi controller có hàm `getPatientExportData()`:
- `hepstitisMt1Controller.js`
- `hepatitisController.js`
- `tetanusController.js`
- `liverSurgeryController.js`
- `standardController.js`

---

## 📊 Định dạng Headers mới

### Trước đây:
```javascript
'A1_hoten', 'A2_mabenhan', 'A3_ngaydieutra', ...
'B10_buaphugi_1', 'B10_buaphugi_2', ...
```

### Hiện tại:
```javascript
'A1', 'A2', 'A3', ...
'B101', 'B102', 'B103', 'B104', 'B105', // B10 tách thành 5 cột
'B121', 'B122', 'B123', 'B124', 'B125', // B12 tách thành 5 cột
```

---

## 🔧 Xử lý Multiple Choice

### Ví dụ: B10_buaphugi (Bữa phụ ăn gì)
```javascript
// Dữ liệu gốc: "1,3,5" (chọn sữa, nước ngọt, khác)
// Kết quả export:
exportData.buaphugi_1 = 1; // Sữa - có
exportData.buaphugi_2 = 0; // Bánh kẹo - không
exportData.buaphugi_3 = 1; // Nước ngọt - có
exportData.buaphugi_4 = 0; // Quả chín - không
exportData.buaphugi_5 = 1; // Khác - có
```

### Ví dụ: B12_kiengi (Ăn kiêng gì)
```javascript
// Dữ liệu gốc: "2,4" (kiêng bia, chất đạm)
// Kết quả export:
exportData.kiengi_1 = 0; // Rượu - không
exportData.kiengi_2 = 1; // Bia - có
exportData.kiengi_3 = 0; // Mỡ và thịt mỡ - không
exportData.kiengi_4 = 1; // Chất đạm - có
exportData.kiengi_5 = 0; // Khác - không
```

---

## 📁 Cấu trúc dữ liệu theo Path

### 1. **viem-gan-mt1**
- Bảng chính: `viem_gan_mt1_dhnv` (dấu hiệu nhập viện)
- Bảng SGA: `viem_gan_mt1_sga`
- Bảng sơ gan: `viem_gan_mt1_so_gan`

### 2. **viem-gan**
- Bảng chính: `viem_gan_dhnv` (dấu hiệu nhập viện)
- Bảng SGA: `viem_gan_sga`
- Bảng sơ gan: `viem_gan_so_gan`

### 3. **uon-van**
- Bảng chính: `uon_van_ls` (lâm sàng)
- Bảng SGA: `uon_van_sga`
- Bảng tiêu hóa: `uon_van_ttth`

### 4. **hoi-chan**
- Bảng chính: `cat_gan_nho_kpa` (khẩu phần ăn)
- Bảng SGA: `cat_gan_nho_sga` (nếu có)
- Bảng sơ gan: `cat_gan_nho_so_gan` (nếu có)

### 5. **standard**
- Bảng chính: `phieu_hoi_chan_danh_gia`
- Bảng SGA: `standard_sga` (nếu có)
- Bảng sơ gan: `standard_so_gan` (nếu có)

---

## 🚀 Cách sử dụng

### 1. **API Endpoint**
```javascript
GET /api/export-excel/:path
```

### 2. **Ví dụ gọi API**
```javascript
// Export viêm gan MT1
fetch('/api/export-excel/viem-gan-mt1')

// Export uốn ván
fetch('/api/export-excel/uon-van')

// Export hội chẩn
fetch('/api/export-excel/hoi-chan')
```

### 3. **Response**
- **Success**: File Excel được download
- **Error**: JSON response với thông báo lỗi

---

## 🔍 Xử lý dữ liệu theo ngày

### Multiple Dates cho SGA
```javascript
// Lấy dữ liệu SGA mới nhất
const latestSga = sgaList.sort((a, b) => new Date(b.time) - new Date(a.time))[0];

// Nếu cần xử lý multiple dates, có thể tạo thêm các cột
if (sgaList.length > 1) {
    sgaList.forEach((sga, index) => {
        const suffix = index + 1;
        exportData[`cannang6thang_${suffix}`] = sga.cn_6_thang;
        exportData[`tieuhoa_${suffix}`] = sga.tieu_chung_th;
        // ...
    });
}
```

---

## 🛠️ Mở rộng cho Path mới

### 1. **Thêm hàm export trong patientController.js**
```javascript
// Export Path mới
exportNewPath: async function(req, res, patients, user) {
    try {
        const exportData = [];
        
        for (const patient of patients) {
            let patientData = patient.getBasicPatientData(patient);
            
            // Lấy dữ liệu chi tiết từ controller tương ứng
            const detailController = require('../controllers/newPathController');
            if (detailController.getPatientExportData) {
                const detailData = await detailController.getPatientExportData(patient.id, 'new-path', user);
                patientData = { ...patientData, ...detailData };
            }
            
            exportData.push(patientData);
        }

        // Headers cho path mới
        const headers = [
            'A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'A9', 'A10', 'A11', 'A12',
            'B1', 'B2', 'B3', 'B4', 'B5', 'B6', 'B7', 'B8', 'B9', 
            'B101', 'B102', 'B103', 'B104', 'B105',
            'B11', 
            'B121', 'B122', 'B123', 'B124', 'B125',
            'B13', 'B14', 'B15', 'B16', 'B17', 'B18', 'B19', 'B20', 'B21',
            'C1', 'C2', 'C3', 'C4', 'C5',
            'D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7',
            'E1', 'E2', 'E3', 'E4'
        ];

        return await patient.createExcelFile(res, exportData, headers, 'new-path');

    } catch (error) {
        commonService.saveLog(req, error.message, error.stack);
        res.status(500).json({ 
            success: false, 
            message: 'Có lỗi xảy ra khi xuất dữ liệu new-path!' 
        });
    }
}
```

### 2. **Thêm case trong exportToExcel()**
```javascript
switch(path) {
    case 'new-path':
        return await patient.exportNewPath(req, res, patients, user);
    // ... các case khác
}
```

### 3. **Tạo controller mới với getPatientExportData()**
```javascript
// Trong newPathController.js
getPatientExportData: async function(patientId, path, user) {
    try {
        // Logic lấy dữ liệu cho path mới
        const exportData = {};
        
        // Lấy dữ liệu từ các bảng tương ứng
        // Map dữ liệu theo format yêu cầu
        
        return exportData;
    } catch (error) {
        console.error('Error in getPatientExportData:', error);
        return {};
    }
}
```

---

## ✅ Lợi ích của cấu trúc mới

1. **Modular**: Mỗi path có logic riêng biệt
2. **Maintainable**: Dễ bảo trì và debug
3. **Scalable**: Dễ dàng thêm path mới
4. **Consistent**: Format headers thống nhất
5. **Flexible**: Xử lý multiple choice linh hoạt
6. **Performance**: Tối ưu hóa truy vấn database

---

## 🐛 Troubleshooting

### Lỗi thường gặp:

1. **Controller không tìm thấy**
   - Kiểm tra đường dẫn require
   - Đảm bảo file controller tồn tại

2. **Hàm getPatientExportData không tồn tại**
   - Thêm hàm vào controller tương ứng
   - Kiểm tra tên hàm chính xác

3. **Dữ liệu không hiển thị**
   - Kiểm tra mapping dữ liệu
   - Kiểm tra tên cột trong database

4. **Multiple choice không hoạt động**
   - Kiểm tra format dữ liệu gốc
   - Đảm bảo xử lý split và parseInt đúng cách

