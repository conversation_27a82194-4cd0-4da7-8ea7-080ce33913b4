# Hướng dẫn sử dụng tính năng Chọn cột hiển thị trong bảng thực đơn

## Tổng quan
Tính năng này cho phép người dùng tùy chỉnh các cột hiển thị trong bảng thực đ<PERSON>, bao gồm toàn bộ thông tin dinh dưỡng từ cả 2 bảng `food_info` và `main_nutrients`. C<PERSON>u hình sẽ được lưu riêng cho từng bệnh nhân.

## Các tính năng chính

### 1. Chọn cột hiển thị
- **Thông tin cơ bản**: <PERSON><PERSON><PERSON> thự<PERSON>h<PERSON>, mã thực <PERSON>h<PERSON>, kh<PERSON><PERSON> lư<PERSON>
- **Chất dinh dưỡng chính**: N<PERSON><PERSON> lư<PERSON>, protein, chất béo, carbohydrate, chất xơ, nước, tro
- **<PERSON><PERSON><PERSON>g chất**: <PERSON><PERSON>, phospho, s<PERSON>t, kẽm, natri, kali, magie, mangan, đồng, selen
- **Axit béo**: <PERSON><PERSON><PERSON> ch<PERSON> béo, axit b<PERSON><PERSON> b<PERSON> h<PERSON>, kh<PERSON><PERSON> b<PERSON> hò<PERSON> đơn/đa, cholesterol
- **<PERSON>tein & Amino acid**: Protein động vật, lysin, methionin, tryptophan, v.v.
- **Vitamin**: Vitamin A, B1, B2, B6, B12, C, E, K, folate
- **Đường**: Tổng đường, glucose, fructose, sucrose, lactose, maltose

### 2. Sắp xếp thứ tự cột
- Kéo thả để thay đổi thứ tự hiển thị
- Cột được chọn sẽ hiển thị theo thứ tự đã sắp xếp

### 3. Lưu cấu hình
- Cấu hình được lưu riêng cho từng bệnh nhân
- Tự động load cấu hình khi vào lại trang

## Cách sử dụng

### Bước 1: Mở bộ chọn cột
1. Vào trang chi tiết khẩu phần ăn của bệnh nhân
2. Nhấn vào nút **cài đặt** (⚙️) trong phần "Chọn cột hiển thị"

### Bước 2: Chọn cột muốn hiển thị
1. Tích chọn các checkbox tương ứng với cột muốn hiển thị
2. Bỏ tích các cột không muốn hiển thị
3. Các cột được nhóm theo chủ đề để dễ tìm

### Bước 3: Sắp xếp thứ tự (tuỳ chọn)
1. Trong phần "Thứ tự cột", kéo thả các item để thay đổi thứ tự
2. Thứ tự từ trên xuống dưới sẽ tương ứng với thứ tự từ trái sang phải trong bảng

### Bước 4: Áp dụng và lưu
1. Nhấn **"Áp dụng"** để xem thay đổi ngay lập tức
2. Nhấn **"Lưu cấu hình"** để lưu vĩnh viễn cho bệnh nhân này
3. Nhấn **"Đặt lại mặc định"** để về cấu hình ban đầu

## Tính năng tự động

### Tính toán lại khi thay đổi khối lượng
- Khi thay đổi khối lượng thực phẩm, tất cả các cột dinh dưỡng sẽ được tính toán lại
- Công thức: `Giá trị mới = Giá trị gốc × (Khối lượng mới / Khối lượng gốc)`

### Dòng tổng cộng
- Tự động tính tổng cho tất cả các cột số
- Hiển thị phần trăm năng lượng cho protein, chất béo, carbohydrate
- Cột khối lượng không tính tổng

### Lưu cấu hình tự động
- Cấu hình được lưu vào trường `table_display_config` trong bảng `patients_research`
- Format JSON: `{"visible_columns": [...], "column_order": [...]}`

## Cấu trúc bảng
### Cột cố định (không thay đổi)
- **Bữa ăn**: Hiển thị tên bữa ăn (sáng, trưa, tối, v.v.)
- **Tên món ăn**: Hiển thị tên thực phẩm
- **Thao tác**: Nút xóa thực phẩm khỏi thực đơn

### Cột có thể tùy chỉnh
- Các cột thông tin dinh dưỡng của thực phẩm
- Có thể chọn hiển thị/ẩn và sắp xếp thứ tự

## Cấu hình mặc định
```json
{
  "visible_columns": ["weight", "energy", "protein", "fat", "carbohydrate"],
  "column_order": ["weight", "energy", "protein", "fat", "carbohydrate"]
}
```

## Lưu ý kỹ thuật

### Database Schema
```sql
ALTER TABLE `patients_research` 
ADD COLUMN `table_display_config` TEXT NULL DEFAULT NULL 
COMMENT 'Cấu hình hiển thị cột cho bảng thực đơn (JSON format)';
```

### API Endpoints
- `POST /khau-phan-an/save-table-config`: Lưu cấu hình
- `GET /khau-phan-an/get-table-config`: Lấy cấu hình

### JavaScript Functions
- `createColumnSelector()`: Tạo UI chọn cột
- `applyColumnConfig()`: Áp dụng cấu hình mới
- `saveColumnConfig()`: Lưu cấu hình vào database
- `loadTableDisplayConfig()`: Load cấu hình từ database
- `rebuildTableWithNewColumns()`: Rebuild bảng với cột mới

## Troubleshooting

### Lỗi thường gặp
1. **Không lưu được cấu hình**: Kiểm tra quyền truy cập và ID bệnh nhân
2. **Cột không hiển thị đúng**: Kiểm tra dữ liệu thực phẩm có đầy đủ không
3. **Tính toán sai**: Kiểm tra dữ liệu gốc và công thức tính toán

### Debug
- Mở Developer Tools > Console để xem log
- Kiểm tra network tab cho các API call
- Xem giá trị `currentDisplayConfig` trong console

## Cải tiến tương lai
1. Export/Import cấu hình giữa các bệnh nhân
2. Cấu hình mặc định theo khoa/phòng ban
3. Thêm filter và search cho từng cột
4. Hiển thị đơn vị đo lường động
5. Tính toán nhu cầu dinh dưỡng khuyến nghị 