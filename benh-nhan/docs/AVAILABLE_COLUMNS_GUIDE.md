# Hướng dẫn sử dụng AvailableColumns

## Tổng quan

Hệ thống `availableColumns` đã được tái cấu trúc để tránh việc phải sửa ở nhiều nơi khi thay đổi các giá trị dinh dưỡng. Bây giờ tất cả các cấu hình cột được quản lý tập trung tại một nơi.

## Cấu trúc mới

### 1. File chung: `public/js/availableColumns.js`

File này chứa tất cả các cấu hình cột dinh dưỡng và nhóm cột. Đây là nơi duy nhất bạn cần chỉnh sửa khi:
- Thêm cột dinh dưỡng mới
- Xóa cột dinh dưỡng
- Thay đổi tên hiển thị của cột
- Thay đổi nhóm của cột
- Thay đổi cột mặc định

### 2. API Endpoint: `/api/available-columns`

API này trả về dữ liệu từ file chung để các trang web có thể sử dụng.

### 3. Các file sử dụng:

- `public/js/menuExample.js` - Sử dụng cho chức năng chọn cột hiển thị trong thực đơn
- `views/research/list.ejs` - Sử dụng cho modal xuất Excel trong nghiên cứu

## Cách thêm/xóa/sửa cột dinh dưỡng

### Thêm cột mới:

1. Mở file `public/js/availableColumns.js`
2. Thêm cột mới vào object `AVAILABLE_COLUMNS`:

```javascript
'new_column': { 
    label: 'Tên hiển thị mới', 
    group: 'minerals', // hoặc nhóm khác
    default: false // true nếu muốn hiển thị mặc định
},
```

3. Nếu cần tạo nhóm mới, thêm vào `COLUMN_GROUPS`:

```javascript
'new_group': 'Tên nhóm mới'
```

### Xóa cột:

1. Mở file `public/js/availableColumns.js`
2. Xóa dòng tương ứng trong `AVAILABLE_COLUMNS`

### Sửa tên hiển thị:

1. Mở file `public/js/availableColumns.js`
2. Thay đổi giá trị `label` của cột tương ứng

### Thay đổi nhóm cột:

1. Mở file `public/js/availableColumns.js`
2. Thay đổi giá trị `group` của cột tương ứng

### Thay đổi cột mặc định:

1. Mở file `public/js/availableColumns.js`
2. Thay đổi giá trị `default` từ `false` thành `true` hoặc ngược lại

## Các nhóm cột hiện có

- `basic`: Thông tin cơ bản
- `main_nutrients`: Năng lượng & Chất chính
- `minerals`: Khoáng chất
- `fatty_acids`: Chất béo chi tiết
- `amino_acids`: Amino acid
- `vitamins`: Vitamin
- `sugars`: Đường
- `antioxidants`: Chất chống oxy hóa
- `phytonutrients`: Phytonutrient
- `metabolites`: Chất chuyển hóa

## Lợi ích

1. **Tập trung quản lý**: Chỉ cần sửa ở một nơi duy nhất
2. **Tự động đồng bộ**: Tất cả các trang sử dụng sẽ tự động cập nhật
3. **Dễ bảo trì**: Không cần nhớ sửa ở nhiều file khác nhau
4. **Tính nhất quán**: Đảm bảo tên hiển thị và nhóm cột giống nhau ở mọi nơi

## Lưu ý

- Khi thay đổi file `availableColumns.js`, cần restart server để API cập nhật
- Các trang web sẽ tự động load dữ liệu mới khi refresh
- Nếu API không hoạt động, hệ thống sẽ tự động fallback về file JavaScript chung
