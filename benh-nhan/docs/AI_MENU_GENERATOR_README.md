# AI Menu Generator - Hướng dẫn sử dụng

## Tổng quan
AI Menu Generator là tính năng tạo thực đơn thông minh sử dụng Google Gemini AI và Pinecone vector search để tạo ra các thực đơn cân bằng dinh dưỡng dựa trên yêu cầu của người dùng.

## Cài đặt và Cấu hình

### 1. Dependencies
Các package đã được cài đặt:
- `@google/genai`: Tích hợp Google Gemini AI (phiên bản mới)
- `@pinecone-database/pinecone`: Vector database cho tìm kiếm thực phẩm

### 2. Environment Variables
Thêm các biến môi trường sau vào file `.env`:

```env
# AI Menu Generator Configuration
GEMINI_API_KEY=your-gemini-api-key-here
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=food-nutrition-index
```

### 3. Lấy API Keys

#### Google Gemini API Key:
1. Truy cập [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Tạo API key mới
3. Copy và paste vào `GEMINI_API_KEY`

#### Pinecone API Key:
1. Đăng ký tài khoản tại [Pinecone](https://www.pinecone.io/)
2. Tạo project mới
3. Lấy API key và environment từ dashboard
4. Paste vào `PINECONE_API_KEY` và `PINECONE_ENVIRONMENT`

## Cách sử dụng

### 1. Đồng bộ dữ liệu thực phẩm (Lần đầu)
Trước khi sử dụng, cần đồng bộ dữ liệu food_info lên Pinecone:

```javascript
// Qua API (Admin only)
POST /api/ai-menu/sync-foods

// Hoặc qua test page
GET /test/ai-menu (Admin only)
```

### 2. Tạo thực đơn qua giao diện
1. Vào trang khẩu phần ăn
2. Click nút robot (🤖) để mở AI Menu Generator
3. Nhập yêu cầu tạo thực đơn
4. Thêm sở thích và hạn chế (tùy chọn)
5. Đặt mục tiêu dinh dưỡng (tùy chọn)
6. Click "Tạo thực đơn AI"
7. Xem kết quả và click "Sử dụng thực đơn này"

### 3. Tạo thực đơn qua API

#### Tạo thực đơn tùy chỉnh:
```javascript
POST /api/ai-menu/generate
Content-Type: application/json

{
  "requirements": "Tạo thực đơn cho bệnh nhân tiểu đường, 1800 kcal/ngày",
  "preferences": ["ít đường", "nhiều chất xơ"],
  "restrictions": ["đường", "kẹo", "bánh ngọt"],
  "meal_count": 3,
  "target_nutrition": {
    "energy": 1800,
    "protein": 90,
    "carbohydrate": 180,
    "lipid": 60
  }
}
```

#### Tạo thực đơn mẫu:
```javascript
GET /api/ai-menu/sample?type=diabetes
// Types: balanced, diabetes, weight_loss
```

### 4. Tìm kiếm thực phẩm:
```javascript
GET /api/ai-menu/search-foods?query=thịt gà&limit=10
```

## API Endpoints

| Method | Endpoint | Mô tả | Quyền |
|--------|----------|-------|-------|
| POST | `/api/ai-menu/generate` | Tạo thực đơn AI | User có quyền tạo thực đơn |
| GET | `/api/ai-menu/sample` | Tạo thực đơn mẫu | User có quyền tạo thực đơn |
| POST | `/api/ai-menu/sync-foods` | Đồng bộ dữ liệu | Admin only |
| GET | `/api/ai-menu/search-foods` | Tìm kiếm thực phẩm | Authenticated user |
| GET | `/api/ai-menu/stats` | Thống kê hệ thống | Admin only |
| GET | `/api/ai-menu/test` | Test kết nối | Admin only |
| GET | `/test/ai-menu` | Trang test | Admin only |

## Cấu trúc Response

### Thành công:
```javascript
{
  "success": true,
  "message": "Tạo thực đơn AI thành công",
  "data": {
    "menu": {
      "name": "Thực đơn AI - Tiểu đường",
      "created_at": "2025-01-09T10:00:00Z",
      "ai_generated": true,
      "requirements": "Tạo thực đơn cho bệnh nhân tiểu đường...",
      "detail": [
        {
          "id": 1,
          "name": "Sáng",
          "listFood": [
            {
              "id": 1,
              "id_food": 123,
              "name": "Cơm trắng",
              "weight": 150,
              "energy": 195,
              "protein": 4.05,
              "carbohydrate": 43.5,
              "lipid": 0.45,
              "fiber": 0.6
            }
          ]
        }
      ]
    },
    "nutrition_summary": {
      "total_energy": 1800,
      "total_protein": 90,
      "total_carbohydrate": 180,
      "total_lipid": 60,
      "total_fiber": 25
    }
  },
  "metadata": {
    "foods_searched": 50,
    "foods_used": 30,
    "generation_time": "2025-01-09T10:00:00Z"
  }
}
```

### Lỗi:
```javascript
{
  "success": false,
  "message": "Không thể tạo thực đơn AI: Lỗi kết nối Gemini API"
}
```

## Testing

### 1. Chạy test tự động:
```bash
node test/ai-menu-system-test.js
```

### 2. Test qua giao diện:
Truy cập `/test/ai-menu` (Admin only) để test các tính năng:
- Test kết nối services
- Test tạo thực đơn mẫu
- Test tạo thực đơn tùy chỉnh
- Đồng bộ dữ liệu

## Troubleshooting

### 1. Lỗi "GEMINI_API_KEY not found"
- Kiểm tra file `.env` có chứa `GEMINI_API_KEY`
- Restart server sau khi thêm environment variable

### 2. Lỗi "Failed to initialize Pinecone service"
- Kiểm tra `PINECONE_API_KEY` và `PINECONE_ENVIRONMENT`
- Đảm bảo Pinecone account còn quota

### 3. Lỗi "No foods found"
- Chạy đồng bộ dữ liệu: `POST /api/ai-menu/sync-foods`
- Kiểm tra database có dữ liệu food_info

### 4. AI tạo thực đơn không hợp lý
- Kiểm tra prompt trong `geminiService.js`
- Thử với yêu cầu cụ thể hơn
- Kiểm tra dữ liệu thực phẩm trong Pinecone

### 5. Performance chậm
- Giảm `search_limit` trong request
- Tối ưu Pinecone index
- Cache kết quả tìm kiếm

## Tùy chỉnh

### 1. Thay đổi AI Model
Sửa trong `services/geminiService.js`:
```javascript
// Trong method generateMenu, thay đổi model:
const response = await this.genAI.models.generateContent({
    model: 'gemini-2.0-flash-001', // hoặc model khác
    contents: fullPrompt
});
```

### 2. Tùy chỉnh Prompt
Sửa `createSystemPrompt()` trong `services/geminiService.js`

### 3. Thêm loại thực đơn mẫu
Sửa `sampleRequests` trong `services/aiMenuService.js`

### 4. Tùy chỉnh Vector Embedding
Thay thế `generateSimpleEmbedding()` trong `services/pineconeService.js` bằng model embedding thực tế

## Bảo mật

### 1. API Keys
- Không commit API keys vào git
- Sử dụng environment variables
- Rotate keys định kỳ

### 2. Rate Limiting
- Implement rate limiting cho AI endpoints
- Monitor usage để tránh vượt quota

### 3. Input Validation
- Validate tất cả input từ user
- Sanitize text trước khi gửi AI

## Monitoring

### 1. Logs
- Check logs trong console
- Monitor AI API usage
- Track generation success rate

### 2. Metrics
- Response time
- Success/failure rate
- User adoption

## Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong console
2. Test kết nối qua `/test/ai-menu`
3. Xem documentation tại `/docs/AI_MENU_GENERATOR_DESIGN.md`
4. Contact admin để hỗ trợ
