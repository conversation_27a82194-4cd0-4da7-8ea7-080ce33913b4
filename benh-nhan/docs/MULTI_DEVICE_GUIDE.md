# 🚀 Hướng dẫn sử dụng tính năng Multi-Device Login

## 📋 Tổng quan

Tính năng Multi-Device Login cho phép user đăng nhập trên nhiều thiết bị cùng lúc và quản lý các session đang hoạt động.

## 🗄️ Cấu trúc Database

### Bảng mới được tạo:

1. **`user_sessions`** - L<PERSON><PERSON> lịch sử đăng nhập từng thiết bị
2. **`user_session_settings`** - Cài đặt session cho từng user
3. **Cập nhật bảng `user`** - Thêm các trường hỗ trợ multi-device

## 🛠️ Cài đặt

### 1. Chạy script database
```sql
-- Chạy file: database/multi_device_schema.sql
```

### 2. Khởi động lại ứng dụng
```bash
npm start
```

## 🎯 <PERSON><PERSON><PERSON> tính năng chính

### 1. **<PERSON><PERSON><PERSON> nhập nhiều thiết bị**
- User c<PERSON> thể đăng nhập trên nhiều thiết bị cùng lúc
- Mỗi thiết bị được gán một session riêng biệt
- Tự động detect thông tin thiết bị (browser, OS, device type)

### 2. **Quản lý thiết bị**
- Xem danh sách tất cả thiết bị đang đăng nhập
- Logout từng thiết bị cụ thể
- Logout tất cả thiết bị khác (trừ thiết bị hiện tại)

### 3. **Cài đặt session**
- Số lượng session tối đa cho phép
- Thời gian timeout session
- Cho phép/không cho phép nhiều thiết bị

## 🔗 Routes API

### Quản lý thiết bị
- `GET /devices-page` - Trang quản lý thiết bị (giao diện)
- `GET /devices` - API lấy danh sách thiết bị
- `POST /devices/logout` - Logout một thiết bị cụ thể
- `POST /devices/logout-all-others` - Logout tất cả thiết bị khác

### Cài đặt session
- `GET /devices/settings` - Lấy cài đặt session
- `POST /devices/settings` - Cập nhật cài đặt session

## 🎨 Giao diện

### Truy cập trang quản lý thiết bị:
1. Đăng nhập vào hệ thống
2. Click vào menu "Quản lý thiết bị" trong sidebar
3. Hoặc truy cập trực tiếp: `http://localhost:3000/devices-page`

### Các chức năng trong giao diện:
- **Xem danh sách thiết bị**: Hiển thị tất cả thiết bị đang đăng nhập
- **Cài đặt session**: Thay đổi số session tối đa, timeout, v.v.
- **Logout thiết bị**: Logout từng thiết bị riêng lẻ
- **Logout tất cả**: Logout tất cả thiết bị khác

## 🔧 Cấu hình

### Biến môi trường
```env
JWT_SECRET=your-super-secret-jwt-key
ENABLE_CAPTCHA=1
SITEKEYRECAPTCHA=your-recaptcha-site-key
SECRETKEYRECAPTCHA=your-recaptcha-secret-key
```

### Cài đặt mặc định
- Số session tối đa: 5
- Timeout session: 24 giờ
- Cho phép nhiều thiết bị: Có

## 🧪 Testing

### Chạy test tự động:
```bash
node test-multi-device.js
```

### Test thủ công:
1. Đăng nhập trên nhiều browser/thiết bị
2. Truy cập `/devices-page` để xem danh sách
3. Thử logout từng thiết bị
4. Kiểm tra cài đặt session

## 🔒 Bảo mật

### Tính năng bảo mật:
- Mỗi session có token ID duy nhất
- Tự động logout session hết hạn
- Kiểm tra IP address và User-Agent
- Giới hạn số lượng session đồng thời

### Best practices:
- Đặt timeout session hợp lý
- Giới hạn số session tối đa
- Thường xuyên kiểm tra session không hoạt động

## 🐛 Troubleshooting

### Lỗi thường gặp:

1. **"Token không hợp lệ"**
   - Kiểm tra JWT_SECRET trong .env
   - Đảm bảo database đã được cập nhật

2. **"Không thể tạo session"**
   - Kiểm tra bảng `user_sessions` đã được tạo
   - Kiểm tra quyền ghi database

3. **"Session hết hạn"**
   - Tăng thời gian timeout trong cài đặt
   - Kiểm tra timezone server

### Debug:
```bash
# Xem log session
SELECT * FROM user_sessions WHERE user_id = ? ORDER BY login_at DESC;

# Xem cài đặt user
SELECT * FROM user_session_settings WHERE user_id = ?;
```

## 📈 Monitoring

### Metrics cần theo dõi:
- Số lượng session active
- Session timeout rate
- Login attempts từ nhiều thiết bị
- Failed logout attempts

### Queries hữu ích:
```sql
-- Số session active theo user
SELECT user_id, COUNT(*) as active_sessions 
FROM user_sessions 
WHERE is_active = 1 
GROUP BY user_id;

-- Session sắp hết hạn
SELECT * FROM user_sessions 
WHERE last_activity < DATE_SUB(NOW(), INTERVAL 20 HOUR) 
AND is_active = 1;
```

## 🔄 Migration từ Single Device

### Nếu đang sử dụng single device login:
1. Backup database
2. Chạy script migration
3. Cập nhật code để sử dụng multi-device service
4. Test kỹ lưỡng trước khi deploy production

## 📞 Support

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra log lỗi
2. Xem hướng dẫn troubleshooting
3. Liên hệ team development

---

**Lưu ý**: Tính năng này tương thích ngược với hệ thống single device hiện tại. 