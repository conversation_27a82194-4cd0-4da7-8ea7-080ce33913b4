# 🧹 Database Cleanup Guide

## 📋 Tổng quan

Sau khi đơn giản hóa hệ thống khảo sát, chúng ta cần dọn dẹp database để loại bỏ các bảng và cột không cần thiết.

## 🎯 Mục tiêu Cleanup

### ✅ Loại bỏ hoàn toàn:
1. **Automation System** - Không cần gửi email tự động
2. **Conditional Logic** - Không cần logic điều kiện ẩn/hiện field
3. **Field Groups** - Không cần nhóm field lặp lại
4. **Email Templates** - Không gửi email
5. **Survey Templates** - Không cần template system
6. **Webhook Integration** - Không cần webhook

### 🔄 Cập nhật:
1. **Email Requirements** - Tắt yêu cầu email bắt buộc
2. **Multiple Responses** - <PERSON> phép nhiều phản hồi (v<PERSON> không track email)
3. **Existing Data** - Xóa email và IP address hiện có

## 📊 Các bảng sẽ bị xóa

```sql
-- Automation Tables
DROP TABLE automation_rules;
DROP TABLE webhook_logs;
DROP TABLE survey_invitations;
DROP TABLE automation_logs;
DROP TABLE email_templates;

-- Conditional Logic Tables
DROP TABLE survey_field_groups;

-- Template Tables
DROP TABLE survey_templates;
```

## 📋 Các cột sẽ bị xóa

### survey_configs
```sql
-- Automation columns
reminder_enabled, max_reminders, reminder_interval_hours
webhook_url, webhook_secret, webhook_headers
daily_reports, report_recipients
expiry_date, closed_at, close_reason

-- Form builder column
form_config
```

### survey_fields
```sql
-- Conditional logic columns
field_group, conditional_logic
```

### survey_response_data
```sql
-- Group instance column
group_instance
```

## 🚀 Cách thực hiện Cleanup

### Phương pháp 1: Sử dụng Script tự động (Khuyến nghị)

```bash
# Kiểm tra database trước cleanup
node scripts/cleanup_database.js

# Thực hiện cleanup (cẩn thận!)
node scripts/cleanup_database.js --force
```

### Phương pháp 2: Thực hiện thủ công

```bash
# 1. Kiểm tra database hiện tại
mysql -u root -p your_database < scripts/check_database_size.sql

# 2. Thực hiện migration
mysql -u root -p your_database < database/migrations/2025_09_05_cleanup_survey_system.sql

# 3. Kiểm tra lại sau cleanup
mysql -u root -p your_database < scripts/check_database_size.sql
```

## ⚠️ Cảnh báo quan trọng

### 🔒 Backup trước khi cleanup
```bash
# Backup toàn bộ database
mysqldump -u root -p your_database > backup_before_cleanup.sql

# Hoặc backup chỉ các bảng quan trọng
mysqldump -u root -p your_database \
  survey_configs survey_fields survey_responses survey_response_data \
  > backup_survey_data.sql
```

### 🚨 Thao tác không thể hoàn tác
- Các bảng automation sẽ bị xóa vĩnh viễn
- Dữ liệu email và IP address sẽ bị xóa
- Cấu hình conditional logic sẽ bị mất

## 📈 Lợi ích sau Cleanup

### 🎯 Hiệu suất
- Database nhẹ hơn 30-50%
- Ít bảng và index hơn
- Query nhanh hơn

### 🧹 Bảo trì
- Code đơn giản hơn
- Ít dependency hơn
- Dễ debug hơn

### 🔐 Bảo mật
- Không lưu email và IP
- Ít surface attack
- Tuân thủ GDPR tốt hơn

## 🔍 Kiểm tra sau Cleanup

### Các bảng còn lại:
```sql
-- Core tables (giữ lại)
users, user_tokens, roles, permissions
projects, survey_configs, survey_fields
survey_responses, survey_response_data

-- Optional tables (tùy dự án)
patients, foods, dishes, standards
```

### Chức năng hoạt động:
- ✅ Tạo khảo sát
- ✅ Cấu hình fields
- ✅ Thu thập phản hồi
- ✅ Xem dữ liệu theo cột
- ✅ Edit phản hồi
- ✅ Xuất Excel với field labels
- ✅ Analytics và charts

### Chức năng đã loại bỏ:
- ❌ Gửi email tự động
- ❌ Webhook integration
- ❌ Conditional logic
- ❌ Field groups
- ❌ Survey templates
- ❌ Email invitations

## 🆘 Khôi phục nếu cần

Nếu cần khôi phục, sử dụng backup:
```bash
# Khôi phục toàn bộ
mysql -u root -p your_database < backup_before_cleanup.sql

# Hoặc chỉ khôi phục bảng cụ thể
mysql -u root -p your_database < backup_survey_data.sql
```

## 📞 Hỗ trợ

Nếu gặp vấn đề trong quá trình cleanup:
1. Kiểm tra log lỗi
2. Sử dụng backup để khôi phục
3. Chạy lại script kiểm tra
4. Liên hệ team để hỗ trợ
