# Hướng dẫn gộp hàm validateInput

## Tổng quan
Đã thực hiện gộp 2 hàm `validateInput` từ `commonService` và `securityService` thành một hàm thống nhất trong `securityService` với khả năng tương thích ngược và tính năng nâng cao.

## Những thay đổi đã thực hiện

### 1. Hàm validateInput mới trong securityService.js
- **Tính năng mới**: Hỗ trợ cả 2 định dạng schema (Array và Object)
- **Tự động phát hiện**: Tự động phát hiện loại schema và trả về format phù hợp
- **Tương thích ngược**: Hoàn toàn tương thích với code cũ
- **B<PERSON>o mật tăng cường**: Sanitize input để chống XSS và injection attacks
- **<PERSON><PERSON> hoạt**: Cho phép tùy chỉnh options (returnType, sanitize)

### 2. Signature hàm mới
```javascript
validateInput(inputData, schema, options = {})
```

**Parameters:**
- `inputData`: Object - Dữ liệu cần validate
- `schema`: Object|Array - Schema validation (Object cho style mới, Array cho style cũ)
- `options`: Object - Tùy chọn bổ sung
  - `returnType`: 'auto'|'object'|'array' - Kiểu trả về (mặc định: 'auto')
  - `sanitize`: boolean - Có sanitize input hay không (mặc định: true)

**Return:**
- Nếu schema là Array hoặc returnType = 'array': Trả về Array errors (format cũ)
- Nếu schema là Object hoặc returnType = 'object': Trả về Object {errors, data, isValid}

### 3. Ví dụ sử dụng

#### Style cũ (Array schema) - Tương thích ngược
```javascript
const validateRules = [
    { field: "fullname", type: "string", required: true, message: "Vui lòng nhập họ tên!" },
    { field: "email", type: "string", required: true, message: "Vui lòng nhập email!" },
    { field: "role", type: "array", required: true, message: "Vui lòng chọn quyền!" }
];

const errors = securityService.validateInput(testData, validateRules, { returnType: 'array' });
// Hoặc để auto-detect
const errors = securityService.validateInput(testData, validateRules);
```

#### Style mới (Object schema) 
```javascript
const validationSchema = {
    email: {
        required: true,
        type: 'email',
        message: 'Email không hợp lệ'
    },
    password: {
        required: true,
        type: 'string',
        minLength: 8,
        maxLength: 100,
        message: 'Mật khẩu phải có ít nhất 8 ký tự'
    }
};

const validation = securityService.validateInput(req.body, validationSchema);
if (!validation.isValid) {
    resultData.message = validation.errors.map(e => e.message).join(', ');
    return res.json(resultData);
}
const param = validation.data; // Dữ liệu đã được sanitize
```

### 4. Files đã được cập nhật
Tất cả các controller files đã được cập nhật để sử dụng `securityService.validateInput`:

- ✅ `controllers/adminController.js`
- ✅ `controllers/dishController.js`
- ✅ `controllers/hepatitisController.js`
- ✅ `controllers/hepstitisMt1Controller.js`
- ✅ `controllers/liverSurgeryController.js`
- ✅ `controllers/patientController.js`
- ✅ `controllers/researchController.js`
- ✅ `controllers/standardController.js`
- ✅ `controllers/tetanusController.js`
- ✅ `controllers/userController.js` (đã sử dụng từ trước)
- ✅ `database/test-role-multi-select.js`

### 5. Hàm validateInput cũ đã bị xóa
- ❌ Đã xóa `validateInput` khỏi `services/commonService.js`

### 6. Tính năng nâng cao

#### Sanitization tự động
- Loại bỏ các ký tự nguy hiểm như `<>`, `javascript:`, `on*=`
- Chuẩn hóa email về lowercase
- Chuyển đổi kiểu dữ liệu phù hợp

#### Validation types mới
- `email`: Kiểm tra định dạng email hợp lệ
- `array`: Kiểm tra là array
- `boolean`: Kiểm tra là boolean
- `string`: Kiểm tra là string
- `number`: Kiểm tra là number

#### Length validation
- `minLength`: Độ dài tối thiểu
- `maxLength`: Độ dài tối đa
- Hỗ trợ cả string và array

### 7. Migration Notes

#### Không cần thay đổi gì
Các controller sử dụng style cũ vẫn hoạt động bình thường nhờ auto-detection và backward compatibility.

#### Khuyến nghị cho code mới
- Sử dụng Object schema cho các validation mới
- Tận dụng tính năng sanitization
- Sử dụng validation.data thay vì dữ liệu gốc

### 8. Testing
- ✅ Test file `database/test-role-multi-select.js` đã pass
- ✅ Backward compatibility được đảm bảo
- ✅ Tất cả controller functions hoạt động bình thường

## Lợi ích của việc gộp

1. **Thống nhất code**: Chỉ còn 1 hàm validation duy nhất
2. **Bảo mật tăng cường**: Sanitization tự động chống XSS/injection
3. **Tương thích ngược**: Code cũ vẫn hoạt động
4. **Linh hoạt**: Hỗ trợ nhiều kiểu validation và options
5. **Dễ maintain**: Chỉ cần maintain 1 hàm thay vì 2
6. **Consistent**: API nhất quán trong toàn bộ dự án

## Troubleshooting

### Nếu gặp lỗi "validateInput is not defined"
Đảm bảo đã import securityService:
```javascript
const securityService = require('../services/securityService');
```

### Nếu validation return format khác với mong đợi
Sử dụng options để force return type:
```javascript
// Force return array (old style)
const errors = securityService.validateInput(data, rules, { returnType: 'array' });

// Force return object (new style)  
const result = securityService.validateInput(data, rules, { returnType: 'object' });
```

### Nếu không muốn sanitize
```javascript
const result = securityService.validateInput(data, schema, { sanitize: false });
``` 