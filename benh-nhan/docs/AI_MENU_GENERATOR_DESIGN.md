# AI Menu Generator - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> Hệ thống

## Tổng quan
Hệ thống AI Menu Generator sử dụng Google Gemini API kết hợp với Pinecone vector search để tạo thực đơn thông minh dựa trên dữ liệu food_info hiện có.

## Kiến trúc Hệ thống

### 1. Luồng xử lý chính
```
User Input → Pinecone Vector Search → Google Gemini AI → Menu Structure → Integration
```

### 2. Thành phần chính

#### A. Pinecone Vector Database
- **Mục đích**: Lưu trữ và tìm kiếm vector embeddings của food_info
- **Dữ liệu**: Tất cả thông tin dinh dưỡng từ bảng food_info (50+ trường)
- **Vector**: Embedding từ tên, mô tả và thông tin dinh dưỡng của thực phẩm
- **Metadata**: <PERSON>, tên, loạ<PERSON>, nă<PERSON>, và các thông số dinh dưỡng chính

#### B. Google Gemini AI
- **<PERSON><PERSON><PERSON> đích**: <PERSON>ân tích yêu cầu người dùng và tạo thực đơn
- **Input**: Yêu cầu người dùng + danh sách thực phẩm từ Pinecone
- **Output**: Cấu trúc thực đơn JSON theo format hiện tại
- **Prompt Engineering**: Tối ưu hóa để tạo thực đơn cân bằng dinh dưỡng

#### C. AI Menu Service
- **Mục đích**: Điều phối giữa Pinecone và Gemini
- **Chức năng**: 
  - Xử lý yêu cầu người dùng
  - Tìm kiếm thực phẩm phù hợp
  - Gọi AI để tạo thực đơn
  - Tính toán dinh dưỡng

## Cấu trúc Dữ liệu

### 1. Vector Embedding Structure
```javascript
{
  id: "food_123",
  values: [0.1, 0.2, ...], // 1536 dimensions
  metadata: {
    food_id: 123,
    name: "Cơm trắng",
    type: "cooked",
    type_year: "2017",
    energy: 130,
    protein: 2.7,
    carbohydrate: 29.0,
    // ... các trường dinh dưỡng khác
  }
}
```

### 2. AI Request Format
```javascript
{
  requirements: "Tạo thực đơn cho bệnh nhân tiểu đường, 1800 kcal/ngày",
  preferences: ["ít đường", "nhiều chất xơ"],
  restrictions: ["không có sữa"],
  meal_count: 3, // số bữa ăn
  target_nutrition: {
    energy: 1800,
    protein: 80,
    carbohydrate: 200
  }
}
```

### 3. AI Response Format
```javascript
{
  success: true,
  menu: {
    name: "Thực đơn AI - Tiểu đường",
    created_at: "2025-01-09T10:00:00Z",
    ai_generated: true,
    requirements: "Thực đơn cho bệnh nhân tiểu đường...",
    detail: [
      {
        id: 1,
        name: "Sáng",
        listFood: [
          {
            id: 1,
            id_food: 123,
            name: "Cơm trắng",
            weight: 150,
            energy: 195,
            protein: 4.05,
            // ... tất cả trường dinh dưỡng
          }
        ]
      }
    ]
  },
  nutrition_summary: {
    total_energy: 1800,
    total_protein: 80,
    // ... tổng hợp dinh dưỡng
  }
}
```

## API Endpoints

### 1. `/api/ai-menu/generate` (POST)
- **Mục đích**: Tạo thực đơn bằng AI
- **Input**: Yêu cầu người dùng, ràng buộc dinh dưỡng
- **Output**: Thực đơn JSON theo format hiện tại

### 2. `/api/ai-menu/sync-foods` (POST)
- **Mục đích**: Đồng bộ dữ liệu food_info lên Pinecone
- **Input**: Danh sách food_id hoặc sync toàn bộ
- **Output**: Trạng thái đồng bộ

### 3. `/api/ai-menu/search-foods` (GET)
- **Mục đích**: Tìm kiếm thực phẩm bằng vector search
- **Input**: Query text, filters
- **Output**: Danh sách thực phẩm phù hợp

## Tích hợp với Hệ thống Hiện tại

### 1. Database Schema
- Không thay đổi schema hiện tại
- Thêm trường `ai_generated: boolean` vào menu JSON
- Thêm trường `ai_requirements: text` để lưu yêu cầu gốc

### 2. Frontend Integration
- Thêm tab "AI Menu Generator" vào menuExample.js
- Button "Tạo thực đơn bằng AI" 
- Form nhập yêu cầu và ràng buộc
- Hiển thị kết quả và cho phép chỉnh sửa

### 3. Backend Integration
- Tích hợp vào foodRationController.js
- Sử dụng cùng format JSON menu hiện tại
- Tương thích với hệ thống tính toán dinh dưỡng

## Prompt Engineering Strategy

### 1. System Prompt
```
Bạn là chuyên gia dinh dưỡng AI. Nhiệm vụ tạo thực đơn cân bằng từ danh sách thực phẩm.
Quy tắc:
- Đảm bảo cân bằng dinh dưỡng
- Tuân thủ ràng buộc y tế
- Đa dạng thực phẩm
- Tính toán chính xác khối lượng
```

### 2. User Prompt Template
```
Yêu cầu: {user_requirements}
Ràng buộc: {restrictions}
Mục tiêu dinh dưỡng: {target_nutrition}
Danh sách thực phẩm khả dụng: {available_foods}

Tạo thực đơn JSON theo format: {menu_format}
```

## Security & Performance

### 1. Security
- API key encryption
- Rate limiting cho AI calls
- Input validation và sanitization
- User permission checks

### 2. Performance
- Cache vector search results
- Batch processing cho sync
- Async processing cho AI calls
- Connection pooling

## Error Handling

### 1. Pinecone Errors
- Connection timeout
- Index not found
- Vector dimension mismatch

### 2. Gemini AI Errors
- API quota exceeded
- Invalid response format
- Content policy violations

### 3. Integration Errors
- Invalid menu structure
- Nutrition calculation errors
- Database sync failures

## Testing Strategy

### 1. Unit Tests
- Vector embedding generation
- AI response parsing
- Nutrition calculations

### 2. Integration Tests
- End-to-end menu generation
- Database synchronization
- API endpoint testing

### 3. User Acceptance Tests
- Various dietary requirements
- Edge cases and error scenarios
- Performance under load
