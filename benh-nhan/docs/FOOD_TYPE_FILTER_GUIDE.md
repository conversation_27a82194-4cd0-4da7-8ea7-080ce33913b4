# Hướng dẫn Hệ thống Filter Thực phẩm theo Lo<PERSON> và Năm

## Tổng quan
Hệ thống đã được cập nhật để hỗ trợ phân loại thực phẩm theo 2 tiêu chí:
- **<PERSON><PERSON><PERSON> thực phẩm**: Sống (raw) hoặc Chín (cooked)
- **Năm dữ liệu**: 2017 hoặc 2024

## Cấu trúc Database

### Bảng `food_info` - C<PERSON>c trường mới
```sql
- type: ENUM('raw', 'cooked') DEFAULT 'raw' 
  -- Lo<PERSON>i thực phẩm: raw=sống, cooked=chín
- type_year: ENUM('2017', '2024') DEFAULT '2017'
  -- Năm dữ liệu thực phẩm
```

### Index được thêm
```sql
- idx_type_year: (type, type_year)
- idx_name_type_year: (name, type, type_year)
```

## Backend Changes

### 1. Service Layer
**File**: `services/foodService.js`
- `getFoodForSelect(type, type_year)`: <PERSON><PERSON><PERSON> danh sách thực phẩm với filter
- `getFoodDetail(id)`: Lấy chi tiết thực phẩm bao gồm type và type_year

### 2. API Endpoints Mới
```javascript
// Public API
GET /khau-phan-an/food-name-filtered?type={type}&type_year={year}&search={search}

// Admin API  
GET /admin/api/food-name-filtered?type={type}&type_year={year}&search={search}
```

### 3. Controller Updates
**AdminController**: 
- Thêm trường `type` và `type_year` vào form thực phẩm
- Cập nhật DataTable hiển thị loại và năm
- Validation cho các trường mới

**DishController**:
- Cập nhật query để bao gồm thông tin type và type_year

## Frontend Changes

### 1. JavaScript Updates
**File**: `public/js/menuExample.js`

#### Function `generateFoodName()`
- Cập nhật để hỗ trợ filter parameters
- Tự động lấy giá trị từ `#food_type` và `#food_year`
- Gửi request với filter parameters

#### Function `updateFoodDropdown()`
- Reset dropdown khi thay đổi filter
- Xóa options hiện tại để load lại với filter mới

### 2. UI Components
Thêm filter controls vào các trang:

#### Trang khẩu phần ăn (`views/khau-phan-an/index.ejs`)
```html
<div class="row g-2 align-items-center mb-3">
    <div class="col-12 col-md-3">
        <label for="food_type">Loại thực phẩm</label>
        <select id="food_type" onchange="updateFoodDropdown('food_name')">
            <option value="">Tất cả</option>
            <option value="raw">Sống</option>
            <option value="cooked">Chín</option>
        </select>
    </div>
    <div class="col-12 col-md-3">
        <label for="food_year">Năm dữ liệu</label>
        <select id="food_year" onchange="updateFoodDropdown('food_name')">
            <option value="">Tất cả</option>
            <option value="2017">2017</option>
            <option value="2024">2024</option>
        </select>
    </div>
</div>
```

#### Trang admin thực đơn mẫu (`views/admin/thuc-don-mau/index.ejs`)
- Tương tự như trên
- Sử dụng `addFoodToMenuAdmin()` thay vì `addFoodToMenu()`

#### Trang admin thực phẩm (`views/admin/thuc-pham/index.ejs`)
- Form tạo/sửa thực phẩm có dropdown type và type_year
- DataTable hiển thị badge cho loại và năm

### 3. DataTable Updates
**File**: `views/admin/thuc-pham/list.ejs`
```javascript
// Hiển thị loại thực phẩm
{ 
    data: 'type',
    render: function(data, type, row) {
        return data === 'raw' ? 
            '<span class="badge badge-info">Sống</span>' : 
            '<span class="badge badge-success">Chín</span>';
    }
},
// Hiển thị năm dữ liệu
{ 
    data: 'type_year',
    render: function(data, type, row) {
        return '<span class="badge badge-secondary">' + data + '</span>';
    }
}
```

## Cách sử dụng

### 1. Quản lý thực phẩm (Admin)
1. Vào `/admin/thuc-pham`
2. Thêm/sửa thực phẩm với các trường:
   - **Loại thực phẩm**: Chọn Sống hoặc Chín
   - **Năm dữ liệu**: Chọn 2017 hoặc 2024
3. Danh sách hiển thị badge màu sắc phân biệt

### 2. Tạo thực đơn với filter
1. Vào trang khẩu phần ăn hoặc admin thực đơn mẫu
2. Chọn **Loại thực phẩm** và **Năm dữ liệu** 
3. Tìm kiếm thực phẩm - kết quả sẽ được filter theo lựa chọn
4. Thêm thực phẩm vào thực đơn như bình thường

### 3. Tạo món ăn với filter
1. Vào `/admin/mon-an`
2. Tạo món ăn mới
3. Sử dụng filter để chọn thực phẩm phù hợp
4. Thêm vào món ăn

## API Response Format

### Food List với Filter
```json
{
    "success": true,
    "data": [
        {
            "value": 1,
            "label": "Thịt gà (Chín - 2024)",
            "customData": {
                "id": 1,
                "name": "Chicken meat",
                "type": "cooked",
                "type_year": "2024",
                "energy": 165,
                "protein": 31.0,
                // ... other nutrients
            }
        }
    ]
}
```

### Food Detail
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Chicken meat",
        "type": "cooked",
        "type_year": "2024",
        "weight": 100,
        "protein": 31.0,
        // ... other fields
    }
}
```

## Performance Optimization

### 1. Database Indexes
- `idx_type_year`: Tối ưu filter theo type và type_year
- `idx_name_type_year`: Tối ưu search kết hợp với filter

### 2. Frontend Caching
- VirtualSelect cache kết quả search
- Reset cache khi thay đổi filter

### 3. API Optimization
- Sử dụng prepared statements
- Limit kết quả search (mặc định 50)

## Troubleshooting

### 1. Filter không hoạt động
**Nguyên nhân**: JavaScript error hoặc API không nhận parameters
**Giải pháp**: 
- Kiểm tra console browser
- Verify API endpoint nhận đúng parameters
- Đảm bảo `updateFoodDropdown()` được gọi

### 2. Dropdown không load dữ liệu
**Nguyên nhân**: VirtualSelect không reset đúng cách
**Giải pháp**:
```javascript
// Reset VirtualSelect
const element = document.querySelector('#food_name');
if (element && element.virtualSelect) {
    element.virtualSelect.reset();
    element.virtualSelect.setServerOptions([]);
}
```

### 3. Badge không hiển thị đúng
**Nguyên nhân**: DataTable render function lỗi
**Giải pháp**: Kiểm tra data type và giá trị null

## Migration Guide

### Cập nhật dữ liệu hiện tại
```sql
-- Cập nhật thực phẩm hiện tại thành "sống" và "2017"
UPDATE food_info SET 
    type = 'raw',
    type_year = '2017' 
WHERE type IS NULL OR type_year IS NULL;

-- Hoặc cập nhật theo logic nghiệp vụ
UPDATE food_info SET 
    type = 'cooked',
    type_year = '2024'
WHERE name LIKE '%nấu%' OR name LIKE '%chín%';
```

### Backup trước khi migrate
```sql
-- Backup bảng food_info
CREATE TABLE food_info_backup AS SELECT * FROM food_info;
```

## Testing

### 1. Unit Tests
- Test `foodService.getFoodForSelect()` với các filter khác nhau
- Test API endpoints với parameters

### 2. Integration Tests  
- Test UI filter interaction
- Test VirtualSelect reset/reload
- Test DataTable hiển thị

### 3. Performance Tests
- Test với large dataset
- Test search performance với filter
- Test index effectiveness

## Future Enhancements

### 1. Thêm filter nâng cao
- Filter theo nhóm thực phẩm
- Filter theo mức năng lượng
- Filter theo hàm lượng protein

### 2. Bulk operations
- Import thực phẩm với type và type_year
- Bulk update type cho nhiều thực phẩm

### 3. Analytics
- Thống kê sử dụng theo type
- Report theo năm dữ liệu
- Trend analysis 