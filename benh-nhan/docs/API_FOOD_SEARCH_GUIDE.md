# API Tìm Kiếm Thực Phẩm - Hướng Dẫn Sử Dụng

## Tổng Quan

API `/api/food-search` là API chung được sử dụng để tìm kiếm thực phẩm với các filter và search. API này thay thế 2 API cũ:
- `/admin/api/food-name-filtered` (cho admin)
- `/khau-phan-an/food-name-filtered` (cho user)

## Endpoint

```
GET /api/food-search
```

## Tham Số (Query Parameters)

| Tham số | Kiểu | Bắt buộc | Mô tả | Ví dụ |
|---------|------|----------|-------|-------|
| `type` | string | Không | Loại thực phẩm: `raw` (sống) hoặc `cooked` (chín) | `type=raw` |
| `type_year` | string | Không | Năm dữ liệu: `2017` hoặc `2024` | `type_year=2024` |
| `search` | string | Không | Từ khóa tìm kiếm (tối thiểu 2 ký tự) | `search=thịt` |

## Ví Dụ Sử Dụng

### 1. Tìm kiếm cơ bản
```javascript
// Tìm tất cả thực phẩm có chứa từ "thịt"
fetch('/api/food-search?search=thịt')
```

### 2. Filter theo loại thực phẩm
```javascript
// Chỉ tìm thực phẩm sống
fetch('/api/food-search?type=raw&search=thịt')
```

### 3. Filter theo năm dữ liệu
```javascript
// Chỉ tìm dữ liệu năm 2024
fetch('/api/food-search?type_year=2024&search=thịt')
```

### 4. Kết hợp tất cả filter
```javascript
// Tìm thực phẩm chín, dữ liệu 2024, có chứa từ "thịt"
fetch('/api/food-search?type=cooked&type_year=2024&search=thịt')
```

## Response Format

### Cấu Trúc Dữ Liệu Đầy Đủ

API trả về toàn bộ thông tin từ cả hai bảng:

**Từ bảng `food_info` (60+ trường):**
- Thông tin cơ bản: `id`, `code`, `name`, `ten`, `type`, `type_year`, `weight`
- Protein và amino acid: `protein`, `animal_protein`, `lysin`, `methionin`, `tryptophan`, etc.
- Lipid: `unanimal_lipid`, `lignoceric`, etc.
- Đường: `total_sugar`, `galactose`, `maltose`, `lactose`, `fructose`, `glucose`, `sucrose`
- Vitamin: `vitamin_a_rae`, `vitamin_b1`, `vitamin_b2`, `vitamin_b6`, `vitamin_b12`, `vitamin_c`, `vitamin_e`, `vitamin_k`, `vitamin_pp`
- Carotenoid: `caroten`, `b_carotene`, `a_carotene`, `b_cryptoxanthin`, `lycopene`, `lutein_zeaxanthin`
- Isoflavone: `total_isoflavone`, `daidzein`, `genistein`, `glycetin`
- Khác: `phytosterol`, `purine`, `retinol`, `riboflavin`, `thiamine`, `niacin`, `pantothenic_acid`, `folate`, `folic_acid`, `biotin`

**Từ bảng `main_nutrients` (40+ trường):**
- Thông tin cơ bản: `edible`, `energy`, `water`, `protein`, `fat`, `carbohydrate`, `fiber`, `ash`
- Khoáng chất: `calci`, `phosphorous`, `fe`, `zinc`, `sodium`, `potassium`, `magnesium`, `manganese`, `copper`, `selenium`
- Lipid chi tiết: `total_fat`, `total_saturated_fat`, `palmitic`, `margaric`, `stearic`, `arachidic`, `behenic`, `lignoceric`
- Acid béo không bão hòa: `mufa`, `myristoleic`, `palmitoleic`, `oleic`, `fufa`, `linoleic`, `linolenic`, `arachidonic`, `dha`
- Khác: `trans_fatty_acids`, `cholesterol`

### Response JSON

```json
{
    "success": true,
    "message": "",
    "data": [
        {
            "value": 123,
            "label": "Thịt bò - Beef (Chín - 2024)",
            "customData": {
                // Thông tin từ food_info
                "id": 123,
                "code": "TB001",
                "name": "Thịt bò",
                "type": "cooked",
                "type_year": "2024",
                "ten": "Beef",
                "weight": 100,
                "protein": 26.1,
                "animal_protein": 26.1,
                "unanimal_lipid": 0,
                "total_sugar": "0",
                "galactose": "0",
                "maltose": "0",
                "lactose": "0",
                "fructose": "0",
                "glucose": "0",
                "sucrose": "0",
                "lycopene": "0",
                "lutein_zeaxanthin": "0",
                "total_isoflavone": "0",
                "daidzein": "0",
                "genistein": "0",
                "glycetin": "0",
                "phytosterol": "0",
                "purine": "146",
                // ... tất cả các trường khác từ food_info
                
                // Thông tin từ main_nutrients
                "mn_id": 456,
                "id_food": 123,
                "edible": 100,
                "energy": 250,
                "water": "56.2",
                "mn_protein": "26.1",
                "fat": 15.0,
                "carbohydrate": 0.0,
                "fiber": "0",
                "ash": "1.2",
                "calci": 18,
                "phosphorous": 200,
                "fe": 2.6,
                "zinc": 4.8,
                "sodium": 60,
                "potassium": 318,
                "magnesium": 21,
                "manganese": "0.01",
                "copper": 0.1,
                "selenium": 14.2,
                "total_fat": "15.0",
                "total_saturated_fat": "6.2",
                "palmitic": "4.8",
                "margaric": "0.3",
                "stearic": "1.1",
                "arachidic": "0.0",
                "behenic": "0.0",
                "mn_lignoceric": "0.0",
                "mufa": "6.8",
                "myristoleic": "0.3",
                "palmitoleic": "0.4",
                "oleic": "6.1",
                "fufa": "0.6",
                "linoleic": "0.4",
                "linolenic": "0.1",
                "arachidonic": "0.1",
                "dha": "0.0",
                "trans_fatty_acids": "0.0",
                "cholesterol": "70"
            }
        }
    ]
}
```

## Tính Năng Chính

### 1. Tìm Kiếm Thông Minh
- Tìm kiếm trong các trường: `name`, `ten`, `code`
- Sử dụng LIKE pattern với wildcards
- Không phân biệt hoa thường

### 2. Filter Linh Hoạt
- Filter theo loại thực phẩm (sống/chín)
- Filter theo năm dữ liệu (2017/2024)
- Có thể kết hợp nhiều filter

### 3. Giới Hạn Kết Quả
- Tối đa 100 kết quả mỗi lần tìm kiếm
- Sắp xếp theo tên thực phẩm (A-Z)

### 4. Thông Tin Đầy Đủ
- Trả về **TẤT CẢ** thông tin từ bảng `food_info` và `main_nutrients`
- Bao gồm toàn bộ 60+ trường dữ liệu dinh dưỡng
- Label hiển thị tên tiếng Việt và tiếng Anh với phân loại
- Dữ liệu đủ để lưu vào thực đơn mà không cần gọi API khác

## Cách Sử Dụng Trong Frontend

### 1. Trong Virtual Select
```javascript
function generateFoodName(selectId) {
    const typeSelect = document.querySelector('#food_type');
    const yearSelect = document.querySelector('#food_year');
    
    let apiUrl = '/api/food-search';
    let params = [];
    
    if (typeSelect && typeSelect.value) {
        params.push(`type=${typeSelect.value}`);
    }
    if (yearSelect && yearSelect.value) {
        params.push(`type_year=${yearSelect.value}`);
    }
    
    if (params.length > 0) {
        apiUrl += '?' + params.join('&');
    }
    
    VirtualSelect.init({
        ele: `#${selectId}`,
        options: [],
        search: true,
        searchByStartsWith: false,
        searchPlaceholderText: 'Tìm kiếm thực phẩm...',
        noOptionsText: 'Không có thực phẩm nào',
        noSearchResultsText: 'Không tìm thấy thực phẩm',
        searchGroup: false,
        maxWidth: '100%',
        zIndex: 10,
        popupDropboxBreakpoint: '3000px',
        hasOptionDescription: false,
        optionHeight: '40px',
        showValueAsTags: false,
        search: true,
        searchByStartsWith: false,
        onServerSearch: function(search, virtualSelect) {
            if (search && search.length >= 2) {
                let searchUrl = apiUrl;
                const separator = apiUrl.includes('?') ? '&' : '?';
                searchUrl += `${separator}search=${encodeURIComponent(search)}`;
                
                fetch(searchUrl)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            virtualSelect.setServerOptions(data.data);
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        }
    });
}
```

### 2. Cập nhật khi thay đổi filter
```javascript
function updateFoodDropdown(selectId) {
    const virtualSelectElement = document.querySelector('#' + selectId);
    if (virtualSelectElement && virtualSelectElement.virtualSelect) {
        virtualSelectElement.virtualSelect.reset();
        virtualSelectElement.virtualSelect.setServerOptions([]);
        generateFoodName(selectId);
    }
}

// Event listeners cho filter
document.getElementById('food_type').addEventListener('change', function() {
    updateFoodDropdown('food_name');
});

document.getElementById('food_year').addEventListener('change', function() {
    updateFoodDropdown('food_name');
});
```

## Migration từ API cũ

### Thay đổi URL
```javascript
// Cũ - Admin
'/admin/api/food-name-filtered'

// Cũ - User  
'/khau-phan-an/food-name-filtered'

// Mới - Chung
'/api/food-search'
```

### Thêm tham số search
```javascript
// Cũ
const apiUrl = `/admin/api/food-name-filtered?type=${type}&type_year=${type_year}`;

// Mới
const apiUrl = `/api/food-search?type=${type}&type_year=${type_year}&search=${search}`;
```

## Performance

### 1. Database Optimization
- Sử dụng index trên các trường tìm kiếm
- LIMIT 100 để tránh query quá chậm
- LEFT JOIN để lấy thông tin dinh dưỡng

### 2. Frontend Optimization
- Chỉ tìm kiếm khi có ít nhất 2 ký tự
- Debouncing để tránh gọi API liên tục
- Cache kết quả tìm kiếm

## Troubleshooting

### 1. Không có kết quả tìm kiếm
- Kiểm tra từ khóa tìm kiếm (tối thiểu 2 ký tự)
- Thử bỏ filter type và type_year
- Kiểm tra database có dữ liệu không

### 2. API trả về lỗi 500
- Kiểm tra kết nối database
- Xem log server để biết chi tiết lỗi
- Kiểm tra syntax SQL query

### 3. Virtual Select không hiển thị options
- Kiểm tra API response format
- Đảm bảo `data.success` là true
- Kiểm tra console log có lỗi JavaScript không

## Bảo Mật

- API yêu cầu authentication (`commonService.isAuthenticated`)
- Validate và sanitize tất cả input parameters
- Sử dụng prepared statements để tránh SQL injection
- Rate limiting để tránh spam requests 