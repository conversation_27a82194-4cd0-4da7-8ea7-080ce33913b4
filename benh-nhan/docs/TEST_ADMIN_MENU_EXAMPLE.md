# Hướng dẫn Test Chức năng Admin Thực đơn mẫu

## Đã hoàn thiện:

### 1. **Cấu trúc dữ liệu đã được đơn giản hóa**
- ✅ Bỏ `currentMenuExample`, chỉ sử dụng `menuExamine`
- ✅ `menuExamine[0]` chứa thông tin thực đơn hiện tại
- ✅ Thêm field `isExisting` để phân biệt thực đơn mới vs có sẵn

### 2. **Controller đã được cập nhật**
- ✅ `menuExampleDetail()` xử lý cả case edit và tạo mới
- ✅ Parse JSON detail từ database
- ✅ Tạo cấu trúc thực đơn trống cho case mới

### 3. **Frontend đã được cập nhật**
- ✅ Sử dụng `menuExamine[0]` thay vì `currentMenuExample`
- ✅ Hiển thị header động dựa trên `isExisting`
- ✅ JavaScript functions đã được cập nhật

## Test Cases:

### Test 1: Tạ<PERSON> mới thực đơn mẫu
```
URL: /admin/thuc-don-mau/new
Kết quả mong đợi:
- Header hiển thị "Thực đọn mẫu mới"
- Input tên thực đơn có value "Thực đơn mới"
- Bảng thực đơn hiển thị với các giờ ăn trống
- Nút "Lưu" hoạt động
- Sau khi lưu redirect đến trang edit với ID mới
```

### Test 2: Xem chi tiết thực đơn có sẵn
```
URL: /admin/thuc-don-mau/{id}
Kết quả mong đợi:
- Header hiển thị "Chi tiết: {tên thực đơn}"
- Input tên thực đơn có value từ database
- Bảng thực đơn hiển thị dữ liệu từ JSON detail
- Nút "Cập nhật" và "Lưu" hoạt động
```

### Test 3: Thêm thực phẩm vào thực đơn
```
Các bước:
1. Chọn giờ ăn
2. Chọn thực phẩm
3. Nhập khối lượng
4. Click "Thêm"

Kết quả mong đợi:
- Thực phẩm được thêm vào bảng
- Tính toán dinh dưỡng tự động
- Cập nhật tổng cộng
```

### Test 4: Lưu thực đơn
```
Thực đơn mới:
- Tạo record mới trong bảng menu_example
- Redirect đến trang edit

Thực đơn có sẵn:
- Cập nhật record hiện có
- Hiển thị thông báo thành công
```

### Test 5: Cập nhật tên thực đơn
```
Các bước:
1. Click nút "Cập nhật" (biểu tượng edit)
2. Nhập tên mới trong SweetAlert
3. Click "Cập nhật"

Kết quả mong đợi:
- Header cập nhật tên mới
- Input tên thực đơn cập nhật
- Hiển thị thông báo "Nhấn Lưu để hoàn tất"
```

## Cấu trúc dữ liệu `menuExamine[0]`:

```json
{
  "id": 123, // hoặc "new" cho thực đơn mới
  "name": "Tên thực đơn",
  "isExisting": true, // false cho thực đơn mới
  "note": "",
  "detail": [
    {
      "id": 1,
      "name": "6h - 6h30",
      "name_course": "",
      "listFood": [
        {
          "id": 1,
          "id_food": 37,
          "name": "Bún",
          "weight": 200,
          "energy": 220,
          "protein": 3.4,
          "animal_protein": 0,
          "lipid": 0,
          "unanimal_lipid": 0,
          "carbohydrate": 51.4
        }
      ]
    }
  ]
}
```

## URL Structure:
- **Danh sách**: `/admin/thuc-don-mau`
- **Tạo mới**: `/admin/thuc-don-mau/new`
- **Chi tiết**: `/admin/thuc-don-mau/{id}`
- **API lưu**: `POST /admin/thuc-don-mau/upsert/`
- **API xóa**: `POST /admin/thuc-don-mau/delete/{id}`

## JavaScript Functions:
- `initAdminMenuExample()` - Khởi tạo thực đơn admin
- `saveMenuExample()` - Lưu thực đơn admin
- `showUpdateMenuNameModal()` - Cập nhật tên admin
- `addFoodToMenu()` - Thêm thực phẩm (tự động detect admin/patient)
- `addFoodToMenuAdmin()` - Thêm thực phẩm admin (internal)
- `createNewMenuExample()` - Tạo thực đơn trống admin

## Files đã thay đổi:
1. `controllers/adminController.js` - Logic xử lý
2. `views/admin/thuc-don-mau/index.ejs` - Giao diện  
3. `public/js/menuExample.js` - JavaScript functions (đã gộp admin functions)
4. Đã bỏ `public/js/admin-menu-example.js` - Không cần thiết nữa
5. Đã bỏ dependency vào `currentMenuExample`

## Cách hoạt động:
- **Patient pages**: Sử dụng functions gốc như `addFoodToMenu()`, `saveMenu()`
- **Admin pages**: Auto-detect dựa trên URL và chuyển sang logic admin
- **Logic detection**: `window.location.pathname.includes('/admin/thuc-don-mau/')`
- **Single JavaScript file**: Tất cả logic trong `menuExample.js` 