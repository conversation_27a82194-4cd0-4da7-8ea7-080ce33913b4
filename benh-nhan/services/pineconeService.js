const { Pinecone } = require('@pinecone-database/pinecone');
const commonService = require('./commonService');
require('dotenv').config();

class PineconeService {
    constructor() {
        this.pinecone = null;
        this.index = null;
        this.indexName = process.env.PINECONE_INDEX_NAME || 'food-nutrition-index';
        this.initialized = false;
    }

    async initialize() {
        try {
            if (this.initialized) return true;

            this.pinecone = new Pinecone({
                apiKey: process.env.PINECONE_API_KEY,
            });

            // Kiểm tra và tạo index nếu chưa có
            await this.ensureIndexExists();
            
            this.index = this.pinecone.index(this.indexName);
            this.initialized = true;
            
            console.log('Pinecone service initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize Pinecone service:', error);
            return false;
        }
    }

    async ensureIndexExists() {
        try {
            const indexList = await this.pinecone.listIndexes();
            const indexExists = indexList.indexes?.some(index => index.name === this.indexName);

            if (!indexExists) {
                console.log(`Creating Pinecone index: ${this.indexName}`);
                await this.pinecone.createIndex({
                    name: this.indexName,
                    dimension: 1024, // Dimension for llama-text-embed-v2
                    metric: 'cosine',
                    spec: {
                        serverless: {
                            cloud: 'aws',
                            region: 'us-east-1'
                        }
                    }
                });
                
                // Đợi index được tạo hoàn toàn
                await this.waitForIndexReady();
            }
        } catch (error) {
            console.error('Error ensuring index exists:', error);
            throw error;
        }
    }

    async waitForIndexReady() {
        const maxAttempts = 30;
        let attempts = 0;
        
        while (attempts < maxAttempts) {
            try {
                const indexStats = await this.pinecone.describeIndex(this.indexName);
                if (indexStats.status?.ready) {
                    console.log('Index is ready');
                    return;
                }
                
                console.log('Waiting for index to be ready...');
                await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
                attempts++;
            } catch (error) {
                console.error('Error checking index status:', error);
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
        
        throw new Error('Index did not become ready within expected time');
    }

    // Tạo embedding text sử dụng một service đơn giản nhưng hiệu quả hơn
    // Trong thực tế, bạn có thể sử dụng OpenAI API hoặc model khác
    generateSimpleEmbedding(text) {
        // Tạo embedding 1024 dimensions phù hợp với Pinecone index
        const embedding = new Array(1024).fill(0);

        // Xử lý text
        const cleanText = text.toLowerCase().replace(/[^\w\s]/g, ' ');
        const words = cleanText.split(/\s+/).filter(word => word.length > 0);

        // Tạo embedding dựa trên nhiều yếu tố
        words.forEach((word, wordIndex) => {
            // Hash chính của từ
            const mainHash = this.simpleHash(word);

            // Phân bổ giá trị vào nhiều vị trí để tăng độ phong phú
            for (let i = 0; i < 3; i++) {
                const position = (mainHash + i * 341) % 1024; // 341 là số nguyên tố
                const weight = 1.0 / (wordIndex + 1) / (i + 1); // Giảm trọng số theo vị trí từ và iteration
                embedding[position] += weight;
            }

            // Thêm thông tin về độ dài từ
            const lengthPos = (word.length * 127) % 1024; // 127 là số nguyên tố
            embedding[lengthPos] += 0.1;

            // Thêm thông tin về ký tự đầu và cuối
            if (word.length > 0) {
                const firstCharPos = (word.charCodeAt(0) * 17) % 1024;
                const lastCharPos = (word.charCodeAt(word.length - 1) * 19) % 1024;
                embedding[firstCharPos] += 0.05;
                embedding[lastCharPos] += 0.05;
            }
        });

        // Thêm thông tin về tổng số từ
        const wordCountPos = (words.length * 23) % 1024;
        embedding[wordCountPos] += 0.2;

        // Normalize vector để có magnitude = 1
        const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
        if (magnitude > 0) {
            return embedding.map(val => val / magnitude);
        } else {
            // Fallback: tạo vector random nhỏ nếu không có text
            return embedding.map(() => (Math.random() - 0.5) * 0.001);
        }
    }

    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    // Tạo text mô tả chi tiết cho thực phẩm để embedding
    createFoodDescription(food) {
        const parts = [];

        // Thêm tên thực phẩm với các biến thể
        if (food.name) {
            parts.push(food.name);
            // Thêm từ khóa liên quan đến tên
            parts.push(`thực phẩm ${food.name.toLowerCase()}`);
        }
        if (food.ten && food.ten !== food.name) {
            parts.push(food.ten);
        }

        // Thêm thông tin loại chi tiết
        if (food.type) {
            const typeText = this.getTypeDescription(food.type);
            parts.push(typeText);
            parts.push(`loại ${food.type}`);

            // Thêm từ khóa theo loại
            if (food.type === 'raw') parts.push('tươi sống nguyên liệu');
            if (food.type === 'cooked') parts.push('chín nấu chế biến');
            if (food.type === 'milk') parts.push('sữa dairy lactose');
        }

        // Thêm năm dữ liệu
        if (food.type_year) {
            parts.push(`dữ liệu năm ${food.type_year}`);
        }

        // Phân loại theo năng lượng
        const energy = parseFloat(food.energy) || 0;
        if (energy > 0) {
            parts.push(`năng lượng ${energy} kcal`);
            if (energy < 50) parts.push('ít calo low calorie');
            else if (energy < 150) parts.push('calo trung bình moderate calorie');
            else if (energy < 300) parts.push('calo cao high calorie');
            else parts.push('rất nhiều calo very high calorie');
        }

        // Phân loại theo protein
        const protein = parseFloat(food.protein) || 0;
        if (protein > 0) {
            parts.push(`protein ${protein}g`);
            if (protein < 5) parts.push('ít protein low protein');
            else if (protein < 15) parts.push('protein trung bình moderate protein');
            else if (protein < 25) parts.push('nhiều protein high protein');
            else parts.push('rất nhiều protein very high protein');
        }

        // Phân loại theo carbohydrate
        const carb = parseFloat(food.carbohydrate) || 0;
        if (carb > 0) {
            parts.push(`carbohydrate ${carb}g`);
            if (carb < 10) parts.push('ít carb low carb');
            else if (carb < 30) parts.push('carb trung bình moderate carb');
            else parts.push('nhiều carb high carb');
        }

        // Phân loại theo fat
        const fat = parseFloat(food.fat) || 0;
        if (fat > 0) {
            parts.push(`fat chất béo ${fat}g`);
            if (fat < 3) parts.push('ít béo low fat');
            else if (fat < 10) parts.push('béo trung bình moderate fat');
            else parts.push('nhiều béo high fat');
        }

        // Thông tin chất xơ
        const fiber = parseFloat(food.fiber) || 0;
        if (fiber > 0) {
            parts.push(`chất xơ fiber ${fiber}g`);
            if (fiber > 5) parts.push('nhiều chất xơ high fiber');
        }

        // Vitamin và khoáng chất quan trọng
        const micronutrients = [];
        if (food.vitamin_c && parseFloat(food.vitamin_c) > 0) {
            micronutrients.push(`vitamin C ${food.vitamin_c}mg`);
            if (parseFloat(food.vitamin_c) > 50) parts.push('giàu vitamin C');
        }
        if (food.calci && parseFloat(food.calci) > 0) {
            micronutrients.push(`canxi calcium ${food.calci}mg`);
            if (parseFloat(food.calci) > 100) parts.push('giàu canxi');
        }
        if (food.fe && parseFloat(food.fe) > 0) {
            micronutrients.push(`sắt iron ${food.fe}mg`);
            if (parseFloat(food.fe) > 5) parts.push('giàu sắt');
        }
        if (food.potassium && parseFloat(food.potassium) > 0) {
            micronutrients.push(`kali potassium ${food.potassium}mg`);
        }

        if (micronutrients.length > 0) {
            parts.push(micronutrients.join(' '));
        }

        // Thêm từ khóa sức khỏe dựa trên thành phần
        const healthKeywords = [];
        if (protein > 15) healthKeywords.push('tăng cơ muscle building');
        if (fiber > 3) healthKeywords.push('tiêu hóa digestion');
        if (energy < 100 && protein > 10) healthKeywords.push('giảm cân weight loss');
        if (carb < 10) healthKeywords.push('keto low carb diet');
        if (fat < 3) healthKeywords.push('ít béo diet food');

        if (healthKeywords.length > 0) {
            parts.push(healthKeywords.join(' '));
        }

        return parts.join(' ');
    }

    getTypeDescription(type) {
        const typeMap = {
            'raw': 'thực phẩm sống',
            'cooked': 'thực phẩm chín',
            'cooked_vdd': 'thực phẩm chín viện dinh dưỡng',
            'milk': 'sản phẩm sữa',
            'ddd': 'dịch dinh dưỡng'
        };
        return typeMap[type] || type;
    }

    // Tạo text mô tả chi tiết cho món ăn để embedding
    createDishDescription(dish) {
        const parts = [];

        // Thêm tên món ăn
        if (dish.name) {
            parts.push(dish.name);
            parts.push(`món ăn ${dish.name.toLowerCase()}`);
        }

        // Thêm mô tả
        if (dish.description) {
            parts.push(dish.description);
        }

        // Thêm loại món ăn
        if (dish.category) {
            parts.push(dish.category);
            parts.push(`món ${dish.category.toLowerCase()}`);
        }

        // Thêm thông tin dinh dưỡng tổng hợp
        if (dish.total_energy) {
            parts.push(`${dish.total_energy} kcal tổng năng lượng`);
            if (dish.total_energy > 500) parts.push('món giàu năng lượng');
            else if (dish.total_energy < 200) parts.push('món ít năng lượng');
        }

        if (dish.total_protein) {
            parts.push(`${dish.total_protein}g protein tổng`);
            if (dish.total_protein > 20) parts.push('món giàu protein');
        }

        if (dish.total_carbohydrate) {
            parts.push(`${dish.total_carbohydrate}g carbohydrate tổng`);
            if (dish.total_carbohydrate > 50) parts.push('món giàu tinh bột');
        }

        if (dish.total_fat) {
            parts.push(`${dish.total_fat}g chất béo tổng`);
            if (dish.total_fat > 15) parts.push('món giàu chất béo');
        }

        if (dish.total_fiber) {
            parts.push(`${dish.total_fiber}g chất xơ tổng`);
            if (dish.total_fiber > 8) parts.push('món giàu chất xơ');
        }

        // Thêm thông tin về thực phẩm thành phần
        if (dish.ingredients && Array.isArray(dish.ingredients)) {
            const ingredientNames = dish.ingredients.map(ing => ing.name).join(' ');
            parts.push(`chứa ${ingredientNames}`);

            // Phân tích thành phần
            const hasVegetables = dish.ingredients.some(ing =>
                ing.type && (ing.type.includes('vegetable') || ing.type.includes('rau'))
            );
            const hasMeat = dish.ingredients.some(ing =>
                ing.type && (ing.type.includes('meat') || ing.type.includes('thịt'))
            );
            const hasRice = dish.ingredients.some(ing =>
                ing.name && ing.name.toLowerCase().includes('cơm')
            );

            if (hasVegetables) parts.push('có rau xanh');
            if (hasMeat) parts.push('có thịt');
            if (hasRice) parts.push('có cơm');
        }

        return parts.join(' ');
    }

    // Upload một thực phẩm lên Pinecone
    async upsertFood(food) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const description = this.createFoodDescription(food);
            const embedding = this.generateSimpleEmbedding(description);

            const vector = {
                id: `food_${food.id}`,
                values: embedding,
                metadata: {
                    type: 'food', // Phân biệt với dish
                    food_id: food.id,
                    name: food.name,
                    ten: food.ten || '',
                    food_type: food.type,
                    type_year: food.type_year,
                    energy: parseFloat(food.energy) || 0,
                    protein: parseFloat(food.protein) || 0,
                    carbohydrate: parseFloat(food.carbohydrate) || 0,
                    fat: parseFloat(food.fat) || 0,
                    fiber: parseFloat(food.fiber) || 0,
                    description: description
                }
            };

            await this.index.upsert([vector]);
            return { success: true, message: 'Food uploaded successfully' };
        } catch (error) {
            console.error('Error upserting food:', error);
            return { success: false, message: error.message };
        }
    }

    // Upload một món ăn lên Pinecone
    async upsertDish(dish) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const description = this.createDishDescription(dish);
            const embedding = this.generateSimpleEmbedding(description);

            const vector = {
                id: `dish_${dish.id}`,
                values: embedding,
                metadata: {
                    type: 'dish', // Phân biệt với food
                    dish_id: dish.id,
                    name: dish.name,
                    description: dish.description || '',
                    category: dish.category || '',
                    total_energy: parseFloat(dish.total_energy) || 0,
                    total_protein: parseFloat(dish.total_protein) || 0,
                    total_carbohydrate: parseFloat(dish.total_carbohydrate) || 0,
                    total_fat: parseFloat(dish.total_fat) || 0,
                    total_fiber: parseFloat(dish.total_fiber) || 0,
                    description_text: description
                }
            };

            await this.index.upsert([vector]);
            return { success: true, message: 'Dish uploaded successfully' };
        } catch (error) {
            console.error('Error upserting dish:', error);
            return { success: false, message: error.message };
        }
    }

    // Upload nhiều thực phẩm cùng lúc
    async upsertFoods(foods) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const vectors = foods.map(food => {
                const description = this.createFoodDescription(food);
                const embedding = this.generateSimpleEmbedding(description);

                return {
                    id: `food_${food.id}`,
                    values: embedding,
                    metadata: {
                        type: 'food', // Phân biệt với dish
                        food_id: food.id,
                        name: food.name,
                        ten: food.ten || '',
                        food_type: food.type,
                        type_year: food.type_year,
                        energy: parseFloat(food.energy) || 0,
                        protein: parseFloat(food.protein) || 0,
                        carbohydrate: parseFloat(food.carbohydrate) || 0,
                        fat: parseFloat(food.fat) || 0,
                        fiber: parseFloat(food.fiber) || 0,
                        description: description
                    }
                };
            });

            // Upload theo batch để tránh timeout
            const batchSize = 100;
            for (let i = 0; i < vectors.length; i += batchSize) {
                const batch = vectors.slice(i, i + batchSize);
                await this.index.upsert(batch);
                console.log(`Uploaded batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(vectors.length/batchSize)}`);
            }

            return { success: true, message: `${vectors.length} foods uploaded successfully` };
        } catch (error) {
            console.error('Error upserting foods:', error);
            return { success: false, message: error.message };
        }
    }

    // Tìm kiếm thực phẩm bằng vector search
    async searchFoods(query, options = {}) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const {
                topK = 10,
                filter = {},
                includeMetadata = true
            } = options;

            const queryEmbedding = this.generateSimpleEmbedding(query);
            
            const searchRequest = {
                vector: queryEmbedding,
                topK: topK,
                includeMetadata: includeMetadata
            };

            // Chỉ tìm kiếm thực phẩm (không phải món ăn)
            const foodFilter = { type: { $eq: 'food' } };
            if (Object.keys(filter).length > 0) {
                searchRequest.filter = { $and: [foodFilter, filter] };
            } else {
                searchRequest.filter = foodFilter;
            }

            const results = await this.index.query(searchRequest);
            
            return {
                success: true,
                data: results.matches.map(match => ({
                    food_id: match.metadata.food_id,
                    name: match.metadata.name,
                    ten: match.metadata.ten,
                    type: match.metadata.type,
                    type_year: match.metadata.type_year,
                    energy: match.metadata.energy,
                    protein: match.metadata.protein,
                    carbohydrate: match.metadata.carbohydrate,
                    fat: match.metadata.fat,
                    fiber: match.metadata.fiber,
                    score: match.score,
                    description: match.metadata.description
                }))
            };
        } catch (error) {
            console.error('Error searching foods:', error);
            return { success: false, message: error.message, data: [] };
        }
    }

    // Upload nhiều món ăn cùng lúc
    async upsertDishes(dishes) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const vectors = dishes.map(dish => {
                const description = this.createDishDescription(dish);
                const embedding = this.generateSimpleEmbedding(description);

                return {
                    id: `dish_${dish.id}`,
                    values: embedding,
                    metadata: {
                        type: 'dish',
                        dish_id: dish.id,
                        name: dish.name,
                        description: dish.description || '',
                        category: dish.category || '',
                        total_energy: parseFloat(dish.total_energy) || 0,
                        total_protein: parseFloat(dish.total_protein) || 0,
                        total_carbohydrate: parseFloat(dish.total_carbohydrate) || 0,
                        total_fat: parseFloat(dish.total_fat) || 0,
                        total_fiber: parseFloat(dish.total_fiber) || 0,
                        description_text: description
                    }
                };
            });

            await this.index.upsert(vectors);
            return { success: true, message: `${dishes.length} dishes uploaded successfully` };
        } catch (error) {
            console.error('Error upserting dishes:', error);
            return { success: false, message: error.message };
        }
    }

    // Tìm kiếm món ăn bằng vector search
    async searchDishes(query, options = {}) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const {
                topK = 10,
                filter = {},
                includeMetadata = true
            } = options;

            const queryEmbedding = this.generateSimpleEmbedding(query);

            const searchRequest = {
                vector: queryEmbedding,
                topK: topK,
                includeMetadata: includeMetadata
            };

            // Chỉ tìm kiếm món ăn (không phải thực phẩm)
            const dishFilter = { type: { $eq: 'dish' } };
            if (Object.keys(filter).length > 0) {
                searchRequest.filter = { $and: [dishFilter, filter] };
            } else {
                searchRequest.filter = dishFilter;
            }

            const results = await this.index.query(searchRequest);

            return {
                success: true,
                data: results.matches.map(match => ({
                    type: 'dish', // Thêm type để phân biệt với food
                    dish_id: match.metadata.dish_id,
                    name: match.metadata.name,
                    description: match.metadata.description,
                    category: match.metadata.category,
                    total_energy: match.metadata.total_energy,
                    total_protein: match.metadata.total_protein,
                    total_carbohydrate: match.metadata.total_carbohydrate,
                    total_fat: match.metadata.total_fat,
                    total_fiber: match.metadata.total_fiber,
                    score: match.score,
                    description_text: match.metadata.description_text
                }))
            };
        } catch (error) {
            console.error('Error searching dishes:', error);
            return { success: false, message: error.message, data: [] };
        }
    }

    // Tìm kiếm cả thực phẩm và món ăn
    async searchFoodsAndDishes(query, options = {}) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const {
                topK = 20,
                filter = {},
                includeMetadata = true,
                foodLimit = 10,
                dishLimit = 10
            } = options;

            // Tìm kiếm thực phẩm
            const foodResults = await this.searchFoods(query, {
                topK: foodLimit,
                filter,
                includeMetadata
            });

            // Tìm kiếm món ăn
            const dishResults = await this.searchDishes(query, {
                topK: dishLimit,
                filter,
                includeMetadata
            });

            const combinedResults = [];

            if (foodResults.success && foodResults.data) {
                combinedResults.push(...foodResults.data);
            }

            if (dishResults.success && dishResults.data) {
                combinedResults.push(...dishResults.data);
            }

            // Sắp xếp theo score
            combinedResults.sort((a, b) => b.score - a.score);

            return {
                success: true,
                data: combinedResults.slice(0, topK),
                metadata: {
                    foods_found: foodResults.success ? foodResults.data.length : 0,
                    dishes_found: dishResults.success ? dishResults.data.length : 0
                }
            };
        } catch (error) {
            console.error('Error searching foods and dishes:', error);
            return { success: false, message: error.message };
        }
    }

    // Đồng bộ tất cả dữ liệu food_info lên Pinecone
    async syncAllFoods() {
        try {
            console.log('Starting food sync to Pinecone...');
            
            // Lấy tất cả thực phẩm từ database
            const sql = 'SELECT * FROM food_info WHERE active = 1 ORDER BY id';
            const result = await commonService.getListTable(sql, []);
            
            if (!result.success || !result.data) {
                return { success: false, message: 'Failed to fetch foods from database' };
            }

            console.log(`Found ${result.data.length} foods to sync`);
            
            // Upload lên Pinecone
            const uploadResult = await this.upsertFoods(result.data);
            
            if (uploadResult.success) {
                console.log('Food sync completed successfully');
            }
            
            return uploadResult;
        } catch (error) {
            console.error('Error syncing foods:', error);
            return { success: false, message: error.message };
        }
    }

    // Xóa thực phẩm khỏi Pinecone
    async deleteFood(foodId) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            await this.index.deleteOne(`food_${foodId}`);
            return { success: true, message: 'Food deleted successfully' };
        } catch (error) {
            console.error('Error deleting food:', error);
            return { success: false, message: error.message };
        }
    }

    // Lấy thống kê index
    async getIndexStats() {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const stats = await this.index.describeIndexStats();
            return { success: true, data: stats };
        } catch (error) {
            console.error('Error getting index stats:', error);
            return { success: false, message: error.message };
        }
    }
}

module.exports = new PineconeService();
