const { GoogleGenAI } = require('@google/genai');
require('dotenv').config();

class GeminiService {
    constructor() {
        this.genAI = null;
        this.initialized = false;
    }

    async initialize() {
        try {
            if (this.initialized) return true;

            if (!process.env.GEMINI_API_KEY) {
                throw new Error('GEMINI_API_KEY not found in environment variables');
            }

            this.genAI = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
            this.initialized = true;

            console.log('Gemini service initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize Gemini service:', error);
            return false;
        }
    }

    // Tạo system prompt cho việc tạo thực đơn
    createSystemPrompt() {
        return `Bạn là một chuyên gia dinh dưỡng AI chuyên nghiệp. Nhiệm vụ của bạn là tạo thực đơn cân bằng dinh dưỡng từ danh sách thực phẩm được cung cấp.

NGUYÊN TẮC QUAN TRỌNG:
1. PHẢI đạt đúng mục tiêu năng lượng được yêu cầu (±50 kcal)
2. PHẢI tạo đúng số bữa ăn được yêu cầu
3. Đảm bảo cân bằng dinh dưỡng theo khuyến nghị y tế
4. Tuân thủ nghiêm ngặt các ràng buộc và hạn chế được yêu cầu
5. Đa dạng hóa thực phẩm trong thực đơn
6. Tính toán chính xác khối lượng thực phẩm để đạt mục tiêu năng lượng
7. Ưu tiên thực phẩm tự nhiên, ít chế biến

QUY TẮC TÍNH TOÁN:
- Tất cả giá trị dinh dưỡng trong database được tính theo 100g thực phẩm
- Công thức tính: giá_trị_thực_tế = (giá_trị_database / 100) * khối_lượng_gram
- Khối lượng phải hợp lý cho từng loại thực phẩm (50-500g tùy loại)
- TÍNH TOÁN CHÍNH XÁC để đạt mục tiêu năng lượng

CẤU TRÚC THỰC ĐƠN:
- Tên bữa ăn: Sáng, Trưa, Tối, Phụ 1, Phụ 2 (tùy theo số bữa yêu cầu)
- Mỗi bữa ăn có courses (món ăn) và danh sách thực phẩm
- Tính toán tổng dinh dưỡng cho toàn bộ thực đơn

RESPONSE FORMAT: Chỉ trả về JSON hợp lệ, không có text giải thích thêm.`;
    }

    // Tạo user prompt từ yêu cầu và danh sách thực phẩm
    createUserPrompt(requirements, availableFoods, targetNutrition = null, mealCount = 3) {
        // Tạo danh sách tên bữa ăn theo đúng thứ tự menuTime
        const allMealTimeLabels = [
            { name: 'Sáng', label: '6h - 6h30', id: 3, isMain: true },
            { name: 'Phụ 1', label: '9h', id: 4, isMain: false },
            { name: 'Trưa', label: '11h30 - 12h', id: 5, isMain: true },
            { name: 'Phụ 2', label: '15h', id: 6, isMain: false },
            { name: 'Tối', label: '17h30 - 18h', id: 7, isMain: true },
            { name: 'Phụ 3', label: '21h', id: 8, isMain: false }
        ];

        // Chọn bữa ăn theo logic thông minh
        let requestedMeals = [];
        if (mealCount === 3) {
            // 3 bữa chính: Sáng, Trưa, Tối
            requestedMeals = [
                allMealTimeLabels[0], // Sáng
                allMealTimeLabels[2], // Trưa
                allMealTimeLabels[4]  // Tối
            ];
        } else if (mealCount === 5) {
            // 5 bữa: Sáng, Phụ 1, Trưa, Phụ 2, Tối
            requestedMeals = [
                allMealTimeLabels[0], // Sáng
                allMealTimeLabels[1], // Phụ 1
                allMealTimeLabels[2], // Trưa
                allMealTimeLabels[3], // Phụ 2
                allMealTimeLabels[4]  // Tối
            ];
        } else {
            // Mặc định: lấy theo thứ tự
            requestedMeals = allMealTimeLabels.slice(0, mealCount);
        }

        let prompt = `YÊU CẦU TẠO THỰC ĐƠN:
${requirements}

SỐ BỮA ĂN YÊU CẦU: ${mealCount} bữa (${requestedMeals.map(m => `${m.name} (${m.label})`).join(', ')})

NGUYÊN TẮC TẠO THỰC ĐƠN THỰC TẾ:
- Bữa chính (Sáng, Trưa, Tối): 3-5 món khác nhau để cân bằng dinh dưỡng
- Bữa phụ (Phụ 1, Phụ 2, Phụ 3): 1-2 món nhẹ (trái cây, sữa, bánh...)
- Đa dạng hóa thực phẩm: protein, carbohydrate, chất béo, vitamin, chất xơ
- Mỗi bữa chính nên có: 1 món protein + 1 món tinh bột + 1-2 món rau/trái cây
- Ưu tiên sử dụng MÓN ĂN (dishes) khi có sẵn thay vì thực phẩm đơn lẻ
- TRÁNH LẶP LẠI: Mỗi thực phẩm/món ăn chỉ xuất hiện tối đa 1-2 lần trong toàn bộ thực đơn
- ĐA DẠNG HÓA: Sử dụng nhiều loại thực phẩm khác nhau, không lặp lại cùng một món

DANH SÁCH THỰC PHẨM VÀ MÓN ĂN KHẢ DỤNG:
${availableFoods.map(food => {
    if (food.item_type === 'dish') {
        return `- ID: ${food.food_id}, Tên: ${food.name} (MÓN ĂN), Loại: ${food.category || 'món ăn'}, Năng lượng: ${food.energy} kcal/khẩu phần, Protein: ${food.protein}g/khẩu phần, Carbohydrate: ${food.carbohydrate}g/khẩu phần, Fat: ${food.fat}g/khẩu phần, Fiber: ${food.fiber || 0}g/khẩu phần`;
    } else {
        return `- ID: ${food.food_id}, Tên: ${food.name}${food.ten ? ' (' + food.ten + ')' : ''} (THỰC PHẨM), Loại: ${food.type}, Năng lượng: ${food.energy} kcal/100g, Protein: ${food.protein}g/100g, Carbohydrate: ${food.carbohydrate}g/100g, Fat: ${food.fat}g/100g, Fiber: ${food.fiber || 0}g/100g`;
    }
}).join('\n')}`;

        if (targetNutrition) {
            prompt += `\n\nMỤC TIÊU DINH DƯỠNG (BẮT BUỘC ĐẠT):
- Tổng năng lượng: ${targetNutrition.energy || 2000} kcal (PHẢI đạt ±50 kcal)
- Tổng protein: ${targetNutrition.protein || 'tự động tính'} g
- Tổng carbohydrate: ${targetNutrition.carbohydrate || 'tự động tính'} g
- Tổng fat: ${targetNutrition.fat || 'tự động tính'} g

CÁCH TÍNH TOÁN NĂNG LƯỢNG:
- Năng lượng thực tế = (năng lượng/100g) × (khối lượng/100)
- VÍ DỤ: Thực phẩm có 200 kcal/100g, dùng 150g → 200 × 1.5 = 300 kcal
- PHẢI tính toán chính xác để tổng năng lượng = ${targetNutrition.energy || 2000} kcal
- Nếu thiếu năng lượng: tăng khối lượng hoặc thêm thực phẩm giàu năng lượng
- Nếu thừa năng lượng: giảm khối lượng hoặc thay thế bằng thực phẩm ít năng lượng`;
        }

        prompt += `\n\nVÍ DỤ TÍNH TOÁN CHI TIẾT:
Để đạt ${targetNutrition?.energy || 2000} kcal với ${mealCount} bữa ăn:
- Mỗi bữa cần khoảng ${Math.round((targetNutrition?.energy || 2000) / mealCount)} kcal

CÁCH TÍNH KHỐI LƯỢNG:
- THỰC PHẨM: weight = (target_energy * 100) / energy_per_100g
  Ví dụ: Cần 300 kcal từ gạo (380 kcal/100g) → weight = (300 * 100) / 380 = 79g
- MÓN ĂN: weight = target_energy / energy_per_serving
  Ví dụ: Cần 300 kcal từ cơm gà (250 kcal/khẩu phần) → weight = 300 / 250 = 1.2 khẩu phần

TRẢ VỀ JSON THEO FORMAT SAU (chỉ JSON, không có text khác):
{
  "success": true,
  "menu": {
    "name": "Tên thực đơn",
    "created_at": "${new Date().toISOString()}",
    "ai_generated": true,
    "requirements": "${requirements}",
    "detail": [`;

        // Tạo template cho từng bữa ăn
        const targetEnergyPerMeal = Math.round((targetNutrition?.energy || 2000) / mealCount);
        requestedMeals.forEach((meal, index) => {
            const isMainMeal = ['Sáng', 'Trưa', 'Tối'].includes(meal.name);
            const foodCount = isMainMeal ? '3-5 món' : '1-2 món';

            prompt += `
      {
        "id": ${meal.id},
        "name": "${meal.name}",
        "courses": [{"id": 1, "name": "Món chính"}],
        "listFood": [
          // ${foodCount} khác nhau cho bữa ${meal.name} (${meal.label})
          // Mục tiêu: ~${targetEnergyPerMeal} kcal
          {
            "id": 1,
            "id_food": [ID_THỰC_PHẨM_1],
            "name": "[TÊN_THỰC_PHẨM_1]",
            "weight": [KHỐI_LƯỢNG_GRAM],
            "energy": [NĂNG_LƯỢNG_THỰC_TẾ],
            "protein": [PROTEIN_THỰC_TẾ],
            "carbohydrate": [CARBOHYDRATE_THỰC_TẾ],
            "fat": [FAT_THỰC_TẾ],
            "fiber": [FIBER_THỰC_TẾ],
            "course_id": 1
          }${isMainMeal ? `,
          {
            "id": 2,
            "id_food": [ID_THỰC_PHẨM_2],
            "name": "[TÊN_THỰC_PHẨM_2]",
            "weight": [KHỐI_LƯỢNG_GRAM],
            "energy": [NĂNG_LƯỢNG_THỰC_TẾ],
            "protein": [PROTEIN_THỰC_TẾ],
            "carbohydrate": [CARBOHYDRATE_THỰC_TẾ],
            "fat": [FAT_THỰC_TẾ],
            "fiber": [FIBER_THỰC_TẾ],
            "course_id": 1
          },
          {
            "id": 3,
            "id_food": [ID_THỰC_PHẨM_3],
            "name": "[TÊN_THỰC_PHẨM_3]",
            "weight": [KHỐI_LƯỢNG_GRAM],
            "energy": [NĂNG_LƯỢNG_THỰC_TẾ],
            "protein": [PROTEIN_THỰC_TẾ],
            "carbohydrate": [CARBOHYDRATE_THỰC_TẾ],
            "fat": [FAT_THỰC_TẾ],
            "fiber": [FIBER_THỰC_TẾ],
            "course_id": 1
          }` : ''}
        ]
      }${index < requestedMeals.length - 1 ? ',' : ''}`;
        });

        prompt += `
    ]
  },
  "nutrition_summary": {
    "total_energy": ${targetNutrition?.energy || 2000},
    "total_protein": 80,
    "total_carbohydrate": 200,
    "total_fat": 60,
    "total_fiber": 25
  }
}

LƯU Ý QUAN TRỌNG:
- CHỈ SỬ DỤNG ID có trong danh sách: [${availableFoods.map(f => f.food_id).join(', ')}]
- KHÔNG tự tạo ID mới hoặc sử dụng ID không có trong danh sách
- TRÁNH LẶP LẠI: Mỗi thực phẩm/món ăn chỉ xuất hiện tối đa 1-2 lần trong toàn bộ thực đơn
- PHẢI tạo đúng ${mealCount} bữa ăn với ID chính xác: ${requestedMeals.map(m => `${m.name}=${m.id}`).join(', ')}
- PHẢI đạt mục tiêu năng lượng ${targetNutrition?.energy || 2000} kcal (±50 kcal)
- Bữa chính (Sáng, Trưa, Tối): 3-5 món khác nhau để cân bằng dinh dưỡng
- Bữa phụ (Phụ 1, Phụ 2, Phụ 3): 1-2 món nhẹ
- Mỗi thực phẩm PHẢI có course_id = 1
- Mỗi bữa ăn PHẢI có courses array với ít nhất 1 course
- Tính toán chính xác weight để đạt mục tiêu năng lượng
- Chia đều năng lượng cho các bữa ăn (khoảng ${Math.round((targetNutrition?.energy || 2000) / mealCount)} kcal/bữa)
- Sử dụng khối lượng hợp lý: 50-500g cho thực phẩm chính, 100-300g cho trái cây
- KIỂM TRA LẠI tổng năng lượng trước khi trả về JSON`;

        return prompt;
    }

    // Gọi Gemini API để tạo thực đơn
    async generateMenu(requirements, availableFoods, targetNutrition = null, mealCount = 3) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const systemPrompt = this.createSystemPrompt();
            const userPrompt = this.createUserPrompt(requirements, availableFoods, targetNutrition, mealCount);

            const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;

            console.log('Sending request to Gemini API...');
            console.log('Target energy:', targetNutrition?.energy || 'not specified');
            console.log('Meal count:', mealCount);

            const response = await this.genAI.models.generateContent({
                model: 'gemini-2.0-flash-001',
                contents: fullPrompt
            });
            const text = response.text;

            console.log('Received response from Gemini API');
            
            // Parse JSON response - handle markdown code blocks
            let cleanedText = text.trim();

            // Remove markdown code blocks if present
            if (cleanedText.startsWith('```json')) {
                cleanedText = cleanedText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
            } else if (cleanedText.startsWith('```')) {
                cleanedText = cleanedText.replace(/^```\s*/, '').replace(/\s*```$/, '');
            }

            try {
                const jsonResponse = JSON.parse(cleanedText);

                // Validate response structure
                if (!jsonResponse.success || !jsonResponse.menu) {
                    throw new Error('Invalid response structure from AI');
                }

                // Validate và fix cấu trúc menu
                const validatedMenu = this.validateAndFixMenuStructure(jsonResponse.menu, mealCount);

                // Tính toán lại dinh dưỡng để đảm bảo chính xác
                const recalculatedMenu = this.recalculateNutrition(validatedMenu, availableFoods);

                return {
                    success: true,
                    data: {
                        ...jsonResponse,
                        menu: recalculatedMenu.menu,
                        nutrition_summary: recalculatedMenu.nutrition_summary
                    }
                };
            } catch (parseError) {
                console.error('Error parsing AI response:', parseError);
                console.log('Raw AI response:', text);

                // Thử extract JSON từ response nếu có text thừa
                const jsonMatch = text.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    try {
                        const jsonResponse = JSON.parse(jsonMatch[0]);

                        // Validate và fix cấu trúc menu
                        const validatedMenu = this.validateAndFixMenuStructure(jsonResponse.menu, mealCount);
                        const recalculatedMenu = this.recalculateNutrition(validatedMenu, availableFoods);

                        return {
                            success: true,
                            data: {
                                ...jsonResponse,
                                menu: recalculatedMenu.menu,
                                nutrition_summary: recalculatedMenu.nutrition_summary
                            }
                        };
                    } catch (secondParseError) {
                        console.error('Second parse attempt failed:', secondParseError);
                    }
                }

                return {
                    success: false,
                    message: 'Failed to parse AI response as JSON',
                    raw_response: text
                };
            }
        } catch (error) {
            console.error('Error generating menu with Gemini:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }



    // Tạo thực đơn với retry logic
    async generateMenuWithRetry(requirements, availableFoods, targetNutrition = null, mealCount = 3, maxRetries = 3) {
        let lastError = null;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`Attempt ${attempt}/${maxRetries} to generate menu`);
                const result = await this.generateMenu(requirements, availableFoods, targetNutrition, mealCount);

                if (result.success) {
                    // Kiểm tra xem có đạt mục tiêu năng lượng không
                    const actualEnergy = result.data.nutrition_summary?.total_energy || 0;
                    const targetEnergy = targetNutrition?.energy || 2000;
                    const energyDiff = Math.abs(actualEnergy - targetEnergy);

                    if (energyDiff > 100) { // Cho phép sai lệch 100 kcal
                        console.log(`Energy target not met: ${actualEnergy} vs ${targetEnergy} (diff: ${energyDiff})`);
                        if (attempt < maxRetries) {
                            console.log('Retrying to meet energy target...');
                            continue;
                        }
                    }

                    return result;
                }

                lastError = result;
                console.log(`Attempt ${attempt} failed:`, result.message);

                // Đợi trước khi retry
                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            } catch (error) {
                lastError = { success: false, message: error.message };
                console.error(`Attempt ${attempt} error:`, error);

                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
        }

        return lastError || { success: false, message: 'All retry attempts failed' };
    }

    // Validate và fix cấu trúc menu để đảm bảo tương thích với hệ thống
    validateAndFixMenuStructure(menu, expectedMealCount = 3) {
        try {
            // Map tên bữa ăn với ID menuTime từ database
            const mealTimeMapping = {
                'Sáng': 3,      // 6h - 6h30
                'Phụ 1': 4,     // 9h
                'Trưa': 5,      // 11h30 - 12h
                'Phụ 2': 6,     // 15h
                'Tối': 7,       // 17h30 - 18h
                'Phụ 3': 8      // 21h
            };

            // Danh sách bữa ăn theo đúng thứ tự menuTime
            const allMealTimeLabels = [
                { name: 'Sáng', label: '6h - 6h30', id: 3, isMain: true },
                { name: 'Phụ 1', label: '9h', id: 4, isMain: false },
                { name: 'Trưa', label: '11h30 - 12h', id: 5, isMain: true },
                { name: 'Phụ 2', label: '15h', id: 6, isMain: false },
                { name: 'Tối', label: '17h30 - 18h', id: 7, isMain: true },
                { name: 'Phụ 3', label: '21h', id: 8, isMain: false }
            ];

            // Chọn bữa ăn theo logic thông minh
            let expectedMealLabels = [];
            if (expectedMealCount === 3) {
                // 3 bữa chính: Sáng, Trưa, Tối
                expectedMealLabels = [
                    allMealTimeLabels[0], // Sáng
                    allMealTimeLabels[2], // Trưa
                    allMealTimeLabels[4]  // Tối
                ];
            } else if (expectedMealCount === 5) {
                // 5 bữa: Sáng, Phụ 1, Trưa, Phụ 2, Tối
                expectedMealLabels = [
                    allMealTimeLabels[0], // Sáng
                    allMealTimeLabels[1], // Phụ 1
                    allMealTimeLabels[2], // Trưa
                    allMealTimeLabels[3], // Phụ 2
                    allMealTimeLabels[4]  // Tối
                ];
            } else {
                // Mặc định: lấy theo thứ tự
                expectedMealLabels = allMealTimeLabels.slice(0, expectedMealCount);
            }

            const mealNames = expectedMealLabels.map(m => m.name);

            // Đảm bảo có đủ số bữa ăn
            if (!menu.detail || menu.detail.length < expectedMealCount) {
                console.warn(`Menu has ${menu.detail?.length || 0} meals, expected ${expectedMealCount}`);

                // Tạo thêm bữa ăn nếu thiếu
                if (!menu.detail) menu.detail = [];

                while (menu.detail.length < expectedMealCount) {
                    const mealIndex = menu.detail.length;
                    const mealInfo = expectedMealLabels[mealIndex] || {
                        name: `Bữa ${mealIndex + 1}`,
                        id: mealIndex + 1
                    };

                    menu.detail.push({
                        id: mealInfo.id,
                        name: mealInfo.name,
                        courses: [{ id: 1, name: 'Món chính' }],
                        listFood: []
                    });
                }
            }

            // Đảm bảo mỗi bữa ăn có cấu trúc đúng và ID đúng
            menu.detail.forEach((meal, mealIndex) => {
                // Đảm bảo có tên bữa ăn hợp lệ
                if (!meal.name || !mealTimeMapping[meal.name]) {
                    meal.name = mealNames[mealIndex] || `Bữa ${mealIndex + 1}`;
                }

                // Đảm bảo có ID đúng theo mapping
                meal.id = mealTimeMapping[meal.name] || (mealIndex + 1);

                // Đảm bảo có courses
                if (!meal.courses || !Array.isArray(meal.courses) || meal.courses.length === 0) {
                    meal.courses = [{ id: 1, name: 'Món chính' }];
                }

                // Đảm bảo mỗi food có course_id
                if (meal.listFood && Array.isArray(meal.listFood)) {
                    meal.listFood.forEach((food, foodIndex) => {
                        if (!food.id) food.id = foodIndex + 1;
                        if (!food.course_id) food.course_id = 1;
                    });
                }
            });

            // Sắp xếp lại theo thứ tự ID
            menu.detail.sort((a, b) => a.id - b.id);

            return menu;
        } catch (error) {
            console.error('Error validating menu structure:', error);
            return menu;
        }
    }

    // Tính lại dinh dưỡng dựa trên database thực tế
    recalculateNutrition(menu, availableFoods) {
        try {
            let totalNutrition = {
                energy: 0,
                protein: 0,
                carbohydrate: 0,
                fat: 0,
                fiber: 0
            };

            menu.detail.forEach(meal => {
                if (meal.listFood && Array.isArray(meal.listFood)) {
                    meal.listFood.forEach(food => {
                        let dbFood = null;

                        // Kiểm tra xem đây là món ăn hay thực phẩm
                        if (food.id_food && food.id_food.toString().startsWith('dish_')) {
                            // Đây là món ăn
                            const dishId = food.id_food.toString().replace('dish_', '');
                            dbFood = availableFoods.find(f =>
                                f.item_type === 'dish' &&
                                (f.food_id === `dish_${dishId}` || f.food_id === dishId)
                            );
                        } else {
                            // Đây là thực phẩm thông thường
                            const foodId = food.id_food ? food.id_food.toString() : null;
                            dbFood = availableFoods.find(f =>
                                f.item_type === 'food' && (
                                    f.food_id == foodId ||
                                    f.name === food.name ||
                                    f.ten === food.name
                                )
                            );
                        }

                        if (!dbFood) {
                            console.log(`Food/Dish not found in database: ${food.name} (id: ${food.id_food})`);
                            // Sử dụng giá trị mặc định để tránh lỗi
                            food.energy = 0;
                            food.protein = 0;
                            food.carbohydrate = 0;
                            food.fat = 0;
                            food.fiber = 0;
                            return; // Skip item này
                        }

                        if (dbFood) {
                            const weight = parseFloat(food.weight) || 100;
                            let energy, protein, carbohydrate, fat, fiber;

                            if (dbFood.item_type === 'dish') {
                                // Đối với món ăn: sử dụng total_* và weight là số khẩu phần
                                const servings = weight;
                                energy = (parseFloat(dbFood.energy) || 0) * servings;
                                protein = (parseFloat(dbFood.protein) || 0) * servings;
                                carbohydrate = (parseFloat(dbFood.carbohydrate) || 0) * servings;
                                fat = (parseFloat(dbFood.fat) || 0) * servings;
                                fiber = (parseFloat(dbFood.fiber) || 0) * servings;
                            } else {
                                // Đối với thực phẩm: sử dụng ratio theo 100g
                                const ratio = weight / 100;
                                energy = (parseFloat(dbFood.energy) || 0) * ratio;
                                protein = (parseFloat(dbFood.protein) || 0) * ratio;
                                carbohydrate = (parseFloat(dbFood.carbohydrate) || 0) * ratio;
                                fat = (parseFloat(dbFood.fat) || 0) * ratio;
                                fiber = (parseFloat(dbFood.fiber) || 0) * ratio;
                            }

                            // Làm tròn và gán giá trị
                            food.energy = Math.round(energy * 10) / 10;
                            food.protein = Math.round(protein * 10) / 10;
                            food.carbohydrate = Math.round(carbohydrate * 10) / 10;
                            food.fat = Math.round(fat * 10) / 10;
                            food.fiber = Math.round(fiber * 10) / 10;

                            // Cộng vào tổng
                            totalNutrition.energy += food.energy;
                            totalNutrition.protein += food.protein;
                            totalNutrition.carbohydrate += food.carbohydrate;
                            totalNutrition.fat += food.fat;
                            totalNutrition.fiber += food.fiber;

                            // Cập nhật ID và thông tin
                            if (dbFood.item_type === 'dish') {
                                food.id_food = dbFood.food_id;
                                food.dish_category = dbFood.category;
                            } else {
                                food.id_food = dbFood.food_id;
                            }
                        } else {
                            console.warn(`Food/Dish not found in database: ${food.name} (id: ${food.id_food})`);
                        }
                    });
                }
            });

            // Làm tròn tổng dinh dưỡng
            Object.keys(totalNutrition).forEach(key => {
                totalNutrition[key] = Math.round(totalNutrition[key] * 10) / 10;
            });

            return {
                menu: menu,
                nutrition_summary: totalNutrition
            };
        } catch (error) {
            console.error('Error recalculating nutrition:', error);
            return {
                menu: menu,
                nutrition_summary: {
                    energy: 0,
                    protein: 0,
                    carbohydrate: 0,
                    fat: 0,
                    fiber: 0
                }
            };
        }
    }

    // Validate menu structure
    validateMenuStructure(menu) {
        const errors = [];

        if (!menu.name) errors.push('Menu name is required');
        if (!menu.detail || !Array.isArray(menu.detail)) errors.push('Menu detail must be an array');

        if (menu.detail) {
            menu.detail.forEach((meal, mealIndex) => {
                if (!meal.name) errors.push(`Meal ${mealIndex + 1} name is required`);
                if (!meal.listFood || !Array.isArray(meal.listFood)) {
                    errors.push(`Meal ${mealIndex + 1} listFood must be an array`);
                } else {
                    meal.listFood.forEach((food, foodIndex) => {
                        if (!food.id_food) errors.push(`Food ${foodIndex + 1} in meal ${mealIndex + 1} missing id_food`);
                        if (!food.name) errors.push(`Food ${foodIndex + 1} in meal ${mealIndex + 1} missing name`);
                        if (!food.weight || food.weight <= 0) errors.push(`Food ${foodIndex + 1} in meal ${mealIndex + 1} invalid weight`);
                    });
                }
            });
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

module.exports = new GeminiService();
