const pineconeService = require('./pineconeService');
const geminiService = require('./geminiService');
const foodService = require('./foodService');
const commonService = require('./commonService');
const dishService = require('./dishService');

class AIMenuService {
    constructor() {
        this.initialized = false;
    }

    async initialize() {
        try {
            if (this.initialized) return true;

            // Initialize both services
            const pineconeInit = await pineconeService.initialize();
            const geminiInit = await geminiService.initialize();

            if (!pineconeInit || !geminiInit) {
                throw new Error('Failed to initialize AI services');
            }

            this.initialized = true;
            console.log('AI Menu Service initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize AI Menu Service:', error);
            return false;
        }
    }

    // T<PERSON>o thực đơn thông minh
    async generateSmartMenu(request) {
        try {
            if (!this.initialized) {
                await this.initialize();
            }

            const {
                requirements,
                preferences = [],
                restrictions = [],
                meal_count = 3,
                target_nutrition = null,
                search_limit = 50
            } = request;

            console.log('Starting smart menu generation...');
            console.log('Requirements:', requirements);

            // Bước 1: Tìm kiếm thực phẩm phù hợp bằng vector search
            const searchResults = await this.searchRelevantFoods(
                requirements, 
                preferences, 
                restrictions, 
                search_limit
            );

            if (!searchResults.success || searchResults.data.length === 0) {
                return {
                    success: false,
                    message: 'Không tìm thấy thực phẩm phù hợp với yêu cầu'
                };
            }

            console.log(`Found ${searchResults.data.length} relevant foods`);

            // Bước 2: Lọc và chuẩn bị danh sách thực phẩm
            const preparedFoods = await this.prepareFoodsForAI(searchResults.data, restrictions);

            if (preparedFoods.length === 0) {
                return {
                    success: false,
                    message: 'Không có thực phẩm nào phù hợp sau khi lọc'
                };
            }

            console.log(`Prepared ${preparedFoods.length} foods for AI`);

            // Bước 3: Tạo thực đơn bằng Gemini AI
            const aiResult = await geminiService.generateMenuWithRetry(
                requirements,
                preparedFoods,
                target_nutrition,
                meal_count
            );

            if (!aiResult.success) {
                return {
                    success: false,
                    message: 'AI không thể tạo thực đơn: ' + aiResult.message
                };
            }

            // Bước 4: Validate và enhance menu
            const finalMenu = await this.enhanceGeneratedMenu(aiResult.data);

            console.log('Smart menu generation completed successfully');

            return {
                success: true,
                data: finalMenu,
                metadata: {
                    foods_searched: searchResults.data.length,
                    foods_used: preparedFoods.length,
                    generation_time: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('Error in generateSmartMenu:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    // Tìm kiếm thực phẩm liên quan bằng vector search
    async searchRelevantFoods(requirements, preferences, restrictions, limit = 50) {
        try {
            // Tạo query string từ requirements và preferences
            let searchQuery = requirements;
            
            if (preferences.length > 0) {
                searchQuery += ' ' + preferences.join(' ');
            }

            // Tìm kiếm cả thực phẩm và món ăn
            const searchResult = await pineconeService.searchFoodsAndDishes(searchQuery, {
                topK: limit,
                includeMetadata: true,
                foodLimit: Math.ceil(limit * 0.7), // 70% thực phẩm
                dishLimit: Math.ceil(limit * 0.3)  // 30% món ăn
            });

            if (!searchResult.success) {
                // Fallback: tìm kiếm trực tiếp từ database
                console.log('Vector search failed, using database fallback');
                return await this.fallbackFoodSearch(requirements, limit);
            }

            // Xử lý kết quả để chuẩn hóa format
            const processedData = [];

            for (const item of searchResult.data) {
                if (item.type === 'food') {
                    // Thực phẩm - giữ nguyên format
                    processedData.push({
                        food_id: item.food_id,
                        name: item.name,
                        ten: item.ten,
                        type: item.food_type,
                        type_year: item.type_year,
                        energy: item.energy,
                        protein: item.protein,
                        carbohydrate: item.carbohydrate,
                        fat: item.fat,
                        fiber: item.fiber,
                        score: item.score,
                        item_type: 'food'
                    });
                } else if (item.type === 'dish') {
                    // Món ăn - chuyển đổi thành format thực phẩm để AI có thể sử dụng
                    processedData.push({
                        food_id: `dish_${item.dish_id}`, // Đánh dấu là món ăn
                        name: item.name,
                        ten: item.description || '',
                        type: 'dish',
                        type_year: 'dish',
                        energy: item.total_energy,
                        protein: item.total_protein,
                        carbohydrate: item.total_carbohydrate,
                        fat: item.total_fat,
                        fiber: item.total_fiber,
                        score: item.score,
                        item_type: 'dish',
                        category: item.category
                    });
                }
            }

            return {
                success: true,
                data: processedData,
                metadata: searchResult.metadata
            };
        } catch (error) {
            console.error('Error in searchRelevantFoods:', error);
            // Fallback to database search
            return await this.fallbackFoodSearch(requirements, limit);
        }
    }

    // Fallback search khi vector search không hoạt động
    async fallbackFoodSearch(requirements, limit = 50) {
        try {
            // Tìm kiếm đơn giản trong database
            const keywords = requirements.toLowerCase().split(/\s+/);
            const searchTerms = keywords.filter(word => word.length > 2);
            
            if (searchTerms.length === 0) {
                // Lấy thực phẩm phổ biến
                const sql = `
                    SELECT * FROM food_info 
                    WHERE active = 1 
                    ORDER BY RAND() 
                    LIMIT ?
                `;
                const result = await commonService.getListTable(sql, [limit]);
                
                if (result.success) {
                    return {
                        success: true,
                        data: result.data.map(food => ({
                            food_id: food.id,
                            name: food.name,
                            ten: food.ten,
                            type: food.type,
                            type_year: food.type_year,
                            energy: food.energy,
                            protein: food.protein,
                            carbohydrate: food.carbohydrate,
                            fat: food.fat,
                            fiber: food.fiber,
                            score: 0.5
                        }))
                    };
                }
            } else {
                // Tìm kiếm theo từ khóa
                const searchTerm = `%${searchTerms.join('%')}%`;
                const sql = `
                    SELECT * FROM food_info 
                    WHERE active = 1 
                    AND (name LIKE ? OR ten LIKE ?)
                    ORDER BY 
                        CASE 
                            WHEN name LIKE ? THEN 1
                            WHEN ten LIKE ? THEN 2
                            ELSE 3
                        END
                    LIMIT ?
                `;
                
                const result = await commonService.getListTable(sql, [
                    searchTerm, searchTerm, 
                    `%${searchTerms[0]}%`, `%${searchTerms[0]}%`,
                    limit
                ]);
                
                if (result.success) {
                    return {
                        success: true,
                        data: result.data.map(food => ({
                            food_id: food.id,
                            name: food.name,
                            ten: food.ten,
                            type: food.type,
                            type_year: food.type_year,
                            energy: food.energy,
                            protein: food.protein,
                            carbohydrate: food.carbohydrate,
                            fat: food.fat,
                            fiber: food.fiber,
                            score: 0.7
                        }))
                    };
                }
            }

            return { success: false, message: 'No foods found', data: [] };
        } catch (error) {
            console.error('Error in fallbackFoodSearch:', error);
            return { success: false, message: error.message, data: [] };
        }
    }

    // Chuẩn bị danh sách thực phẩm cho AI
    async prepareFoodsForAI(searchResults, restrictions = []) {
        try {
            let foods = [...searchResults];

            // Lọc theo restrictions
            if (restrictions.length > 0) {
                foods = foods.filter(food => {
                    const foodName = (food.name + ' ' + (food.ten || '')).toLowerCase();
                    return !restrictions.some(restriction => 
                        foodName.includes(restriction.toLowerCase())
                    );
                });
            }

            // Đảm bảo đa dạng loại thực phẩm
            const typeGroups = {};
            foods.forEach(food => {
                if (!typeGroups[food.type]) {
                    typeGroups[food.type] = [];
                }
                typeGroups[food.type].push(food);
            });

            // Lấy tối đa 10 thực phẩm mỗi loại để đảm bảo đa dạng
            const balancedFoods = [];
            Object.values(typeGroups).forEach(group => {
                const sorted = group.sort((a, b) => (b.score || 0) - (a.score || 0));
                balancedFoods.push(...sorted.slice(0, 10));
            });

            // Sắp xếp theo score và lấy top foods
            const finalFoods = balancedFoods
                .sort((a, b) => (b.score || 0) - (a.score || 0))
                .slice(0, 30); // Giới hạn để tránh prompt quá dài

            return finalFoods;
        } catch (error) {
            console.error('Error in prepareFoodsForAI:', error);
            return searchResults.slice(0, 20); // Fallback
        }
    }

    // Enhance menu được tạo bởi AI
    async enhanceGeneratedMenu(aiMenuData) {
        try {
            const menu = aiMenuData.menu;
            
            // Validate menu structure
            const validation = geminiService.validateMenuStructure(menu);
            if (!validation.isValid) {
                console.warn('Menu validation warnings:', validation.errors);
            }

            // Thêm thông tin bổ sung
            menu.ai_generated = true;
            menu.created_at = new Date().toISOString();
            
            // Đảm bảo có ID cho các phần tử
            menu.detail.forEach((meal, mealIndex) => {
                if (!meal.id) meal.id = mealIndex + 1;
                
                meal.listFood.forEach((food, foodIndex) => {
                    if (!food.id) food.id = foodIndex + 1;
                });
            });

            return {
                menu: menu,
                nutrition_summary: aiMenuData.nutrition_summary,
                ai_metadata: {
                    generated_at: new Date().toISOString(),
                    requirements: menu.requirements,
                    validation: validation
                }
            };
        } catch (error) {
            console.error('Error in enhanceGeneratedMenu:', error);
            return aiMenuData;
        }
    }

    // Tạo thực đơn mẫu cho demo
    async generateSampleMenu(sampleType = 'balanced') {
        const sampleRequests = {
            balanced: {
                requirements: 'Tạo thực đơn cân bằng dinh dưỡng cho người trưởng thành khỏe mạnh, 3 bữa ăn trong ngày',
                preferences: ['đa dạng thực phẩm', 'cân bằng dinh dưỡng'],
                restrictions: [],
                meal_count: 3,
                target_nutrition: {
                    energy: 2000,
                    protein: 80,
                    carbohydrate: 250,
                    fat: 70
                }
            },
            diabetes: {
                requirements: 'Tạo thực đơn cho bệnh nhân tiểu đường, ít đường, nhiều chất xơ, kiểm soát carbohydrate',
                preferences: ['ít đường', 'nhiều chất xơ', 'ít carbohydrate'],
                restrictions: ['đường', 'kẹo', 'bánh ngọt'],
                meal_count: 5,
                target_nutrition: {
                    energy: 1800,
                    protein: 90,
                    carbohydrate: 180,
                    fat: 60
                }
            },
            weight_loss: {
                requirements: 'Tạo thực đơn giảm cân, ít calo, nhiều protein, nhiều rau xanh',
                preferences: ['ít calo', 'nhiều protein', 'rau xanh'],
                restrictions: ['đồ chiên', 'đồ ngọt'],
                meal_count: 4,
                target_nutrition: {
                    energy: 1500,
                    protein: 100,
                    carbohydrate: 150,
                    fat: 50
                }
            }
        };

        const request = sampleRequests[sampleType] || sampleRequests.balanced;
        return await this.generateSmartMenu(request);
    }

    // Sync dữ liệu food_info lên Pinecone
    async syncFoodData() {
        try {
            console.log('Starting food data sync...');
            const result = await pineconeService.syncAllFoods();
            
            if (result.success) {
                console.log('Food data sync completed successfully');
            } else {
                console.error('Food data sync failed:', result.message);
            }
            
            return result;
        } catch (error) {
            console.error('Error in syncFoodData:', error);
            return { success: false, message: error.message };
        }
    }

    // Lấy thống kê hệ thống
    async getSystemStats() {
        try {
            const pineconeStats = await pineconeService.getIndexStats();
            
            return {
                success: true,
                data: {
                    pinecone: pineconeStats.success ? pineconeStats.data : null,
                    initialized: this.initialized,
                    timestamp: new Date().toISOString()
                }
            };
        } catch (error) {
            console.error('Error getting system stats:', error);
            return { success: false, message: error.message };
        }
    }
}

module.exports = new AIMenuService();
