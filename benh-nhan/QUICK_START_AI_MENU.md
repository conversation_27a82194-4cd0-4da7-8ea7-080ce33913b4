# 🚀 Quick Start - AI Menu Generator

## ✅ Cậ<PERSON> nhật mới - <PERSON><PERSON> chuy<PERSON>n sang @google/genai!

**<PERSON><PERSON><PERSON> vấn đề đã được khắc phục:**
- ✅ Cập nhật từ `@google/generative-ai` sang `@google/genai` (phiên bản mới)
- ✅ Sử dụng model `gemini-2.0-flash-001` thay vì `gemini-pro` (deprecated)
- ✅ Pinecone index: 1024 dimensions
- ✅ Code embedding: 1024 dimensions
- ✅ Test thành công: Single food upload và search hoạt động

## 🔧 Cấu hình nhanh

### 1. Thêm API Keys vào `.env`:
```env
# AI Menu Generator Configuration
GEMINI_API_KEY=your-gemini-api-key-here
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_ENVIRONMENT=aped-4627-b74a
PINECONE_INDEX_NAME=foodinfo
```

### 2. Lấy API Keys:

**Google Gemini API:**
1. Truy cập: https://makersuite.google.com/app/apikey
2. Tạo API key mới
3. Copy vào `GEMINI_API_KEY`

**Pinecone API (bạn đã có):**
- API Key: Từ Pinecone dashboard
- Environment: `aped-4627-b74a`
- Index Name: `foodinfo`

## 🧪 Test ngay

### Cách 1: Test New Gemini API (Mới!)
```bash
# Test API mới @google/genai
node test/test-new-gemini-api.js
```

### Cách 2: Test qua Browser (Khuyến nghị)
1. Khởi động server: `npm start`
2. Login với tài khoản admin
3. Truy cập: `http://localhost:4000/test/ai-menu`
4. Click "Test All Connections" để kiểm tra
5. Click "Sync Foods" để đồng bộ dữ liệu
6. Test các thực đơn mẫu

### Cách 3: Test qua Command Line
```bash
# Test Pinecone embedding (đã pass)
node test/test-pinecone-sync.js

# Test API (cần authentication)
node test/test-api-sync.js
```

## 🎯 Sử dụng ngay

### 1. Đồng bộ dữ liệu (lần đầu):
- Vào `/test/ai-menu` (admin only)
- Click "Sync All Foods"
- Chờ hoàn thành

### 2. Tạo thực đơn AI:
- Vào trang "Khẩu phần ăn"
- Click nút robot 🤖
- Nhập yêu cầu: "Tạo thực đơn cho bệnh nhân tiểu đường, 1800 kcal"
- Click "Tạo thực đơn AI"
- Xem kết quả và click "Sử dụng thực đơn này"

### 3. Test các mẫu có sẵn:
- Thực đơn cân bằng (2000 kcal)
- Thực đơn tiểu đường (1800 kcal) 
- Thực đơn giảm cân (1500 kcal)

## 📊 Kết quả Test

**✅ Đã hoạt động:**
- Pinecone service initialization
- Embedding generation (1024 dimensions)
- Single food upload to Pinecone
- Food search in Pinecone
- Vector similarity matching

**⚠️ Cần authentication để test:**
- Database sync với Pinecone
- Full AI menu generation
- API endpoints

## 🔍 Troubleshooting

### Nếu sync-foods vẫn báo lỗi:
1. **Kiểm tra API keys** trong `.env`
2. **Restart server** sau khi thêm API keys
3. **Test connection** tại `/test/ai-menu`
4. **Kiểm tra Pinecone quota** (free tier có giới hạn)

### Nếu AI không tạo thực đơn:
1. **Kiểm tra Gemini API key** (đã cập nhật cho @google/genai)
2. **Đảm bảo đã sync foods** trước
3. **Thử với yêu cầu đơn giản** trước
4. **Chạy test**: `node test/test-new-gemini-api.js`

### Nếu không tìm thấy thực phẩm:
1. **Chạy sync foods** trước
2. **Kiểm tra database** có dữ liệu food_info
3. **Test search** với từ khóa đơn giản như "cơm"

## 🎉 Tính năng hoạt động

**✅ Vector Search:**
- Tìm thực phẩm theo ngữ nghĩa
- Matching dựa trên dinh dưỡng
- Similarity scoring

**✅ AI Menu Generation:**
- Prompt engineering cho thực đơn
- Nutrition calculation
- Menu structure generation

**✅ Integration:**
- Seamless với hệ thống hiện tại
- Menu format compatibility
- User permission handling

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Check logs trong browser console
2. Test tại `/test/ai-menu`
3. Xem file `docs/AI_MENU_GENERATOR_README.md`

## 🔄 Thay đổi mới nhất

**Đã cập nhật từ @google/generative-ai sang @google/genai:**
- ✅ Gỡ bỏ package cũ: `npm uninstall @google/generative-ai`
- ✅ Cài đặt package mới: `npm install @google/genai`
- ✅ Cập nhật API calls trong `services/geminiService.js`
- ✅ Sử dụng model mới: `gemini-2.0-flash-001` thay vì `gemini-pro`
- ✅ Test thành công với script mới: `test/test-new-gemini-api.js`

**Lý do thay đổi:**
- Model `gemini-pro` đã deprecated trong API v1beta
- Package `@google/generative-ai` không còn được phát triển
- Package `@google/genai` là phiên bản mới nhất với Gemini 2.0 features

**Hệ thống đã sẵn sàng sử dụng! 🚀**
