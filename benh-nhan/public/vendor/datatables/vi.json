{"processing": "<PERSON><PERSON> xử lý...", "aria": {"sortAscending": ": <PERSON><PERSON><PERSON> xếp thứ tự tăng dần", "sortDescending": ": <PERSON><PERSON><PERSON> xếp thứ tự gi<PERSON>m dần"}, "autoFill": {"cancel": "<PERSON><PERSON><PERSON>", "fill": "<PERSON><PERSON><PERSON><PERSON> tất cả ô với <i>%d</i>", "fillHorizontal": "<PERSON><PERSON><PERSON><PERSON> theo hàng ngang", "fillVertical": "<PERSON><PERSON><PERSON><PERSON> theo hà<PERSON>c"}, "buttons": {"collection": "<PERSON><PERSON><PERSON> l<PERSON> <span class=\"ui-button-icon-primary ui-icon ui-icon-triangle-1-s\"></span>", "colvis": "<PERSON><PERSON><PERSON> thị theo cột", "colvisRestore": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>c hiển thị", "copy": "Sao chép", "copyKeys": "Nhấn Ctrl hoặc u2318 + C để sao chép bảng dữ liệu vào clipboard.<br /><br /><PERSON><PERSON> hủy, click vào thông báo này hoặc nhấn ESC", "copySuccess": {"1": "Đã sao chép 1 dòng dữ liệu vào clipboard", "_": "Đã sao chép %d dòng vào clipboard"}, "copyTitle": "Sao chép vào clipboard", "pageLength": {"-1": "<PERSON><PERSON> tất cả các dòng", "_": "Hiển thị %d dòng"}, "print": "In ấn", "createState": "<PERSON><PERSON>o trạng thái", "csv": "CSV", "excel": "Excel", "pdf": "PDF", "removeAllStates": "<PERSON><PERSON><PERSON> hết trạng thái", "removeState": "Xóa", "renameState": "<PERSON><PERSON><PERSON> tên", "savedStates": "<PERSON>r<PERSON>ng thái đã lưu", "stateRestore": "Trạng thái %d", "updateState": "<PERSON><PERSON><PERSON>"}, "infoThousands": "`", "select": {"cells": {"1": "1 ô đang đ<PERSON><PERSON><PERSON> ch<PERSON>n", "_": "%d ô đang đ<PERSON><PERSON><PERSON> ch<PERSON>n"}, "columns": {"1": "1 cột đang đ<PERSON><PERSON><PERSON> ch<PERSON>n", "_": "%d cột đang đ<PERSON><PERSON><PERSON> đ<PERSON><PERSON> ch<PERSON>n"}, "rows": {"1": "1 dòng đang đ<PERSON><PERSON><PERSON> ch<PERSON>n", "_": "%d dòng đang đ<PERSON><PERSON><PERSON> ch<PERSON>n"}}, "thousands": "`", "searchBuilder": {"title": {"_": "<PERSON><PERSON><PERSON><PERSON> lập tìm k<PERSON>ế<PERSON> (%d)", "0": "<PERSON><PERSON><PERSON><PERSON> lập tìm k<PERSON>ếm"}, "button": {"0": "<PERSON><PERSON><PERSON><PERSON> lập tìm k<PERSON>ếm", "_": "<PERSON><PERSON><PERSON><PERSON> lập tìm k<PERSON>ế<PERSON> (%d)"}, "value": "<PERSON><PERSON><PERSON> trị", "clearAll": "<PERSON><PERSON><PERSON>", "condition": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "conditions": {"date": {"after": "Sau", "before": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "between": "Nằm giữa", "empty": "Rỗng", "equals": "Bằng với", "not": "Không phải", "notBetween": "Không nằm giữa", "notEmpty": "Không rỗng"}, "number": {"between": "Nằm giữa", "empty": "Rỗng", "equals": "Bằng với", "gt": "<PERSON><PERSON><PERSON>", "gte": "Lớn hơn hoặc bằng", "lt": "Nhỏ hơn", "lte": "Nhỏ hơn hoặc bằng", "not": "Không phải", "notBetween": "Không nằm giữa", "notEmpty": "Không rỗng"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "Rỗng", "endsWith": "<PERSON><PERSON><PERSON> th<PERSON>c bằng", "equals": "Bằng", "not": "Không phải", "notEmpty": "Không rỗng", "startsWith": "<PERSON><PERSON><PERSON> đ<PERSON>u với", "notContains": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON>", "notEnds": "<PERSON><PERSON><PERSON><PERSON> kết thúc với", "notStarts": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t đầu với"}, "array": {"equals": "Bằng", "empty": "<PERSON><PERSON><PERSON><PERSON>", "contains": "<PERSON><PERSON><PERSON>", "not": "K<PERSON>ô<PERSON>", "notEmpty": "<PERSON><PERSON><PERSON><PERSON> được rỗng", "without": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>a"}}, "logicAnd": "Và", "logicOr": "Hoặc", "add": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u kiện", "data": "<PERSON><PERSON> liệu", "deleteTitle": "<PERSON><PERSON><PERSON> quy tắc lọc", "leftTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "rightTitle": "<PERSON><PERSON><PERSON> th<PERSON>t l<PERSON>"}, "searchPanes": {"countFiltered": "{shown} ({total})", "emptyPanes": "<PERSON><PERSON><PERSON><PERSON> có phần tìm kiếm", "clearMessage": "<PERSON><PERSON><PERSON>", "loadMessage": "<PERSON><PERSON> load ph<PERSON>n tìm kiếm", "collapse": {"0": "<PERSON><PERSON><PERSON> t<PERSON>m k<PERSON>m", "_": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> k<PERSON> (%d)"}, "title": "<PERSON><PERSON> lọc đang hoạt động - %d", "count": "{total}", "collapseMessage": "<PERSON><PERSON> g<PERSON>n tất cả", "showMessage": "<PERSON><PERSON><PERSON> tất cả"}, "datetime": {"hours": "Giờ", "minutes": "<PERSON><PERSON><PERSON>", "next": "Sau", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seconds": "Giây", "amPm": ["am", "pm"], "unknown": "-", "weekdays": ["<PERSON>ủ <PERSON>h<PERSON>"]}, "emptyTable": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "info": "<PERSON><PERSON><PERSON> thị _START_ tới _END_ của _TOTAL_ dữ liệu", "infoEmpty": "Hiển thị 0 tới 0 của 0 dữ liệu", "lengthMenu": "<PERSON><PERSON><PERSON> thị _MENU_ <PERSON><PERSON> liệu", "loadingRecords": "<PERSON><PERSON> tả<PERSON>...", "paginate": {"first": "<PERSON><PERSON><PERSON> tiên", "last": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "next": "Sau", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "search": "<PERSON><PERSON><PERSON> k<PERSON>m:", "zeroRecords": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "decimal": ",", "editor": {"close": "Đ<PERSON><PERSON>", "create": {"button": "<PERSON><PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> mục mới"}, "edit": {"button": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "error": {"system": "<PERSON><PERSON> xảy ra lỗi hệ thống (&lt;a target=\"\\\" rel=\"nofollow\" href=\"\\\"&gt;Thêm thông tin&lt;/a&gt;)."}, "multi": {"info": "<PERSON><PERSON><PERSON> mục đã chọn chứa các giá trị khác nhau cho đầu vào này. Để chỉnh sửa và đặt tất cả các mục cho đầu vào này thành cùng một giá trị, h<PERSON><PERSON> nhấp hoặc nhấn vào đây, nế<PERSON> không chúng sẽ giữ lại các giá trị riêng lẻ của chúng.", "noMulti": "<PERSON><PERSON><PERSON> vào này có thể được chỉnh sửa riêng lẻ, nhưng không phải là một phần của một nhóm.", "restore": "<PERSON><PERSON><PERSON> tác thay đổi", "title": "<PERSON><PERSON>ều giá trị"}, "remove": {"button": "Xóa", "confirm": {"_": "Bạn có chắc chắn muốn xóa %d hàng không?", "1": "Bạn có chắc chắn muốn xóa 1 hàng không?"}, "submit": "Xóa", "title": "Xóa"}}, "infoFiltered": "(<PERSON><PERSON><PERSON><PERSON> l<PERSON>c từ _MAX_ dữ liệu)", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tìm k<PERSON>ế<PERSON>...", "stateRestore": {"creationModal": {"button": "<PERSON><PERSON><PERSON><PERSON>", "columns": {"search": "<PERSON><PERSON><PERSON> k<PERSON> c<PERSON>t", "visible": "<PERSON><PERSON><PERSON> năng hiển thị cột"}, "name": "Tên:", "order": "<PERSON><PERSON><PERSON>p", "paging": "<PERSON><PERSON> trang", "scroller": "Cuộn vị trí", "search": "<PERSON><PERSON><PERSON>", "searchBuilder": "<PERSON><PERSON><PERSON><PERSON> tạo tìm kiếm", "select": "<PERSON><PERSON><PERSON>", "title": "<PERSON>hêm trạng thái", "toggleLabel": "<PERSON><PERSON> gồ<PERSON>:"}, "duplicateError": "Trạng thái có tên này đã tồn tại.", "emptyError": "<PERSON><PERSON><PERSON> không đư<PERSON><PERSON> để trống.", "emptyStates": "<PERSON><PERSON><PERSON><PERSON> có trạng thái đã lưu", "removeConfirm": "Bạn có chắc chắn muốn xóa %s không?", "removeError": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>a đ<PERSON> trạng thái.", "removeJoiner": "và", "removeSubmit": "Xóa", "removeTitle": "<PERSON>óa trạng thái", "renameButton": "<PERSON><PERSON><PERSON> tên", "renameLabel": "Tên mới cho %s:", "renameTitle": "<PERSON><PERSON>i tên trạng thái"}}