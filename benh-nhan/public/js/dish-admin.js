// JavaScript cho quản lý món ăn
// Biến global để lưu danh sách thực phẩm trong món ăn
let dishFoods = [];
let foodSelectInstance = null;

$(document).ready(function() {
    // Khởi tạo Virtual Select cho thực phẩm
    initFoodSelect();
    
    // Khởi tạo autocomplete cho tên món ăn
    initDishNameAutocomplete();
    
    // Load dữ liệu hiện tại nếu đang edit
    if (window.dishData && window.dishData.isEdit && window.dishData.existingFoods.length > 0) {
        dishFoods = window.dishData.existingFoods.map(food => ({
            food_id: food.food_id,
            food_name: food.food_name,
            food_code: food.food_code,
            food_type: food.food_type,
            food_type_year: food.food_type_year,
            weight: parseFloat(food.weight),
            food_energy: parseFloat(food.food_energy) || 0,
            food_protein: parseFloat(food.food_protein) || 0,
            food_fat: parseFloat(food.food_fat) || 0,
            calculated_energy: parseFloat(food.calculated_energy) || 0,
            calculated_protein: parseFloat(food.calculated_protein) || 0,
            calculated_fat: parseFloat(food.calculated_fat) || 0
        }));
        updateFoodTable();
    }

    // Event listeners cho filter
    $('#foodType, #foodYear').on('change', function() {
        updateFoodSelect();
    });
});

function initDishNameAutocomplete() {
    try {
        const dishNameInput = $('#dishName');
        if (dishNameInput.length === 0) {
            console.error('Element #dishName không tồn tại!');
            return;
        }

        // Tạo dropdown container
        const dropdownContainer = $('<div class="dish-suggestions-dropdown"></div>');
        dishNameInput.after(dropdownContainer);

        let searchTimeout;
        let currentSuggestions = [];

        // Xử lý khi user nhập
        dishNameInput.on('input', function() {
            const query = $(this).val().trim();
            
            // Clear timeout cũ
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Ẩn dropdown nếu query quá ngắn
            if (query.length < 2) {
                hideSuggestions();
                return;
            }

            // Debounce search
            searchTimeout = setTimeout(() => {
                searchDishes(query);
            }, 300);
        });

        // Ẩn dropdown khi click ra ngoài
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#dishName, .dish-suggestions-dropdown').length) {
                hideSuggestions();
            }
        });

        // Xử lý khi focus vào input
        dishNameInput.on('focus', function() {
            const query = $(this).val().trim();
            if (query.length >= 2 && currentSuggestions.length > 0) {
                showSuggestions(currentSuggestions);
            }
        });

        function searchDishes(query) {
            $.ajax({
                url: '/admin/api/search-dishes',
                type: 'GET',
                data: { q: query },
                success: function(response) {
                    if (response.success && response.data && response.data.length > 0) {
                        currentSuggestions = response.data;
                        showSuggestions(currentSuggestions);
                    } else {
                        hideSuggestions();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Search dishes error:', error);
                    hideSuggestions();
                }
            });
        }

        function showSuggestions(suggestions) {
            let html = '<div class="suggestion-list">';
            suggestions.forEach(dish => {
                const categoryText = dish.category ? ` (${dish.category})` : '';
                const shareText = dish.share == 1 ? '<i class="fas fa-share-alt text-success" title="Được chia sẻ"></i>' : '';
                html += `
                    <div class="suggestion-item" data-dish-name="${dish.name}">
                        <div class="suggestion-name">
                            ${dish.name}${categoryText}
                            ${shareText}
                        </div>
                        <div class="suggestion-description">
                            ${dish.description || 'Không có mô tả'}
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            dropdownContainer.html(html).show();

            // Xử lý click vào suggestion
            dropdownContainer.find('.suggestion-item').on('click', function() {
                const dishName = $(this).data('dish-name');
                dishNameInput.val(dishName);
                hideSuggestions();
                
                // Hiển thị thông báo gợi ý
                toarstWarning(`Gợi ý: Món ăn "${dishName}" đã tồn tại. Bạn có thể chỉnh sửa hoặc tạo món ăn mới với tên khác.`);
            });
        }

        function hideSuggestions() {
            dropdownContainer.hide().empty();
        }
    } catch (error) {
        console.error('Error initializing dish name autocomplete:', error);
    }
}

function initFoodSelect() {
    try {
        // Kiểm tra element tồn tại
        const selectElement = document.querySelector('#foodSelect');
        if (!selectElement) {
            console.error('Element #foodSelect không tồn tại!');
            return;
        }
 
        // Khởi tạo Virtual Select với API search
        VirtualSelect.init({
            ele: '#foodSelect',
            placeholder: 'Tìm kiếm thực phẩm...',
            search: true,
            searchPlaceholderText: 'Nhập tên thực phẩm (tối thiểu 2 ký tự)...',
            noSearchResultsText: 'Không tìm thấy thực phẩm nào',
            noOptionsText: 'Nhập từ khóa để tìm kiếm',
            optionsCount: 10,
            onServerSearch: function(search, virtualSelect) {
                
                if (search.length < 2) {
                    virtualSelect.setServerOptions([]);
                    return;
                }

                const type = $('#foodType').val();
                const type_year = $('#foodYear').val();
                $.ajax({
                    url: '/api/food-search',
                    type: 'GET',
                    data: {
                        search: search,
                        type: type,
                        type_year: type_year
                    },
                    success: function(response) {
                        
                        if (response.success && response.data) {
                            const options = response.data;
                            virtualSelect.setServerOptions(options);
                        } else {
                            virtualSelect.setServerOptions([]);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('API error:', error);
                        virtualSelect.setServerOptions([]);
                    }
                });
            }
        });
        
        foodSelectInstance = document.querySelector('#foodSelect');
        
    } catch (error) {
        console.error('Error initializing Virtual Select:', error);
    }
}

function updateFoodSelect() {
    // Reset search khi thay đổi filter
    if (foodSelectInstance) {
        foodSelectInstance.reset();
    }
}

function addFoodToDish() {
    try {
        // Kiểm tra foodSelectInstance tồn tại
        if (!foodSelectInstance) {
            toarstError('Lỗi hệ thống: Virtual Select chưa được khởi tạo!');
            return;
        }

        // Lấy thông tin đã chọn
        const selectedOptions = foodSelectInstance.getSelectedOptions();
        const weightInput = $('#foodWeight').val();
        // Kiểm tra đã chọn thực phẩm chưa
        if (!selectedOptions || selectedOptions.length === 0) {
            toarstError('Vui lòng chọn thực phẩm!');
            $('#foodSelect').focus();
            return;
        }

        // Kiểm tra khối lượng
        if (!weightInput || weightInput.trim() === '') {
            toarstError('Vui lòng nhập khối lượng!');
            $('#foodWeight').focus();
            return;
        }

        const weight = parseInt(weightInput);
        if (isNaN(weight) || weight <= 0) {
            toarstError('Khối lượng phải là số dương! Ví dụ: 100, 150.5');
            $('#foodWeight').focus();
            return;
        }

        // Kiểm tra dữ liệu thực phẩm
        const foodData = selectedOptions.customData;
        if (!foodData || !foodData.id) {
            toarstError('Dữ liệu thực phẩm không hợp lệ!');
            return;
        }
        
        // Kiểm tra trùng lặp
        const existingIndex = dishFoods.findIndex(f => f.food_id == foodData.id);
        if (existingIndex !== -1) {
            toarstWarning('Thực phẩm "' + foodData.name + '" đã có trong danh sách!');
            return;
        }

        const foodEnergy = parseNumber(foodData.energy) || 0;
        const foodProtein = parseNumber(foodData.protein) || 0;
        const foodFat = parseNumber(foodData.fat) || 0;
        const ratio = weight / parseNumber(foodData.weight) || 0;
        // Thêm vào danh sách
        const newFood = {
            food_id: foodData.id,
            food_name: foodData.name || 'Không rõ tên',
            food_code: foodData.code || '',
            food_type: foodData.type || 'raw',
            food_type_year: foodData.type_year || '2017',
            weight: weight,
            food_energy: foodEnergy,
            food_protein: foodProtein,
            food_fat: foodFat,
            calculated_energy: foodEnergy * ratio, 
            calculated_protein: foodProtein * ratio,
            calculated_fat: foodFat * ratio
        };
        dishFoods.push(newFood);

        // Cập nhật bảng
        updateFoodTable();

        // Reset form
        foodSelectInstance.reset();
        $('#foodWeight').val('');
        
        toarstMessage('Đã thêm "' + foodData.name + '" vào món ăn!');
    } catch (error) {
        console.error('Error in addFoodToDish:', error);
        toarstError('Có lỗi xảy ra khi thêm thực phẩm!');
    }
}

function removeFoodFromDish(index) {
    dishFoods.splice(index, 1);
    updateFoodTable();
    toarstMessage('Đã xóa thực phẩm khỏi món ăn!');
}

function toggleWeightEdit(index) {
    const row = $(`#dishFoodsBody tr:eq(${index})`);
    const weightInput = row.find('.weight-input');
    const weightDisplay = row.find('.weight-display');
    const editBtn = row.find('button[onclick*="toggleWeightEdit"]');
    
    if (weightInput.is(':visible')) {
        // Đang ở chế độ edit, chuyển về hiển thị
        const newWeight = parseFloat(weightInput.val());
        if (isNaN(newWeight) || newWeight <= 0) {
            toarstError('Khối lượng phải là số dương!');
            return;
        }
        
        // Cập nhật khối lượng và tính lại các chất dinh dưỡng
        updateFoodWeight(index, newWeight);
        
        weightInput.hide();
        weightDisplay.show();
        editBtn.html('<i class="fas fa-edit"></i>');
        editBtn.attr('title', 'Sửa khối lượng');
    } else {
        // Chuyển sang chế độ edit
        weightInput.show();
        weightDisplay.hide();
        weightInput.focus();
        editBtn.html('<i class="fas fa-check"></i>');
        editBtn.attr('title', 'Xác nhận');
    }
}

function updateFoodWeight(index, newWeight) {
    if (index < 0 || index >= dishFoods.length) {
        console.error('Invalid food index:', index);
        return;
    }
    
    const newWeightNum = parseFloat(newWeight);
    if (isNaN(newWeightNum) || newWeightNum <= 0) {
        toarstError('Khối lượng phải là số dương!');
        return;
    }
    
    const food = dishFoods[index];
    const oldWeight = food.weight;
    
    // Tính tỉ lệ dựa trên khối lượng gốc của thực phẩm (100g)
    // Vì food_energy, food_protein, food_fat là giá trị trên 100g
    const ratio = newWeightNum / 100;
    
    // Cập nhật khối lượng
    food.weight = newWeightNum;
    
    // Tính lại các chất dinh dưỡng theo tỉ lệ với 100g
    food.calculated_energy = food.food_energy * ratio;
    food.calculated_protein = food.food_protein * ratio;
    food.calculated_fat = food.food_fat * ratio;
    
    // Cập nhật hiển thị trong bảng
    const row = $(`#dishFoodsBody tr:eq(${index})`);
    row.find('.weight-display').text(newWeightNum);
    row.find('.nutrition-value').eq(1).text(food.calculated_energy.toFixed(1)); // Năng lượng
    row.find('.nutrition-value').eq(2).text(food.calculated_protein.toFixed(1)); // Protein
    row.find('.nutrition-value').eq(3).text(food.calculated_fat.toFixed(1)); // Lipid
    
    // Cập nhật tổng
    updateTotals();
    
    toarstMessage(`Đã cập nhật khối lượng từ ${oldWeight}g thành ${newWeightNum}g`);
}

function updateFoodTable() {
    const tbody = $('#dishFoodsBody');
    const emptyMessage = $('#emptyMessage');
    
    if (dishFoods.length === 0) {
        tbody.empty();
        emptyMessage.show();
        $('#totalWeight').text('0');
        $('#totalEnergy').text('0');
        return;
    }

    emptyMessage.hide();
    
    let html = '';
    let totalWeight = 0;
    let totalEnergy = 0;

    dishFoods.forEach((food, index) => {
        totalWeight += food.weight;
        totalEnergy += food.calculated_energy;
        
        html += '<tr>';
        html += '<td>' + (index + 1) + '</td>';
        html += '<td>' + food.food_name + '</td>';
        html += '<td>' + (food.food_code || '') + '</td>';
        html += '<td><span class="food-type-badge ' + (food.food_type === 'raw' ? 'food-type-raw' : 'food-type-cooked') + '">' + textTypeFood(food.food_type) + ' (' + food.food_type_year + ')</span></td>';
        html += '<td class="nutrition-value">';
        html += '<div class="weight-edit-container">';
        html += '<input type="number" class="form-control form-control-sm weight-input" ';
        html += 'value="' + food.weight + '" min="0.1" step="0.1" ';
        html += 'data-index="' + index + '" data-original-weight="' + food.weight + '" ';
        html += 'onchange="updateFoodWeight(' + index + ', this.value)" ';
        html += 'style="display: none;">';
        html += '<span class="weight-display">' + food.weight + '</span>';
        html += '</div>';
        html += '</td>';
        html += '<td class="nutrition-value">' + food.calculated_energy.toFixed(1) + '</td>';
        html += '<td class="nutrition-value">' + food.calculated_protein.toFixed(1) + '</td>';
        html += '<td class="nutrition-value">' + food.calculated_fat.toFixed(1) + '</td>';
        html += '<td>';
        html += '<button class="btn btn-sm btn-warning me-1" onclick="toggleWeightEdit(' + index + ')" title="Sửa khối lượng"><i class="fas fa-edit"></i></button>';
        html += '<button class="btn btn-sm btn-danger" onclick="removeFoodFromDish(' + index + ')" title="Xóa"><i class="fas fa-trash"></i></button>';
        html += '</td>';
        html += '</tr>';
    });

    tbody.html(html);
    $('#totalWeight').text(totalWeight.toFixed(1));
    $('#totalEnergy').text(totalEnergy.toFixed(1));
}

function updateTotals() {
    let totalWeight = 0;
    let totalEnergy = 0;
    
    dishFoods.forEach(food => {
        totalWeight += food.weight;
        totalEnergy += food.calculated_energy;
    });
    
    $('#totalWeight').text(totalWeight.toFixed(1));
    $('#totalEnergy').text(totalEnergy.toFixed(1));
}

function textTypeFood(type) {
    switch(type) {
        case 'raw':
            return 'Sống';
        case 'cooked':
            return 'Chín ĐP';
        case 'cooked_vdd':
            return 'Chín VDD';
        case 'milk':
            return 'Sữa';
        case 'ddd':
            return 'Dịch DD';
        default:
            return '';
    }
}

function saveDish() {
    try {
        // Validate
        const dishName = $('#dishName').val();
        const dishCategory = $('#dishCategory').val();
        const dishDescription = $('#dishDescription').val();
        const dishId = $('#dishId').val();
        
        if (!dishName || dishName.trim() === '') {
            toarstError('Vui lòng nhập tên món ăn!');
            $('#dishName').focus();
            return;
        }

        if (dishFoods.length === 0) {
            toarstError('Vui lòng thêm ít nhất một thực phẩm vào món ăn!');
            return;
        }

        // Lấy giá trị checkbox share
        const dishShare = $('#dishShare').is(':checked') ? 1 : 0;
        
        // Chuẩn bị dữ liệu gửi lên server
        const dataToSend = {
            name: dishName.trim(),
            category: dishCategory || '',
            description: dishDescription || '',
            share: dishShare,
            dish_foods: JSON.stringify(dishFoods)
        };
        
        // Thêm ID nếu đang edit
        if (dishId) {
            dataToSend.id = dishId;
        }

        // Disable button
        const saveBtn = event.target;
        const originalText = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang lưu...';

        $.ajax({
            url: '/admin/mon-an/upsert',
            type: 'POST',
            data: dataToSend,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    toarstMessage(response.message);
                    setTimeout(() => {
                        window.location.href = '/admin/mon-an';
                    }, 1500);
                } else {
                    toarstError(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Save error:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                toarstError('Có lỗi xảy ra khi lưu món ăn!');
            },
            complete: function() {
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
            }
        });
    } catch (error) {
        console.error('Error in saveDish:', error);
        toarstError('Có lỗi xảy ra trong quá trình xử lý!');
    }
}