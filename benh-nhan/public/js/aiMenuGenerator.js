// AI Menu Generator JavaScript
class AIMenuGenerator {
    constructor() {
        this.isGenerating = false;
        this.currentMenu = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSampleTypes();
    }

    bindEvents() {
        // Generate menu button
        $(document).on('click', '#generateAIMenu', () => {
            this.generateMenu();
        });

        // Generate sample menu buttons
        $(document).on('click', '.generate-sample-btn', (e) => {
            const type = $(e.target).data('type');
            this.generateSampleMenu(type);
        });

        // Use AI menu button
        $(document).on('click', '#useAIMenu', () => {
            this.useGeneratedMenu();
        });

        // Clear form button
        $(document).on('click', '#clearAIForm', () => {
            this.clearForm();
        });

        // Sync foods button (admin only)
        $(document).on('click', '#syncFoodsBtn', () => {
            this.syncFoods();
        });

        // Sync dishes button (admin only)
        $(document).on('click', '#syncDishesBtn', () => {
            this.syncDishes();
        });

        // Test connection button (admin only)
        $(document).on('click', '#testConnectionBtn', () => {
            this.testConnection();
        });

        // Add preference tag
        $(document).on('click', '#addPreference', () => {
            this.addPreferenceTag();
        });

        // Add restriction tag
        $(document).on('click', '#addRestriction', () => {
            this.addRestrictionTag();
        });

        // Remove tag
        $(document).on('click', '.remove-tag', (e) => {
            $(e.target).closest('.tag').remove();
        });

        // Enter key in tag inputs
        $(document).on('keypress', '#preferenceInput, #restrictionInput', (e) => {
            if (e.which === 13) {
                if (e.target.id === 'preferenceInput') {
                    this.addPreferenceTag();
                } else {
                    this.addRestrictionTag();
                }
            }
        });
    }

    loadSampleTypes() {
        const sampleTypes = [
            { type: 'balanced', name: 'Cân bằng dinh dưỡng', description: 'Thực đơn cân bằng cho người khỏe mạnh' },
            { type: 'diabetes', name: 'Tiểu đường', description: 'Ít đường, nhiều chất xơ' },
            { type: 'weight_loss', name: 'Giảm cân', description: 'Ít calo, nhiều protein' }
        ];

        const container = $('#sampleMenuTypes');
        container.empty();

        sampleTypes.forEach(sample => {
            const html = `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6 class="card-title">${sample.name}</h6>
                            <p class="card-text small text-muted">${sample.description}</p>
                            <button type="button" class="btn btn-outline-primary btn-sm generate-sample-btn" data-type="${sample.type}">
                                <i class="fas fa-magic"></i> Tạo mẫu
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.append(html);
        });
    }

    async generateMenu() {
        if (this.isGenerating) return;

        const requirements = $('#aiRequirements').val().trim();
        if (!requirements) {
            toarstError('Vui lòng nhập yêu cầu tạo thực đơn');
            return;
        }

        this.isGenerating = true;
        this.updateGenerateButton(true);

        try {
            const requestData = {
                requirements: requirements,
                preferences: this.getTagValues('.preference-tag'),
                restrictions: this.getTagValues('.restriction-tag'),
                meal_count: parseInt($('#mealCount').val()) || 3,
                target_nutrition: this.getTargetNutrition()
            };

            console.log('Generating AI menu with request:', requestData);

            const response = await fetch('/api/ai-menu/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.success) {
                this.currentMenu = result.data;
                this.displayGeneratedMenu(result.data);
                toarstMessage('Tạo thực đơn AI thành công!');
            } else {
                toarstError(result.message || 'Không thể tạo thực đơn AI');
            }

        } catch (error) {
            console.error('Error generating AI menu:', error);
            toarstError('Có lỗi xảy ra khi tạo thực đơn AI');
        } finally {
            this.isGenerating = false;
            this.updateGenerateButton(false);
        }
    }

    async generateSampleMenu(type) {
        if (this.isGenerating) return;

        this.isGenerating = true;
        this.updateSampleButtons(true);

        try {
            const response = await fetch(`/api/ai-menu/sample?type=${type}`);
            const result = await response.json();

            if (result.success) {
                this.currentMenu = result.data;
                this.displayGeneratedMenu(result.data);
                toarstMessage(`Tạo thực đơn mẫu ${type} thành công!`);
            } else {
                toarstError(result.message || 'Không thể tạo thực đơn mẫu');
            }

        } catch (error) {
            console.error('Error generating sample menu:', error);
            toarstError('Có lỗi xảy ra khi tạo thực đơn mẫu');
        } finally {
            this.isGenerating = false;
            this.updateSampleButtons(false);
        }
    }

    displayGeneratedMenu(menuData) {
        const { menu, nutrition_summary } = menuData;
        
        // Display menu structure
        const menuHtml = this.renderMenuHTML(menu);
        $('#aiMenuResult').html(menuHtml).show();

        // Display nutrition summary
        const nutritionHtml = this.renderNutritionSummary(nutrition_summary);
        $('#nutritionSummary').html(nutritionHtml).show();

        // Show use menu button
        $('#useAIMenu').show();

        // Scroll to result
        $('html, body').animate({
            scrollTop: $('#aiMenuResult').offset().top - 100
        }, 500);
    }

    renderMenuHTML(menu) {
        let html = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-robot text-primary"></i> ${menu.name}
                        <span class="badge badge-success ml-2">AI Generated</span>
                    </h5>
                    <small class="text-muted">Tạo lúc: ${new Date(menu.created_at).toLocaleString('vi-VN')}</small>
                </div>
                <div class="card-body">
        `;

        if (menu.requirements) {
            html += `
                <div class="alert alert-info">
                    <strong>Yêu cầu:</strong> ${menu.requirements}
                </div>
            `;
        }

        menu.detail.forEach(meal => {
            html += `
                <div class="meal-section mb-4">
                    <h6 class="text-primary border-bottom pb-2">
                        <i class="fas fa-utensils"></i> ${meal.name}
                    </h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Thực phẩm</th>
                                    <th>Khối lượng (g)</th>
                                    <th>Năng lượng (kcal)</th>
                                    <th>Protein (g)</th>
                                    <th>Carb (g)</th>
                                    <th>Fat (g)</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            meal.listFood.forEach(food => {
                html += `
                    <tr>
                        <td>${food.name}</td>
                        <td>${food.weight}</td>
                        <td>${food.energy || 0}</td>
                        <td>${food.protein || 0}</td>
                        <td>${food.carbohydrate || 0}</td>
                        <td>${food.fat || 0}</td>
                    </tr>
                `;
            });

            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    renderNutritionSummary(nutrition) {
        return `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Tổng hợp dinh dưỡng</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">${nutrition.total_energy || 0}</h4>
                                <small>Năng lượng (kcal)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">${nutrition.total_protein || 0}</h4>
                                <small>Protein (g)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">${nutrition.total_carbohydrate || 0}</h4>
                                <small>Carbohydrate (g)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">${nutrition.total_lipid || 0}</h4>
                                <small>Lipid (g)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    useGeneratedMenu() {
        if (!this.currentMenu || !this.currentMenu.menu) {
            toarstError('Không có thực đơn nào để sử dụng');
            return;
        }

        try {
            // Tích hợp với hệ thống thực đơn hiện tại
            if (typeof window.addAIMenuToSystem === 'function') {
                window.addAIMenuToSystem(this.currentMenu.menu);
                toarstMessage('Đã thêm thực đơn AI vào hệ thống');
            } else if (typeof window.menuExamine !== 'undefined') {
                // Tích hợp trực tiếp với menuExamine
                const aiMenu = {
                    id: window.menuExamine.length + 1,
                    name: this.currentMenu.menu.name,
                    detail: this.currentMenu.menu.detail,
                    created_at: this.currentMenu.menu.created_at,
                    ai_generated: true
                };

                window.menuExamine.push(aiMenu);

                // Refresh table nếu có
                if (typeof generateTableMenu === 'function') {
                    generateTableMenu(aiMenu.id);
                }

                toarstMessage('Đã thêm thực đơn AI vào hệ thống');
            } else {
                // Fallback: copy menu structure
                console.log('AI Menu to be integrated:', this.currentMenu.menu);
                toarstInfo('Thực đơn AI đã sẵn sàng để tích hợp');
            }
        } catch (error) {
            console.error('Error using AI menu:', error);
            toarstError('Có lỗi khi tích hợp thực đơn AI');
        }
    }

    getTagValues(selector) {
        const values = [];
        $(selector).each(function() {
            const text = $(this).find('.tag-text').text().trim();
            if (text) values.push(text);
        });
        return values;
    }

    getTargetNutrition() {
        const energy = parseInt($('#targetEnergy').val()) || null;
        const protein = parseInt($('#targetProtein').val()) || null;
        const carbohydrate = parseInt($('#targetCarbohydrate').val()) || null;
        const lipid = parseInt($('#targetLipid').val()) || null;

        if (!energy && !protein && !carbohydrate && !lipid) {
            return null;
        }

        return { energy, protein, carbohydrate, lipid };
    }

    addPreferenceTag() {
        const input = $('#preferenceInput');
        const value = input.val().trim();
        if (!value) return;

        const tagHtml = `
            <span class="tag preference-tag badge badge-primary mr-1 mb-1">
                <span class="tag-text">${value}</span>
                <button type="button" class="btn btn-sm remove-tag ml-1" style="border:none;background:none;color:white;padding:0;">×</button>
            </span>
        `;
        
        $('#preferenceTags').append(tagHtml);
        input.val('');
    }

    addRestrictionTag() {
        const input = $('#restrictionInput');
        const value = input.val().trim();
        if (!value) return;

        const tagHtml = `
            <span class="tag restriction-tag badge badge-danger mr-1 mb-1">
                <span class="tag-text">${value}</span>
                <button type="button" class="btn btn-sm remove-tag ml-1" style="border:none;background:none;color:white;padding:0;">×</button>
            </span>
        `;
        
        $('#restrictionTags').append(tagHtml);
        input.val('');
    }

    clearForm() {
        $('#aiRequirements').val('');
        $('#preferenceInput').val('');
        $('#restrictionInput').val('');
        $('#preferenceTags').empty();
        $('#restrictionTags').empty();
        $('#mealCount').val(3);
        $('#targetEnergy, #targetProtein, #targetCarbohydrate, #targetLipid').val('');
        $('#aiMenuResult, #nutritionSummary').hide();
        $('#useAIMenu').hide();
        this.currentMenu = null;
    }

    updateGenerateButton(isLoading) {
        const btn = $('#generateAIMenu');
        if (isLoading) {
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang tạo thực đơn...');
        } else {
            btn.prop('disabled', false).html('<i class="fas fa-magic"></i> Tạo thực đơn AI');
        }
    }

    updateSampleButtons(isLoading) {
        const btns = $('.generate-sample-btn');
        if (isLoading) {
            btns.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang tạo...');
        } else {
            btns.prop('disabled', false).html('<i class="fas fa-magic"></i> Tạo mẫu');
        }
    }

    async syncFoods() {
        if (this.isGenerating) return;

        if (!confirm('Bạn có chắc muốn đồng bộ tất cả dữ liệu thực phẩm lên Pinecone? Quá trình này có thể mất vài phút.')) {
            return;
        }

        const btn = $('#syncFoodsBtn');
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang đồng bộ...');

        try {
            const response = await fetch('/api/ai-menu/sync-foods', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            console.log('Sync foods result:', result);
            if (result.success) {
                toarstMessage('Đồng bộ dữ liệu thực phẩm thành công!');
            } else {
                toarstError(result.message || 'Không thể đồng bộ dữ liệu thực phẩm');
            }

        } catch (error) {
            console.error('Error syncing foods:', error);
            toarstError('Có lỗi xảy ra khi đồng bộ dữ liệu thực phẩm');
        } finally {
            btn.prop('disabled', false).html('<i class="fas fa-sync"></i> Đồng bộ dữ liệu thực phẩm');
        }
    }

    async syncDishes() {
        if (this.isGenerating) return;

        if (!confirm('Bạn có chắc muốn đồng bộ tất cả dữ liệu món ăn lên Pinecone? Quá trình này có thể mất vài phút.')) {
            return;
        }

        const btn = $('#syncDishesBtn');
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang đồng bộ...');

        try {
            const response = await fetch('/api/ai-menu/sync-dishes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            console.log('Sync dishes result:', result);
            if (result.success) {
                toarstMessage('Đồng bộ dữ liệu món ăn thành công!');
            } else {
                toarstError(result.message || 'Không thể đồng bộ dữ liệu món ăn');
            }

        } catch (error) {
            console.error('Error syncing dishes:', error);
            toarstError('Có lỗi xảy ra khi đồng bộ dữ liệu món ăn');
        } finally {
            btn.prop('disabled', false).html('<i class="fas fa-utensils"></i> Đồng bộ dữ liệu món ăn');
        }
    }

    async testConnection() {
        const btn = $('#testConnectionBtn');
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang test...');

        try {
            const response = await fetch('/api/ai-menu/test');
            const result = await response.json();

            if (result.success) {
                const { data } = result;
                let message = 'Kết quả test kết nối:\n';
                message += `- AI Menu Service: ${data.aiMenuService ? '✓' : '✗'}\n`;
                message += `- Pinecone: ${data.pinecone ? '✓' : '✗'}\n`;
                message += `- Gemini AI: ${data.gemini ? '✓' : '✗'}`;
                
                alert(message);
            } else {
                toarstError(result.message || 'Không thể test kết nối');
            }

        } catch (error) {
            console.error('Error testing connection:', error);
            toarstError('Có lỗi xảy ra khi test kết nối');
        } finally {
            btn.prop('disabled', false).html('<i class="fas fa-plug"></i> Test kết nối');
        }
    }
}

// Initialize when document is ready
$(document).ready(function() {
    if (typeof window.aiMenuGenerator === 'undefined') {
        window.aiMenuGenerator = new AIMenuGenerator();
    }
});
