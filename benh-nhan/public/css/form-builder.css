/* Form Builder Styles */
.form-builder-container {
    display: flex;
    height: calc(100vh - 200px);
    gap: 20px;
    margin-top: 20px;
}

.form-builder-sidebar {
    width: 280px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;
}

.form-builder-canvas {
    flex: 1;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;
    position: relative;
}

.form-builder-preview {
    width: 350px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;
}

/* Field Types Palette */
.field-types {
    margin-bottom: 30px;
}

.field-types h5 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.field-type-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.2s ease;
}

.field-type-item:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.field-type-item.dragging {
    opacity: 0.5;
    cursor: grabbing;
}

.field-type-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    color: #6c757d;
}

.field-type-name {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

/* Form Canvas */
.form-canvas {
    min-height: 400px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    position: relative;
}

.form-canvas.drag-over {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.form-canvas-empty {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 60px 20px;
}

/* Form Fields */
.form-field {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid transparent;
    border-radius: 6px;
    position: relative;
    transition: all 0.2s ease;
}

.form-field:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.form-field.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-field.dragging {
    opacity: 0.5;
}

.field-controls {
    position: absolute;
    top: -10px;
    right: -10px;
    display: none;
    gap: 5px;
}

.form-field:hover .field-controls,
.form-field.selected .field-controls {
    display: flex;
}

.field-control-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    background: #007bff;
    color: white;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.field-control-btn:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.field-control-btn.delete {
    background: #dc3545;
}

.field-control-btn.delete:hover {
    background: #c82333;
}

/* Field Properties Panel */
.field-properties {
    margin-top: 30px;
}

.field-properties h5 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.property-group {
    margin-bottom: 20px;
}

.property-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 13px;
}

.property-group input,
.property-group select,
.property-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
}

.property-group textarea {
    resize: vertical;
    min-height: 60px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

/* Options List */
.options-list {
    margin-top: 10px;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.option-item input {
    flex: 1;
}

.option-item .btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.add-option-btn {
    width: 100%;
    padding: 8px;
    border: 1px dashed #ced4da;
    background: transparent;
    color: #6c757d;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
}

.add-option-btn:hover {
    border-color: #007bff;
    color: #007bff;
}

/* Preview Panel */
.preview-panel h5 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.preview-form {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.preview-field {
    margin-bottom: 20px;
}

.preview-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.preview-field .required {
    color: #dc3545;
}

.preview-field input,
.preview-field select,
.preview-field textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.preview-field .form-check {
    margin-bottom: 8px;
}

/* Responsive */
@media (max-width: 1200px) {
    .form-builder-preview {
        display: none;
    }
}

@media (max-width: 768px) {
    .form-builder-container {
        flex-direction: column;
        height: auto;
    }
    
    .form-builder-sidebar {
        width: 100%;
        order: 2;
    }
    
    .form-builder-canvas {
        order: 1;
    }
}

/* Animation */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-field {
    animation: slideIn 0.3s ease;
}

/* Drag and Drop Indicators */
.drop-zone {
    height: 4px;
    background: #007bff;
    border-radius: 2px;
    margin: 10px 0;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.drop-zone.active {
    opacity: 1;
}

/* Theme Customization */
.theme-selector {
    margin-bottom: 20px;
}

.theme-option {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-option:hover {
    background: #f8f9fa;
}

.theme-option.active {
    border-color: #007bff;
    background: #f8f9ff;
}

.theme-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    border: 2px solid white;
    box-shadow: 0 0 0 1px #dee2e6;
}

.theme-name {
    font-size: 14px;
    font-weight: 500;
}

/* File Upload Styles */
.file-upload-container {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.file-upload-area {
    padding: 40px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}

.file-upload-area.drag-over {
    background-color: #e3f2fd;
    border-color: #2196f3;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.upload-text h6 {
    color: #495057;
    margin-bottom: 5px;
}

.upload-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.file-list {
    margin-top: 20px;
    text-align: left;
}

.file-list h6 {
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
    transition: all 0.2s ease;
}

.file-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.file-preview {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    overflow: hidden;
    background: #f8f9fa;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-icon {
    font-size: 24px;
    color: #6c757d;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #495057;
    margin-bottom: 2px;
}

.file-size {
    font-size: 12px;
    color: #6c757d;
}

.file-error {
    font-size: 12px;
    margin-top: 2px;
}

.file-progress {
    margin-top: 5px;
}

.file-progress .progress {
    height: 4px;
    background: #e9ecef;
}

.file-actions {
    display: flex;
    gap: 5px;
}

.upload-actions {
    margin-top: 15px;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.upload-progress {
    margin-top: 20px;
    text-align: center;
}

.upload-progress .progress {
    height: 8px;
    margin-bottom: 10px;
}

.progress-text {
    font-size: 14px;
    color: #6c757d;
}

/* Camera Modal */
.camera-container {
    text-align: center;
    margin-bottom: 20px;
}

#cameraVideo,
#cameraCanvas {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.camera-controls {
    text-align: center;
    margin-top: 15px;
}

.camera-controls .btn {
    margin: 0 5px;
}

/* Crop Modal */
.crop-container {
    text-align: center;
    max-height: 500px;
    overflow: hidden;
}

/* Responsive */
@media (max-width: 768px) {
    .file-upload-area {
        padding: 30px 15px;
    }

    .upload-icon {
        font-size: 2rem;
    }

    .upload-buttons {
        flex-direction: column;
        align-items: center;
    }

    .upload-buttons .btn {
        width: 200px;
    }

    .file-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .file-preview {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .file-actions {
        justify-content: center;
    }
}

/* File Upload Field Preview */
.file-upload-preview {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
}

.upload-placeholder {
    color: #6c757d;
}

.upload-placeholder i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.upload-placeholder span {
    font-weight: 500;
    display: block;
    margin-bottom: 5px;
}

.file-upload-field {
    margin-top: 5px;
}
