/* CSS cho form quản lý món ăn */
.dish-form-container {
    background: #f8f9fc;
    min-height: 100vh;
}

.dish-card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.dish-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 0.35rem 0.35rem 0 0;
}

.food-selection-row {
    background: #f8f9fc;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e3e6f0;
}

.food-selection-row .form-group {
    margin-bottom: 15px;
}

.food-selection-row label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 5px;
}

.dish-foods-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dish-foods-table thead th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.dish-foods-table tbody tr:hover {
    background: #f8f9fc;
    transition: background-color 0.15s ease-in-out;
}

.dish-foods-table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.total-summary {
    background: linear-gradient(135deg, #36b9cc 0%, #1cc88a 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: 600;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #858796;
}

.empty-state i {
    opacity: 0.5;
    margin-bottom: 15px;
}

.btn-add-food {
    background: linear-gradient(135deg, #1cc88a 0%, #36b9cc 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.15s ease-in-out;
}

.btn-add-food:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(28, 200, 138, 0.3);
    color: white;
}

.btn-save-dish {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.15s ease-in-out;
}

.btn-save-dish:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 115, 223, 0.3);
    color: white;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.food-type-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.food-type-raw {
    background: #d1ecf1;
    color: #0c5460;
}

.food-type-cooked {
    background: #f8d7da;
    color: #721c24;
}

.nutrition-value {
    font-weight: 600;
    color: #5a5c69;
}

.dish-form-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #5a5c69;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e3e6f0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .food-selection-row {
        padding: 10px;
    }
    
    .dish-foods-table {
        font-size: 0.85rem;
    }
    
    .btn-add-food,
    .btn-save-dish {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Virtual Select customization */
.vscomp-wrapper {
    border-radius: 6px;
}

.vscomp-wrapper .vscomp-toggle-button {
    border: 1px solid #d1d3e2;
    border-radius: 6px;
    background: white;
}

.vscomp-wrapper .vscomp-toggle-button:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Loading state */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4e73df;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast notifications enhancement */
.toast-success {
    background: linear-gradient(135deg, #1cc88a 0%, #36b9cc 100%);
}

.toast-error {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
}

.toast-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.toast-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
} 