/* Survey Analytics Dashboard Styles */

.analytics-dashboard {
    padding: 20px 0;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    color: white;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card.danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card .card-body {
    position: relative;
    z-index: 2;
    padding: 25px;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2rem;
    opacity: 0.3;
}

/* Chart Containers */
.chart-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    padding: 25px;
    margin-bottom: 30px;
    transition: box-shadow 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.chart-subtitle {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin: 5px 0 0 0;
}

.chart-controls {
    display: flex;
    gap: 10px;
}

.chart-control-btn {
    padding: 5px 12px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-control-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.chart-control-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Real-time indicators */
.realtime-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 500;
}

.realtime-dot {
    width: 8px;
    height: 8px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Response Timeline */
.response-timeline {
    max-height: 400px;
    overflow-y: auto;
}

.timeline-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.timeline-item:hover {
    background-color: #f8f9fa;
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
    margin-right: 15px;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 2px;
}

.timeline-meta {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.timeline-time {
    font-size: 0.8rem;
    color: #95a5a6;
    white-space: nowrap;
}

/* Analytics Filters */
.analytics-filters {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    padding: 20px;
    margin-bottom: 30px;
}

.filter-group {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-item {
    flex: 1;
    min-width: 200px;
}

.filter-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.filter-item select,
.filter-item input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    font-size: 0.9rem;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

/* Export Options */
.export-options {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.export-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: #495057;
}

.export-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.export-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.export-btn.excel {
    background: #217346;
    color: white;
}

.export-btn.pdf {
    background: #dc3545;
    color: white;
}

.export-btn.csv {
    background: #6c757d;
    color: white;
}

.export-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-number {
        font-size: 2rem;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .chart-controls {
        width: 100%;
        justify-content: flex-start;
    }
    
    .filter-group {
        flex-direction: column;
    }
    
    .filter-item {
        min-width: 100%;
    }
    
    .export-buttons {
        justify-content: center;
    }
}

/* Loading States */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #6c757d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Chart Styles */
.chart-canvas {
    max-height: 400px;
}

/* Tooltip Styles */
.custom-tooltip {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
