#!/usr/bin/env node

/**
 * Demo script for AI Menu Generator
 * This script demonstrates the AI menu generation functionality
 */

const aiMenuService = require('../services/aiMenuService');
const commonService = require('../services/commonService');

// Demo configurations
const DEMO_CASES = [
    {
        name: '🥗 Thực đơn cân bằng',
        description: 'Thực đơn cân bằng dinh dưỡng cho người khỏe mạnh',
        request: {
            requirements: 'Tạo thực đơn cân bằng dinh dưỡng cho người trưởng thành khỏe mạnh, 3 bữa ăn trong ngày, đa dạng thực phẩm',
            preferences: ['đa dạng thực phẩm', 'cân bằng dinh dưỡng', 'tươi ngon'],
            restrictions: [],
            meal_count: 3,
            target_nutrition: {
                energy: 2000,
                protein: 80,
                carbohydrate: 250,
                lipid: 70
            }
        }
    },
    {
        name: '🩺 Thực đơn tiểu đường',
        description: 'Thực đơn cho bệnh nhân tiểu đường',
        request: {
            requirements: '<PERSON><PERSON><PERSON> thực đơn cho bệnh nhân tiểu đường type 2, kiểm soát đường huyết, ít đường, nhiều chất xơ, phân bổ đều carbohydrate trong ngày',
            preferences: ['ít đường', 'nhiều chất xơ', 'ít carbohydrate tinh chế', 'nhiều rau xanh'],
            restrictions: ['đường trắng', 'kẹo', 'bánh ngọt', 'nước ngọt', 'mứt'],
            meal_count: 3,
            target_nutrition: {
                energy: 1800,
                protein: 90,
                carbohydrate: 180,
                lipid: 60
            }
        }
    },
    {
        name: '⚖️ Thực đơn giảm cân',
        description: 'Thực đơn giảm cân an toàn và hiệu quả',
        request: {
            requirements: 'Tạo thực đơn giảm cân an toàn, ít calo nhưng đủ dinh dưỡng, nhiều protein để duy trì cơ bắp, nhiều rau xanh và chất xơ',
            preferences: ['ít calo', 'nhiều protein', 'nhiều rau xanh', 'nhiều chất xơ', 'ít chất béo'],
            restrictions: ['đồ chiên', 'đồ ngọt', 'thức ăn nhanh', 'nước ngọt'],
            meal_count: 4,
            target_nutrition: {
                energy: 1500,
                protein: 100,
                carbohydrate: 150,
                lipid: 50
            }
        }
    }
];

class AIMenuDemo {
    constructor() {
        this.results = [];
    }

    async runDemo() {
        console.log('🤖 AI Menu Generator Demo');
        console.log('========================\n');

        try {
            // Initialize services
            console.log('🔧 Initializing AI services...');
            const initResult = await aiMenuService.initialize();
            
            if (!initResult) {
                console.error('❌ Failed to initialize AI services');
                console.log('💡 Make sure you have configured:');
                console.log('   - GEMINI_API_KEY in .env file');
                console.log('   - PINECONE_API_KEY in .env file');
                console.log('   - PINECONE_ENVIRONMENT in .env file');
                return;
            }
            
            console.log('✅ AI services initialized successfully\n');

            // Check database connection
            console.log('🔍 Checking database connection...');
            const dbResult = await this.checkDatabase();
            if (!dbResult) {
                console.error('❌ Database connection failed');
                return;
            }
            console.log('✅ Database connection successful\n');

            // Run demo cases
            for (let i = 0; i < DEMO_CASES.length; i++) {
                const demoCase = DEMO_CASES[i];
                console.log(`${demoCase.name}`);
                console.log(`📝 ${demoCase.description}`);
                console.log('─'.repeat(50));
                
                await this.runDemoCase(demoCase, i + 1);
                console.log('\n');
            }

            // Print summary
            this.printSummary();

        } catch (error) {
            console.error('❌ Demo failed:', error.message);
            console.log('\n💡 Troubleshooting tips:');
            console.log('1. Check your API keys in .env file');
            console.log('2. Ensure database is running');
            console.log('3. Run: npm test to check system health');
        }
    }

    async checkDatabase() {
        try {
            const sql = 'SELECT COUNT(*) as count FROM food_info WHERE active = 1';
            const result = await commonService.getListTable(sql, []);
            
            if (result.success && result.data && result.data[0]) {
                const count = result.data[0].count;
                console.log(`   Found ${count} active foods in database`);
                return count > 0;
            }
            
            return false;
        } catch (error) {
            console.error('   Database error:', error.message);
            return false;
        }
    }

    async runDemoCase(demoCase, caseNumber) {
        const startTime = Date.now();
        
        try {
            console.log('⏳ Generating menu...');
            
            // Generate menu
            const result = await aiMenuService.generateSmartMenu(demoCase.request);
            const duration = Date.now() - startTime;
            
            if (result.success) {
                console.log(`✅ Menu generated successfully in ${duration}ms`);
                
                // Display menu
                this.displayMenu(result.data);
                
                // Store result
                this.results.push({
                    case: caseNumber,
                    name: demoCase.name,
                    success: true,
                    duration: duration,
                    menu: result.data.menu,
                    nutrition: result.data.nutrition_summary
                });
                
            } else {
                console.log(`❌ Menu generation failed: ${result.message}`);
                this.results.push({
                    case: caseNumber,
                    name: demoCase.name,
                    success: false,
                    duration: duration,
                    error: result.message
                });
            }
            
        } catch (error) {
            const duration = Date.now() - startTime;
            console.log(`❌ Error: ${error.message}`);
            this.results.push({
                case: caseNumber,
                name: demoCase.name,
                success: false,
                duration: duration,
                error: error.message
            });
        }
    }

    displayMenu(menuData) {
        const { menu, nutrition_summary } = menuData;
        
        console.log(`\n📋 ${menu.name}`);
        if (menu.requirements) {
            console.log(`💭 Yêu cầu: ${menu.requirements.substring(0, 100)}...`);
        }
        
        // Display nutrition summary
        console.log('\n📊 Tổng hợp dinh dưỡng:');
        console.log(`   🔥 Năng lượng: ${nutrition_summary.total_energy || 0} kcal`);
        console.log(`   💪 Protein: ${nutrition_summary.total_protein || 0} g`);
        console.log(`   🍞 Carbohydrate: ${nutrition_summary.total_carbohydrate || 0} g`);
        console.log(`   🥑 Lipid: ${nutrition_summary.total_lipid || 0} g`);
        console.log(`   🌾 Chất xơ: ${nutrition_summary.total_fiber || 0} g`);
        
        // Display meals
        console.log('\n🍽️ Chi tiết thực đơn:');
        menu.detail.forEach((meal, index) => {
            console.log(`\n   ${index + 1}. ${meal.name}:`);
            meal.listFood.forEach((food, foodIndex) => {
                console.log(`      • ${food.name} - ${food.weight}g (${food.energy || 0} kcal)`);
            });
        });
    }

    printSummary() {
        console.log('📈 DEMO SUMMARY');
        console.log('===============');
        
        const successful = this.results.filter(r => r.success).length;
        const total = this.results.length;
        const avgTime = this.results
            .filter(r => r.success)
            .reduce((sum, r) => sum + r.duration, 0) / successful;
        
        console.log(`✅ Successful: ${successful}/${total}`);
        console.log(`⏱️  Average time: ${avgTime.toFixed(0)}ms`);
        console.log(`📊 Success rate: ${((successful / total) * 100).toFixed(1)}%`);
        
        if (successful > 0) {
            console.log('\n🎯 Generated Menus:');
            this.results.filter(r => r.success).forEach(result => {
                const nutrition = result.nutrition;
                console.log(`   ${result.name}: ${nutrition.total_energy}kcal, ${nutrition.total_protein}g protein`);
            });
        }
        
        if (successful < total) {
            console.log('\n❌ Failed Cases:');
            this.results.filter(r => !r.success).forEach(result => {
                console.log(`   ${result.name}: ${result.error}`);
            });
        }
        
        console.log('\n🚀 Demo completed!');
        
        if (successful === total) {
            console.log('🎉 All demo cases passed successfully!');
            console.log('💡 You can now use the AI Menu Generator in your application.');
        } else {
            console.log('⚠️  Some demo cases failed. Please check the configuration.');
        }
    }
}

// Run demo if this file is executed directly
if (require.main === module) {
    const demo = new AIMenuDemo();
    demo.runDemo().then(() => {
        process.exit(0);
    }).catch((error) => {
        console.error('Demo runner failed:', error);
        process.exit(1);
    });
}

module.exports = AIMenuDemo;
