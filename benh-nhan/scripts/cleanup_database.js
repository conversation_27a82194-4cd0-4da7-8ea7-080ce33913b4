#!/usr/bin/env node

/**
 * Database Cleanup Script
 * Thực hiện cleanup database và hiển thị kết quả
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'benh_nhan',
    multipleStatements: true
};

async function main() {
    let connection;
    
    try {
        console.log('🔗 Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        
        // 1. Kiểm tra kích thước database trước cleanup
        console.log('\n📊 KIỂM TRA DATABASE TRƯỚC CLEANUP:');
        await checkDatabaseSize(connection);
        
        // 2. <PERSON><PERSON>m tra các bảng automation
        console.log('\n🤖 KIỂM TRA CÁC BẢNG AUTOMATION:');
        await checkAutomationTables(connection);
        
        // 3. <PERSON><PERSON><PERSON> tra các cột không cần thiết
        console.log('\n📋 KIỂM TRA CÁC CỘT KHÔNG CẦN THIẾT:');
        await checkUnnecessaryColumns(connection);
        
        // 4. Hỏi xác nhận trước khi cleanup
        console.log('\n⚠️  CẢNH BÁO: Bạn có muốn thực hiện cleanup database không?');
        console.log('   Thao tác này sẽ XÓA VĨNH VIỄN các bảng và cột không cần thiết!');
        
        // Trong môi trường production, nên có confirmation
        const shouldCleanup = process.argv.includes('--force') || process.env.NODE_ENV === 'development';
        
        if (shouldCleanup) {
            console.log('\n🧹 THỰC HIỆN CLEANUP...');
            await performCleanup(connection);
            
            console.log('\n✅ KIỂM TRA DATABASE SAU CLEANUP:');
            await checkDatabaseSize(connection);
            
            console.log('\n🎉 CLEANUP HOÀN THÀNH!');
        } else {
            console.log('\n❌ Cleanup bị hủy. Sử dụng --force để thực hiện cleanup.');
        }
        
    } catch (error) {
        console.error('❌ Lỗi:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

async function checkDatabaseSize(connection) {
    const [rows] = await connection.execute(`
        SELECT 
            table_schema AS 'database_name',
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'size_mb',
            COUNT(*) AS 'table_count'
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
        GROUP BY table_schema
    `);
    
    if (rows.length > 0) {
        const { database_name, size_mb, table_count } = rows[0];
        console.log(`   Database: ${database_name}`);
        console.log(`   Kích thước: ${size_mb} MB`);
        console.log(`   Số bảng: ${table_count}`);
    }
}

async function checkAutomationTables(connection) {
    const automationTables = [
        'automation_rules',
        'webhook_logs', 
        'survey_invitations',
        'automation_logs',
        'email_templates',
        'survey_field_groups',
        'survey_templates'
    ];
    
    for (const tableName of automationTables) {
        try {
            const [rows] = await connection.execute(`
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() AND table_name = ?
            `, [tableName]);
            
            if (rows[0].count > 0) {
                const [dataRows] = await connection.execute(`SELECT COUNT(*) as row_count FROM ${tableName}`);
                console.log(`   ✅ ${tableName}: Tồn tại (${dataRows[0].row_count} rows)`);
            } else {
                console.log(`   ❌ ${tableName}: Không tồn tại`);
            }
        } catch (error) {
            console.log(`   ❌ ${tableName}: Không tồn tại hoặc lỗi`);
        }
    }
}

async function checkUnnecessaryColumns(connection) {
    // Kiểm tra cột trong survey_configs
    const surveyConfigColumns = [
        'reminder_enabled', 'max_reminders', 'reminder_interval_hours',
        'webhook_url', 'webhook_secret', 'webhook_headers',
        'daily_reports', 'report_recipients', 'expiry_date',
        'closed_at', 'close_reason', 'form_config'
    ];
    
    console.log('   📋 survey_configs:');
    for (const columnName of surveyConfigColumns) {
        const [rows] = await connection.execute(`
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
                AND table_name = 'survey_configs' 
                AND column_name = ?
        `, [columnName]);
        
        if (rows[0].count > 0) {
            console.log(`      ✅ ${columnName}: Tồn tại`);
        } else {
            console.log(`      ❌ ${columnName}: Không tồn tại`);
        }
    }
    
    // Kiểm tra cột trong survey_fields
    const surveyFieldColumns = ['field_group', 'conditional_logic'];
    
    console.log('   📋 survey_fields:');
    for (const columnName of surveyFieldColumns) {
        const [rows] = await connection.execute(`
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
                AND table_name = 'survey_fields' 
                AND column_name = ?
        `, [columnName]);
        
        if (rows[0].count > 0) {
            console.log(`      ✅ ${columnName}: Tồn tại`);
        } else {
            console.log(`      ❌ ${columnName}: Không tồn tại`);
        }
    }
}

async function performCleanup(connection) {
    const migrationPath = path.join(__dirname, '../database/migrations/2025_09_05_cleanup_survey_system.sql');
    
    if (!fs.existsSync(migrationPath)) {
        throw new Error('Migration file không tồn tại: ' + migrationPath);
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('   🔄 Đang thực hiện migration...');
    await connection.query(migrationSQL);
    console.log('   ✅ Migration hoàn thành!');
}

// Chạy script
if (require.main === module) {
    main();
}

module.exports = { main };
