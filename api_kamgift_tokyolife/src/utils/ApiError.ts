
enum ApiErrorCode {
  InputError = 'E400',
  NotfoundError = 'E404',
  UnauthorizedError = 'E401'
}

class ApiError extends Error {
  httpStatus: number

  code: string

  error_code: string

  errCode: number

  constructor(httpStatus: number, code: string, message = '', stack = '', errCode?: number) {
    if (message.length === 0) {
      message = code
    }
    super(message)
    this.httpStatus = httpStatus
    this.code = code
    if (errCode) this.errCode = errCode

    if (stack) {
      this.stack = stack
    }
  }
}

export class InputError extends ApiError {
  constructor(message = '', stack = '', errCode?: number) {
    super(400, ApiErrorCode.InputError, message, stack, errCode)
  }
}

export class NotFoundError extends ApiError {
  constructor(message = 'Resource not found', stack = '', errCode?: number) {
    super(404, ApiErrorCode.NotfoundError, message, stack, errCode)
  }
}

export class UnauthorizedError extends ApiError {
  constructor(message = 'Unauthorized', stack = '', errCode?: number) {
    super(401, ApiErrorCode.UnauthorizedError, message, stack, errCode)
  }
}

export default ApiError;