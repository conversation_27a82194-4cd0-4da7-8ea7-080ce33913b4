import { AppDataSource } from '@/config/config'
import { TblArticleLink, TblUserArticleLink, TblUserEvent } from '@/entities'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { LinkRequest } from '@/types/requests/link'
import * as helper from '../helpers/index'
import { PagedData, buildPagedData } from '@/types/models/pagination'
import { NotFoundError } from '@/utils/ApiError'

const myArticleLinkRepository = () => AppDataSource.getRepository(TblArticleLink)
const myUserArticleLinkRepository = () => AppDataSource.getRepository(TblUserArticleLink)
const myUserEventRepository = () => AppDataSource.getRepository(TblUserEvent)

export const getListNotAuth = async (req): Promise<PagedData<any>> => {
  const { limit, offset, order_by, order_type, page_number } = helper.sortAndPaging(req.query)

  const query = myArticleLinkRepository().createQueryBuilder('article_links').where({ active: 1 })

  const [data, count] = await query.skip(offset).take(limit).orderBy('article_links.createdAt', 'DESC').getManyAndCount() 
  let results = [];
  
  for (const link of data) {
    results.push(
      {
        id: link.id,
        title: link.title,
        link: link.link,
        read: 0
      }
    );
  }
  
  return buildPagedData<any>(results, count, { number: page_number, size: limit });
}

export const getList = async (req: AuthorizedUserRequest): Promise<PagedData<any>> => {
  const { user } = req.authorizedUser

  const { limit, offset, order_by, order_type, page_number } = helper.sortAndPaging(req.query)

  const query = myArticleLinkRepository().createQueryBuilder('article_links').where({ active: 1 })

  const [data, count] = await query.skip(offset).take(limit).orderBy('article_links.createdAt', 'DESC').getManyAndCount()

  // const results = data.map(async (link) => {
  //   let read = 0;

  //   if (user) {
  //     const tmp = await myUserArticleLinkRepository().createQueryBuilder('user_article_link').where({ userId: user.id, articleLinkId: link.id }).getOne()
  //     if (tmp) read = 1
  //   }

  //   return {
  //     id: link.id,
  //     title: link.title,
  //     link: link.link,
  //     read: read
  //   }
  // })

  //return buildPagedData<any>(results, count, { number: page_number, size: limit })
 
  let results = [];
  
  for (const link of data) {
    let read = 0;

    if (user) {
      const tmp = await myUserArticleLinkRepository().createQueryBuilder('user_article_link').where({ userId: user.id, articleLinkId: link.id }).getOne()
      if (tmp) read = 1
    }

    results.push(
      {
        id: link.id,
        title: link.title,
        link: link.link,
        read: read
      }
    );
  }
  
  return buildPagedData<any>(results, count, { number: page_number, size: limit });
}

export const read = async (req: LinkRequest) => {
  const { user } = req.authorizedUser
  const id: number = parseInt(req.params['id'] || '0')

  const link = await myArticleLinkRepository().createQueryBuilder('article_links').where({ id: id }).getOne()

  if (!link) throw new NotFoundError(`Link id=${id} không tồn tại.`, '', 1)

  await myUserArticleLinkRepository().save({
    userId: user.id,
    articleLinkId: link.id,
    read: 1
  })
}

/**
 * cong luot cho user
 * neu da doc roi thi khong cong nua
 * chua doc thi cong them luot cho user
 */
export const extraSpin = async (req: LinkRequest) => {
  const { user } = req.authorizedUser
  const id: number = parseInt(req.params['id'] || '0')

  const link = await myArticleLinkRepository().createQueryBuilder('article_links').where({ id: id }).getOne()

  if (!link) throw new NotFoundError(`Link id=${id} không tồn tại.`, '', 1)

  //chua doc thi cong them luot
  if (!link.read) {
    const userEvent = await myUserEventRepository().createQueryBuilder('user_event').where({ userId: user.id }).getOne()
    userEvent.spins++

    await myUserEventRepository().save(userEvent)
  }
  else throw new NotFoundError(`Không thể cộng lượt cho bài báo đã đọc.`, '', 1)
}

