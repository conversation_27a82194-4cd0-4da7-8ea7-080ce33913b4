import { AppDataSource } from '@/config/config'
import { Tbl<PERSON>olls, TblPollAnswers, TblPollVotingRecords } from '@/entities'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import * as helper from '../helpers/index'
import { PagedData, buildPagedData } from '@/types/models/pagination'
import { commonService } from '@/services'

const pollRepository = () => AppDataSource.getRepository(TblPolls)
const pollAnswerRepository = () => AppDataSource.getRepository(TblPollAnswers)
const pollVotingRepository = () => AppDataSource.getRepository(TblPollVotingRecords)

export const getPollGuess = async (req) => {
  const campaignId = req.query.campaign_id || 0;
  const resultId = req.query.result_id || 0;
  const result = await commonService.mysqlQueryData(`call polls_get_current_poll_by_campaign(${campaignId}, 0, ${resultId})`);
  let arrPool = [];
  let arrQuestion = [];
  let arrOption = [];


  if(Array.isArray(result)){
    if(result.length >= 3){
      arrPool = JSON.parse(JSON.stringify(result[0]));
      arrQuestion =  JSON.parse(JSON.stringify(result[1]));
      arrOption =  JSON.parse(JSON.stringify(result[2]));      

      return {
        "pool": arrPool[0],
        "questions": arrQuestion,
        "options": arrOption
      }
    }
  }

  return {};
}

export const getPollUser = async (req: AuthorizedUserRequest) => {
  const { user } = req.authorizedUser;

  if(!user) return {};

  const campaignId = req.query.campaign_id || 0;
  const resultId = req.query.result_id || 0;
  const result = await commonService.mysqlQueryData(`call polls_get_current_poll_by_campaign(${campaignId}, ${user.id}, ${resultId})`);
  let arrPool = [];
  let arrQuestion = [];
  let arrOption = [];


  if(Array.isArray(result)){
    if(result.length >= 3){
      arrPool = JSON.parse(JSON.stringify(result[0]));
      arrQuestion =  JSON.parse(JSON.stringify(result[1]));
      arrOption =  JSON.parse(JSON.stringify(result[2]));      

      return {
        "pool": arrPool[0],
        "questions": arrQuestion,
        "options": arrOption
      }
    }
  }

  return {};
}

export const doPollGuess = async (req) => {
  const pollId = req.body.poll_id || 0;
  const resultId = req.body.result_id || 0;
  let answser = req.body.answer || "[]";

  answser = answser.replaceAll('"', '\'');
  
  console.log(`call polls_user_do_poll(${pollId}, ${resultId}, 0, "${answser}")`);
  
  const result = await commonService.mysqlQueryData(`call polls_user_do_poll(${pollId}, ${resultId}, 0, "${answser}")`);
  let arrPool = [];


  if(Array.isArray(result)){
    if(result.length >= 1){
      arrPool = JSON.parse(JSON.stringify(result[0])); 

      return {
        "poolResult": arrPool[0]
      }
    }
  }

  return {};
}

export const doPollUser = async (req : AuthorizedUserRequest) => {
  const { user } = req.authorizedUser;

  if(!user) return {};

  const pollId = req.body.poll_id || 0;
  const resultId = req.body.result_id || 0;
  let answser = req.body.answer || "[]";

  answser = answser.replaceAll('"', '\'');
  
  console.log(`call polls_user_do_poll(${pollId}, ${resultId}, 0, "${answser}")`);
  
  const result = await commonService.mysqlQueryData(`call polls_user_do_poll(${pollId}, ${resultId}, ${user.id}, "${answser}")`);
  let arrPool = [];


  if(Array.isArray(result)){
    if(result.length >= 1){
      arrPool = JSON.parse(JSON.stringify(result[0])); 

      return {
        "poolResult": arrPool[0]
      }
    }
  }

  return {};
}

export const getAllPoll = async (req): Promise<PagedData<any>> => {
  const { limit, offset, order_by, order_type, page_number } = helper.sortAndPaging(req.query);

  const campaignId = req.query.campaign_id;

  const query = pollRepository().createQueryBuilder('poll').where({ status: 1, campaignId: campaignId })

  const [data, count] = await query.skip(offset).take(limit).orderBy('poll.display_order', 'ASC').getManyAndCount() 
  
  return buildPagedData<any>(data, count, { number: page_number, size: limit });
}

export const getAllPollAnswerByPollId = async (req: AuthorizedUserRequest): Promise<PagedData<any>> => {
  const { user } = req.authorizedUser;
  const pollId = req.params.id;

  const { limit, offset, order_by, order_type, page_number } = helper.sortAndPaging(req.query)

  const query = pollAnswerRepository().createQueryBuilder('poll_answer').where({ pollId: pollId });

  const [data, count] = await query.skip(offset).take(limit).orderBy('poll_answer.display_order', 'ASC').getManyAndCount();
  let results = [];

  for (const link of data) {  
    const voteNum = await pollVotingRepository().createQueryBuilder('poll_voting_record').where({ pollAnswerId: link.id, isUpvote: 1 }).getCount()

    results.push(
      {
        ...link,
        voted: voteNum
      }
    );
  }
   
  return buildPagedData<any>(results, count, { number: page_number, size: limit });
}

export const doPoll = async (req: AuthorizedUserRequest) => {
  const { user } = req.authorizedUser
  const id: number = parseInt(req.params.id || '0');
  const votes = req.body.votes;

  const { limit, offset, order_by, order_type, page_number } = helper.sortAndPaging(req.query)

  const query = pollAnswerRepository().createQueryBuilder('poll_answer').where({ pollId: id });

  const [data, count] = await query.skip(offset).take(limit).orderBy('poll_answer.display_order', 'ASC').getManyAndCount();

  for (const link of data) {
    await pollVotingRepository()
      .createQueryBuilder()
      .delete()
      .from('poll_voting_record') // or use the entity: .from(DistrictEntity)
      .where('user_id = :user_id AND poll_answer_id = :poll_answer_id', { user_id: user.id, poll_answer_id: link.id })
      .execute();
  }

  for (const link of votes) {
    if(link.id){
      const voted = new TblPollVotingRecords();
      voted.pollAnswerId = link.id;
      voted.isUpvote = link.is_upvote || 0;
      voted.userId = user.id;

    await pollVotingRepository().save(voted);
    }    
  }
}

export const didPoll = async (req: AuthorizedUserRequest) => {
  const { user } = req.authorizedUser;
  const pollId = req.params.id;

  const { limit, offset, order_by, order_type, page_number } = helper.sortAndPaging(req.query)

  const query = pollAnswerRepository().createQueryBuilder('poll_answer').where({ pollId: pollId });

  const [data, count] = await query.skip(offset).take(limit).orderBy('poll_answer.display_order', 'ASC').getManyAndCount();
  let results = [];

  for (const link of data) {  
    const voteNum = await pollVotingRepository().createQueryBuilder('poll_voting_record').where({ pollAnswerId: link.id, isUpvote: 1, userId: user.id }).getCount()

    results.push(
      {
        id: link.id,
        name: link.name,
        display_order: link.displayOrder,
        voted: voteNum
      }
    );
  }
   
  return buildPagedData<any>(results, count, { number: page_number, size: limit });
}