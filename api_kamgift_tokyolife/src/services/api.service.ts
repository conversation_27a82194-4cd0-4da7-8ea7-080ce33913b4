import { MySqlCnn } from '@/config/config'
import { AppDataSource } from '@/config/config'
import { TblClientAuthens } from '@/entities'
const clientAuthenRepository = () => AppDataSource.getRepository(TblClientAuthens);

export default {
  async test() {
    const connection = MySqlCnn();

    return new Promise((resolve) =>{
      //Run the query
      connection.query('select * from users', function (error, results) {
        if (error){
            throw error;
        }

        connection.destroy();
    
        return resolve(results);
      });
    });
  },  
  async clientAuthen(clientId, clientSecrectKey) {
    return new Promise(async (resolve) =>{
      const clientAuthen = await clientAuthenRepository()
        .createQueryBuilder('client_authens')
        .where({ clientId: clientId,  clientSecrectKey: clientSecrectKey})
        .getMany();

      if(!clientAuthen || clientAuthen.length == 0){
          return resolve(false);
      }

      return resolve(true);
    });
  },
}