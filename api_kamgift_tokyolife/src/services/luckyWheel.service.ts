import { AppDataSource } from '@/config/config'
import { LuckyPrize } from '@/entities/LuckyPrize'
import { UserSpin } from '@/entities/UserSpin'
import { SpinHistory } from '@/entities/SpinHistory'
import { WaterUserGift } from '@/entities/WaterUserGift'
import { TblGames } from '@/entities'

const prizeRepo = () => AppDataSource.getRepository(LuckyPrize)
const userSpinRepo = () => AppDataSource.getRepository(UserSpin)
const spinHistoryRepo = () => AppDataSource.getRepository(SpinHistory)
const waterUserGiftRepo = () => AppDataSource.getRepository(WaterUserGift)
const gameRepo = () => AppDataSource.getRepository(TblGames)

function getNowGMT7(): Date {
  const now = new Date();
  return new Date(now.getTime() + 7 * 60 * 60 * 1000);
}

function sub7Hours(times: Date): Date {
  const now = new Date(times);
  return new Date(now.getTime() - 7 * 60 * 60 * 1000);
}

async function createUserGift(userId: number,rewardCode: string, campaignId: number, gameId: number){
    const waterUserGift = new WaterUserGift()
    waterUserGift.userId = userId
    waterUserGift.rewardCode = rewardCode
    waterUserGift.times = 0
    waterUserGift.currWaterGiftConfigId = 0
    waterUserGift.active = 0
    waterUserGift.createdAt = getNowGMT7()
    waterUserGift.updatedAt = getNowGMT7()
    waterUserGift.campaignId = campaignId
    waterUserGift.gameId = gameId
    await waterUserGiftRepo().save(waterUserGift)
}

export const getActivePrizes = async (campaignId: number) => {
  console.log('getActivePrizes', campaignId)
  return prizeRepo().find({ where: { campaignId, active: 1 } })
}

export const getUserSpin = async (userId: number, campaignId: number) => {
  return userSpinRepo().findOne({ where: { userId, campaignId } })
}

export const decreaseUserSpin = async (userId: number) => {
  const userSpin = await userSpinRepo().findOne({ where: { userId } })
  if (userSpin && userSpin.spinCounts > 0) {
    userSpin.spinCounts -= 1
    userSpin.updatedAt = getNowGMT7()
    await userSpinRepo().save(userSpin)
  }
}

export const saveSpinHistory = async (userId: number, prizeId: number, campaignId: number) => {
  const history = new SpinHistory()
  history.userId = userId
  history.prizeId = prizeId
  history.spinTime = getNowGMT7()
  history.createdAt = getNowGMT7()
  history.updatedAt = getNowGMT7()
  history.campaignId = campaignId
  history.status = 0
  await spinHistoryRepo().save(history)
}

export const randomPrize = (prizes: LuckyPrize[]): LuckyPrize | null => {
  const total = prizes.reduce((sum, p) => sum + p.winrate, 0)
  let rand = Math.random() * total
  for (const prize of prizes) {
    if (rand < prize.winrate) return prize
    rand -= prize.winrate
  }
  return null
}

export const addSpinCount = async (userId: number, campaignId: number) => {
  console.log('addSpinCount', userId, campaignId)
  const userSpin = await userSpinRepo().findOne({ where: { userId, campaignId } })
  const now = new Date()
  const nowVn = getNowGMT7();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  if (!userSpin) {
    const newUserSpin = new UserSpin()
    newUserSpin.userId = userId
    newUserSpin.spinCounts = 1
    newUserSpin.createdAt = nowVn
    newUserSpin.updatedAt = nowVn
    newUserSpin.lastRequest = nowVn
    newUserSpin.points = 0
    newUserSpin.campaignId = campaignId
    await userSpinRepo().save(newUserSpin)
    return 1
  }
 
  if (!userSpin.lastRequest || new Date(sub7Hours(userSpin.lastRequest)).getTime() < today.getTime()) {
    userSpin.spinCounts += 1
    userSpin.updatedAt = nowVn
    userSpin.lastRequest = nowVn
    await userSpinRepo().save(userSpin)
  }
  return userSpin.spinCounts
}

function generateRandomString(length: number, chars: string) {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

function generateRewardCode(): string {
  const numbers = '0123456789'
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const randomNumbers = generateRandomString(3, numbers)
  const randomLetters = generateRandomString(2, letters)
  return `KAM${randomNumbers}${randomLetters}`
}

export const spinLuckyWheel = async (userId: number, campaignId: number) => {
  const game = await gameRepo().findOne({ where: { campaignId, active: 1, type: 2 } })
  
  if (!game) {
    throw new Error('Không tìm thấy game')
  }
  
  const now = getNowGMT7();
  if (now < game.start_date || now > game.end_date) {
    throw new Error(now < game.start_date ? 'Game chưa bắt đầu' : 'Sự kiện đã kết thúc! Hãy theo dõi sự kiện quay mã trên fanpage của aFamily để tìm ra người may mắn nhận được vé bạn nhé!')
  }

  const userSpin = await getUserSpin(userId, campaignId)
  if (!userSpin || userSpin.spinCounts <= 0) {
    throw new Error('Bạn đã hết lượt quay')
  }
  const prizes = await getActivePrizes(campaignId)
  const prize = randomPrize(prizes)
  if (!prize) throw new Error('Không có phần thưởng phù hợp')
  await saveSpinHistory(userId, prize.id, campaignId)

  // Trừ lượt quay
  userSpin.spinCounts -= 1;

  // Cộng point nếu có
  if (prize.point && prize.point > 0) {
    userSpin.points = (userSpin.points || 0) + prize.point
    if (userSpin.points >= 5) {
      let rewardCode = generateRewardCode()
      let exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
      while (exist) {
        rewardCode = generateRewardCode()
        exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
      }
      userSpin.points -= 5
      await createUserGift(userId, rewardCode, campaignId, game.id)
      return { prize, rewardCode }
    }
  }

  await userSpinRepo().save(userSpin)
  // Tạo rewardCode nếu prize.type là voucher
  if (prize.type === 'voucher') {
    let rewardCode = generateRewardCode()
    let exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
    while (exist) {
      rewardCode = generateRewardCode()
      exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
    }
    
    await createUserGift(userId, rewardCode, campaignId, game.id)
    return { prize, rewardCode }
  }

  return { prize }
}

export const getSpinHistoryByUser = async (userId: number, campaignId: number) => {
  return spinHistoryRepo()
    .createQueryBuilder('spin_histories')
    .leftJoinAndMapOne('spin_histories.prize', LuckyPrize, 'prize', 'prize.id = spin_histories.prizeId')
    .where('spin_histories.userId = :userId', { userId })
    .andWhere('spin_histories.campaignId = :campaignId', { campaignId })
    .orderBy('spin_histories.spinTime', 'DESC')
    .getMany()
}

export const getDailyStats = async (campaignId: string | number) => {
  // Lấy tổng số lượt quay trong ngày từ bảng UserSpin
  const totalSpins = await userSpinRepo()
    .createQueryBuilder('user_spins')
    .where('user_spins.campaignId = :campaignId', { campaignId })
    .getCount()

  // Lấy số lượng rewardCode theo điều kiện
  const totalRewardCodes = await waterUserGiftRepo()
    .createQueryBuilder('water_user_gifts')
    .where('water_user_gifts.active = :active', { active: 0 })
    .andWhere('water_user_gifts.times = :times', { times: 0 })
    .andWhere('water_user_gifts.currWaterGiftConfigId = :configId', { configId: 0 })
    .andWhere('water_user_gifts.campaignId = :campaignId', { campaignId })
    .getCount()

  return {
    totalSpins: totalSpins + 5000,
    totalRewardCodes: totalRewardCodes + 800
  }
} 