import { AppDataSource } from '@/config/config'
import { WaterGiftConfig, WaterUserGift, WaterLogs, TblGames } from '@/entities'
import { Repository, Between, Not, In } from 'typeorm'

function generateRandomString(length: number, chars: string) {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

function generateRewardCode(): string {
  const numbers = '0123456789'
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  
  const randomNumbers = generateRandomString(3, numbers)
  const randomLetters = generateRandomString(2, letters)
  
  return `KAM${randomNumbers}${randomLetters}`
}

function getNowGMT7(): Date {
  const now = new Date();
  return new Date(now.getTime() + 7 * 60 * 60 * 1000);
}

class WaterGameService {
  private userGiftRepo: Repository<WaterUserGift>

  private configRepo: Repository<WaterGiftConfig>

  private logsRepo: Repository<WaterLogs>

  private myGameRepo: Repository<TblGames>

  constructor() {
    this.userGiftRepo = AppDataSource.getRepository(WaterUserGift)
    this.configRepo = AppDataSource.getRepository(WaterGiftConfig)
    this.logsRepo = AppDataSource.getRepository(WaterLogs)
    this.myGameRepo =  AppDataSource.getRepository(TblGames)
  }

  async getCurrentUserGift(userId: number, gameId: number) {
    return this.userGiftRepo.findOne({ where: { userId, active: 1, gameId } })
  }

  async getConfigById(id: number) {
    return this.configRepo.findOne({ where: { id } })
  }

  async getAllConfigs(gameId: number) {
    return this.configRepo.find({ where: { gameId } })
  }

    async getGameById(id: number) {
    return this.myGameRepo.findOne({ where: { id, active: 1 } })
  }

  async getGameByCampaignId(campaignId: number) {
    return this.myGameRepo.findOne({ where: { campaignId, active: 1 } })
  }

  async getMaxDay(userId: number) {
    const result = await this.userGiftRepo
      .createQueryBuilder('userGift')
      .leftJoin('water_gift_config', 'config', 'config.id = userGift.currWaterGiftConfigId')
      .where('userGift.userId = :userId', { userId })
      .orderBy('config.day', 'DESC')
      .select('MAX(config.day)', 'maxDay')
      .getRawOne()

    return result?.maxDay || 0
  }

  async getUserRewardCodes(userId: number, campaignId: number) {
    const result = [];

    const listGame = await this.myGameRepo.find({ where: { active: 1, campaignId } });
    if(listGame.length > 0){
      for(let i = 0; i < listGame.length; i++){
        const game = listGame[i];
        const reward = await this.userGiftRepo.find({ where: { userId, gameId: game.id, rewardCode: Not('') } })
        if(reward && reward.length > 0){
          result.push(reward);
        }
      }
    }
    return result
  }

  async getTotalUserWatering(gameId: number) {
    const result = await this.userGiftRepo
      .createQueryBuilder('userGift')
      .select('COUNT(DISTINCT userGift.userId)', 'count')
      .where('userGift.times > :times', { times: 0 })
      .andWhere('userGift.gameId = :gameId', { gameId: gameId })
      .getRawOne()
      
    return parseInt(result.count) || 0
  }

  async getTotalRewardCodesToday(gameId: number) {

    const result = await this.userGiftRepo
      .createQueryBuilder('userGift')
      .andWhere('userGift.rewardCode != :emptyCode', { emptyCode: '' })
      .andWhere('userGift.times > :times', { times: 0 })
      .andWhere('userGift.gameId = :gameId', { gameId: gameId })
      .getCount()

    return result
  }

  async water(userId: number, day: number, giftReceived: number, gameId: number) {
    const now = getNowGMT7();
    let userGift = await this.userGiftRepo.findOne({ where: { userId, gameId, active: 1 }, relations: ['waterGiftConfig'] })
    const config = await this.configRepo.findOne({ where: { day, gameId } })
    const gameInfo = await this.getGameById(gameId)
    if (!gameInfo) throw new Error('Game not found')
    if (!config) throw new Error('Config not found')

    // Kiểm tra đã tưới chưa
    const timeAgo = new Date(now.getTime() - (gameInfo.time_number || 120) * 60 * 1000)
    const logToday = await this.logsRepo.findOne({
      where: {
        userId: userId,
        gameId: gameId,
        createdAt: Between(timeAgo, now)
      }
    })
    if (logToday) {
      const error: any = new Error('Ngày hôm nay bạn đã tưới rồi')
      error.code = 'ALREADY_WATERED_TODAY'
      throw error
    }

    let rewardCode = ''
    if (day === 5) {
      // Tạo mã code dự thưởng cho ngày thứ 5
      rewardCode = generateRewardCode()
      // Kiểm tra xem mã code đã tồn tại chưa
      let exist = await this.userGiftRepo.findOne({ where: { rewardCode, gameId } })
      while (exist) {
        // Nếu đã tồn tại thì tạo lại mã mới
        rewardCode = generateRewardCode()
        exist = await this.userGiftRepo.findOne({ where: { rewardCode, gameId } })
      }
    }

    if (userGift) {
      // Bỏ kiểm tra hoa héo chỉ cập nhật nếu tồn tại
      // Kiểm tra ngày hoặc phút
      // const lastUpdate = new Date(userGift.updatedAt)
      // const lastUpdateDay = new Date(lastUpdate.getFullYear(), lastUpdate.getMonth(), lastUpdate.getDate())
      // const nowDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      let shouldCreateNew = false;
      console.log('userGift', userGift);
      if(userGift.waterGiftConfig.day == 5) {
        shouldCreateNew = true
      }
      // if (test) {
      //   // So sánh theo giây
      //   const dbTime = new Date(userGift.updatedAt);
      //   const diffSeconds = (now.getTime() - dbTime.getTime()) / 1000
      //   shouldCreateNew = diffSeconds > 600
      //   console.log('shouldCreateNew', shouldCreateNew, diffSeconds, dbTime, now.getTime(), userGift);
      // } else {
      //   // So sánh theo ngày
      //   const diff = (nowDay.getTime() - lastUpdateDay.getTime()) / (1000 * 3600 * 24)
      //   shouldCreateNew = diff > 2
      //   console.log('shouldCreateNew day', shouldCreateNew, diff, nowDay.getTime(), lastUpdateDay.getTime(), userGift);
      // }
      if (shouldCreateNew) {
        // Cập nhật trạng thái của gift cũ
        userGift.active = 0;
        await this.userGiftRepo.save(userGift);
        // Tạo mới
        userGift = this.userGiftRepo.create({
          userId: userId,
          gameId: gameId,
          times: userGift.times + 1,
          currWaterGiftConfigId: config.id,
          rewardCode: '',
          active: 1,
          createdAt: now,
          updatedAt: now
        })
        await this.userGiftRepo.save(userGift)
      } else {
        // Cập nhật
        userGift.currWaterGiftConfigId = config.id
        userGift.updatedAt = now
        if (rewardCode) {
          userGift.rewardCode = rewardCode
        }
        await this.userGiftRepo.save(userGift)
      }
    } else {
      // Tạo mới
      userGift = this.userGiftRepo.create({
        userId: userId,
        gameId: gameId,
        times: 1,
        currWaterGiftConfigId: config.id,
        rewardCode: '',
        active: 1,
        createdAt: now,
        updatedAt: now
      })
      await this.userGiftRepo.save(userGift)
    }

    // Lưu log
    const log = this.logsRepo.create({
      userId: userId,
      gameId: gameId,
      waterGiftConfigId: config.id,
      warterUserGiftId: userGift.id,
      giftReceived: giftReceived,
      createdAt: now,
      updatedAt: now
    })
    await this.logsRepo.save(log)

    return userGift
  }

  randomBetween50And100() {
      return Math.floor(Math.random() * 51) + 50;
  }

  // async createFromLogs() {
  //   const now = getNowGMT7();
    
  //   // Lấy các waterlogs có water_gift_config_id = 12
  //   const logs = await this.logsRepo
  //     .createQueryBuilder('logs')
  //     .leftJoin('warter_user_gift', 'userGift', 'userGift.userId = logs.userId')
  //     .where('logs.waterGiftConfigId = :configId', { configId: 12 })
  //     .getMany();

  //   const results = [];
    
  //   for (const log of logs) {
  //     // Kiểm tra xem đã có waterusergift với cùng userId và updatedAt = createdAt của log chưa
  //     const existingUserGift = await this.userGiftRepo.findOne({
  //       where: {
  //         userId: log.userId,
  //         updatedAt: log.createdAt
  //       }
  //     });

  //     // Nếu đã tồn tại thì bỏ qua
  //     if (existingUserGift) {
  //       continue;
  //     }
      
  //     const rewardCode = generateRewardCode();
      
  //     // Tạo mới waterusergift
  //     const userGift = this.userGiftRepo.create({
  //       userId: log.userId,
  //       times: 1,
  //       currWaterGiftConfigId: 5,
  //       rewardCode: rewardCode,
  //       lastTimes: 0,
  //       createdAt: log.createdAt,
  //       updatedAt: log.createdAt
  //     });
      
  //     const savedUserGift = await this.userGiftRepo.save(userGift);
  //     results.push(savedUserGift);
  //   }

  //   return results;
  // }
}

export default new WaterGameService() 