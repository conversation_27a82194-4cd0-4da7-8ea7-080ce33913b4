/* eslint-disable @typescript-eslint/naming-convention */
import express, { <PERSON><PERSON> } from 'express'
import { authService } from '@/services'
import { MeSuccessResponse } from '@/types/responses/auth'
import { AuthorizedUserRequest } from '@/middlewares/auth'

const loginHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email, password, phone } = req.body;    
    const data = await authService.login(email, password, phone)
    
    return res.status(200).json(data);
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": e.code,
      "message": e.message
    });
  }
}

const loginByGoogleHandler = async (user) => {
  try {
    const data = await authService.loginViaSocial(user, 'google');

    if(data.error_code){
      return {
        "status": 200,
        "error_code": data.error_code,
        "message": data.message + ""
      };
    }
    
    return {
      "status": 200,
      "error_code": 0,
      data: data
    };
  } catch (e) {
    return {
      "status": 200,
      "error_code": e.code,
      "message": e.message
    };
  }
}

const loginByFacebookHandler = async (user) => {
  try { 
    const data = await authService.loginViaSocial(user, 'facebook')
    
    if(data.error_code){
      return {
        "status": 200,
        "error_code": data.error_code,
        "message": data.message + ""
      };
    }
    
    return {
      "status": 200,
      "error_code": 0,
      data: data
    };
  } catch (e) {
    return {
      "status": 200,
      "error_code": e.code,
      "message": e.message
    };
  }
}

//valid token-key
const authenticateHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await authService.authenticate(req, req, next);
    
    return res.status(200).json(data);
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": e.code,
      "message": e.message
    });
  }
}

const sendOtpViaEmailHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email } = req.body;    
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    let data = null;

    data = await authService.sendOtpViaEmail(email, otp);    

    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data":{
        email,
      }      
    };

    if(data.status == 0){
      response.error_code = 2;
      response.message = data.message;
    }

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e.message
    })
    //next(e)
  }
}

const verifyOtpViaEmailHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { e, o, t } = req.query;    
    let data = null;
    
    data = await authService.verifyOtpViaEmail(e, o, t);

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data":data   
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": e.code,
      "message": e.message
    })
    //next(e)
  }
}

// Logout
const logoutHandler: Handler = async (req, res: express.Response, next) => {
  try {
    const email = req.params.email;

    await authService.logout( email );

    const response = {
      "status": 200,
      "error_code": 0,
      "message": "logout success",
      "data": {} 
    }

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": e.code,
      "message": e.message
    })
    //next(e)
  }
}

// me
const meHandler: Handler = async (req: AuthorizedUserRequest, res: express.Response, next) => {
  try {
    const data = await authService.me(req.authorizedUser)
    const response: MeSuccessResponse = {
      code: '0',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

export { loginHandler, logoutHandler, meHandler, authenticateHandler, sendOtpViaEmailHandler, verifyOtpViaEmailHandler, loginByFacebookHandler, loginByGoogleHandler }