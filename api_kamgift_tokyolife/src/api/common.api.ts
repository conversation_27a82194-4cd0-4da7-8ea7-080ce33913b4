
/* eslint-disable @typescript-eslint/naming-convention */
import express, { <PERSON><PERSON> } from 'express'
import { commonService } from '@/services'
import { apiService } from '@/services'

const uploadAvatar = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      let data = null;
      let response = {
        "status": 200,
        "error_code": 0,
        "message": "success",
        data: {
          url: ""
        }
      }

      data = await commonService.uploadAvatar(req, res);
      
      // if(data != null && data.url != ""){
      //   await apiService.updateUserAvatar(req, data.url);

      //   response.data.url = data.url
      // }
      // else{
      //   response.error_code = 4;
      //   response.message = data.mesage;
      // }      
      
      res.status(200).json(response)

    } catch (e) {
      res.status(404).json(e)
      next(e)
    }
}


export { uploadAvatar }