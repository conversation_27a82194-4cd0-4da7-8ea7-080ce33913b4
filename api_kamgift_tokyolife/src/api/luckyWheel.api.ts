import express, { Request, Response } from 'express'
import { luckyWheelService } from '@/services'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { AppDataSource } from '@/config/config'
import { WaterUserGift } from '@/entities'

const waterUserGiftRepo = () => AppDataSource.getRepository(WaterUserGift)

const spinLuckyWheelHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId } = req.body
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    const result = await luckyWheelService.spinLuckyWheel(user.id, parseInt(campaignId) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getActivePrizesHandler = async (req: express.Request, res: express.Response) => {
  try {
    const { campaignId } = req.query
    const prizes = await luckyWheelService.getActivePrizes(parseInt(campaignId as string) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: { prizes }
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const addSpinCountHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId } = req.body
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    const spinCounts = await luckyWheelService.addSpinCount(user.id, parseInt(campaignId) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      message: 'Cộng lượt quay thành công',
      data: {
        spin_counts: spinCounts
      }
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getSpinHistoryHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId } = req.query
    const history = await luckyWheelService.getSpinHistoryByUser(user.id, parseInt(campaignId as string) || 0)
    const waterGifts = await waterUserGiftRepo().find({ 
      where: { 
        userId: user.id,
        campaignId: parseInt(campaignId as string) || 0,
        active: 0,
        times: 0
      }
    })
    
    const result = history.map(item => ({
      spin_time: item.spinTime,
      prize_name: item.prize?.name || null
    }))

    // Thêm thông tin từ waterUserGift
    waterGifts.forEach(gift => {
      result.push({
        spin_time: gift.createdAt,
        prize_name: `Mã dự thưởng: ${gift.rewardCode}`
      })
    })

    // Sắp xếp theo thời gian giảm dần
    result.sort((a, b) => new Date(b.spin_time).getTime() - new Date(a.spin_time).getTime())

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getDailyStatsController = async (req: Request, res: Response) => {
  try {
    const { campaignId } = req.query
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    const stats = await luckyWheelService.getDailyStats(parseInt(campaignId as string) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: stats
    })
  } catch (error) {
    return res.status(500).json({
      status: 500,
      error_code: 1,
      message: error.message
    })
  }
} 

export { spinLuckyWheelHandler, getActivePrizesHandler, addSpinCountHandler, getSpinHistoryHandler, getDailyStatsController } 