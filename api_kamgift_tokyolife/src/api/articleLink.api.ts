/* eslint-disable @typescript-eslint/naming-convention */
import express from 'express'
import { articleLinkService } from '@/services'
import { LinkRequest } from '@/types/requests/link'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { SuccessAPIResponse } from '@/types/responses/base'

const articleLinkListNotAuthHandler = async (req, res: express.Response, next: express.NextFunction) => {
  try {
    // list
    const data = await articleLinkService.getListNotAuth(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const articleLinkListHandler = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    // list
    const data = await articleLinkService.getList(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const readArticleLinkHandler = async (req: LinkRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await articleLinkService.read(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const extraSpinHandler = async (req: LinkRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await articleLinkService.extraSpin(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}


export { articleLinkListNotAuthHandler, articleLinkListHandler, readArticleLinkHandler, extraSpinHandler}

