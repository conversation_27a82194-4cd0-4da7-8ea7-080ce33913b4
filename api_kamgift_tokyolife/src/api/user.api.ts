/* eslint-disable @typescript-eslint/naming-convention */
import express from 'express'
import { userService } from '@/services'
import { UpdateUserRequest } from '@/types/requests/user'
import { AuthorizedUserRequest } from '@/middlewares/auth'

const getInfo = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const response = await userService.getInfo(req);

    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const updateInfo = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const response = await userService.userRegisFromAppEvent(req);

    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const updateUser = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const response = await userService.updateUser(req);

    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const updateUserInfo = async (req: UpdateUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const response = await userService.updateUserInfo(req);

    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const regUserByEmailHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email, password } = req.body;
    let data = null;
    
    if(!email){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "email is empty"
      });
    }
    
    if(!password){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "password is empty"
      });
    }

    data = await userService.regUserByEmail(email, password, req);
    
    let reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message + "";
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });

    //next(e)
  }
}

const verifyOtpViaEmailHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { e, o, k } = req.query;
    let data = null;
    
    data = await userService.verifyOtpViaEmail(e, o, k);

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": {
        id: data.data.id,
        name: data.data.name,
        email: data.data.email,
        role_id: data.data.roleId
      }
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": e.code,
      "message": e.message
    })
    //next(e)
  }
}

const verifyOtpViaEmailPostHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email, otp, token } = req.body;
    let data = null;
    
    data = await userService.verifyOtpViaEmail(email, otp, token);

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": {
        id: data.data.id,
        name: data.data.name,
        email: data.data.email,
        role_id: data.data.roleId,
        access_token: data.data.accessToken
      }
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 3,
      "message": e + ""
    })
    //next(e)
  }
}

const recoverPasswordByEmailHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email } = req.body;
    let data = null;
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    if(!email){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "email is empty"
      });
    }
    
    data = await userService.recoverPasswordByEmail(email, otp);
    
    let reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message + "";
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });

    //next(e)
  }
}

const resetPasswordByEmailHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email, otp, password } = req.body;
    let data = null;
        
    data = await userService.resetPasswordByEmail(email, otp, password);
    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message + "";
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });

    //next(e)
  }
}

const changePasswordByEmailHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email, password_old: passwordOld, password_new: passwordNew } = req.body;
    let data = null;

    if(!email){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "email is empty"
      });
    }
    
    data = await userService.changePasswordByEmail(email, passwordOld, passwordNew);
    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message + "";
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });

    //next(e)
  }
}

//valid otp
const validOtpHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { email, phone, otp } = req.body;    
    let data = null;
    
    data = await userService.verifyOtp(email, phone, otp);

    if(data.error_code){
      return res.status(200).json({
        "status": 200,
        "error_code": data.error_code,
        "message": data.message + ""
      })
    }

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 10,
      "message": e
    })
    //next(e)
  }
}

//phone's function
const regUserByPhoneHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { phone, password, method } = req.body;
    let data = null;
    let curMethod = "mobile";
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    if(!phone){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "Số điện thoại rỗng"
      });
    }
    
    if(!password){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "password rỗng"
      });
    }

    if(method == "zalo"){
      data = await userService.regUserViaZaloPhone(phone, password, otp);
      curMethod = "zalo";
    }
    else if(method == "mobile"){
      data = await userService.regUserViaPhone(phone, password, otp);
    }
    else
    {
      data = await userService.regUserViaZaloPhone(phone, password, otp);

      if(data.error_code != 0){
        data = await userService.regUserViaPhone(phone, password, otp);
      }
      else{
        if(data && data.error_code == 0){
          curMethod = "zalo";
        }
      }
    }
    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "res": data,
      "data":{
        "method": curMethod,
        phone,
      }
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message;
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });
    
    //next(e)
  }
}

const verifyOtpViaPhoneHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { phone, otp } = req.body;    
    let data = null;
    
    data = await userService.verifyOtpViaPhone(phone, otp);

    if(data.error_code){
      return res.status(200).json({
        "status": 200,
        "error_code": data.error_code,
        "message": data.message + ""
      })
    }

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 10,
      "message": e
    })
    //next(e)
  }
}

const recoverPasswordByPhoneHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { phone, method } = req.body;
    let data = null;
    let curMethod = "mobile";
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    if(!phone){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "phone is empty"
      });
    }

    if(method == "zalo"){
      data = await userService.recoverPasswordByZaloPhone(phone, otp);
    }else if(method == "mobile")
    {
      data = await userService.recoverPasswordByPhone(phone, otp);
    }
    else{
      data = await userService.recoverPasswordByZaloPhone(phone, otp);
      //const zaloResult = JSON.stringify(data);

      if(data.error_code != 0){
        data = await userService.recoverPasswordByPhone(phone, otp);
      }
      else{
        if(data && data.error_code == 0){
          curMethod = "zalo";
        }
      }
    }
    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      //"zaloResult": zaloResult,
      "method": curMethod,
      "data": data.data
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message + "";
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });

    //next(e)
  }
}

const resetPasswordByPhoneHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { phone, otp, password } = req.body;
    let data = null;
        
    data = await userService.resetPasswordByPhone(phone, otp, password);
    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message + "";
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });

    //next(e)
  }
}


//req-verify-phone-contact
const reqVerifyPhoneContactHandler = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    let data = null;
    const { method } = req.body;
    let curMethod = "mobile";

    if(method == "zalo"){
      data = await userService.reqVerifyZaloPhoneContact(req);
    }else if(method == "mobile"){
      data = await userService.reqVerifyPhoneContact(req);
    }
    else{
      data = await userService.reqVerifyZaloPhoneContact(req);

      if(data.error_code != 0){
        data = await userService.reqVerifyPhoneContact(req);
      }
      else{
        if(data && data.error_code == 0){
          curMethod = "zalo";
        }
      }
    }    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "method": curMethod,
      "data": data
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message;
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });
    
    //next(e)
  }
}

const verifyOtpViaPhoneContactHandler = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const { phone, otp } = req.body;    
    let data = null;
    
    data = await userService.verifyOtpViaPhoneContact(phone, otp, req);

    if(data.error_code){
      return res.status(200).json({
        "status": 200,
        "error_code": data.error_code,
        "message": data.message + ""
      })
    }

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data.data
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 10,
      "message": e
    })
    //next(e)
  }
}


const reloadZaloAuthenHandler = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const { isReset } = req.body;    
    let data = null;
    
    data = await userService.reloadZaloAuthen(req, isReset);

    if(data.error_code){
      return res.status(200).json({
        "status": 200,
        "error_code": data.error_code,
        "message": data.message + ""
      })
    }

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 10,
      "message": e
    })
    //next(e)
  }
}

const testSendSmsByZalo = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    let data = null;
    
    data = await userService.testSendSmsByZalo(req);

    if(data.error_code){
      return res.status(200).json({
        "status": 200,
        "error_code": data.error_code,
        "message": data.message + ""
      })
    }

    // output
    const response = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": data
    };

    return res.status(200).json(response)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 10,
      "message": e
    })
    //next(e)
  }
}

//google's function
const regUserByGoogleHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { access_token, email, google_id } = req.body;
    let data = null;

    if(!access_token){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "Chưa nhập access_token"
      });
    }

    if(!google_id){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "Chưa nhập google_id"
      });
    }
    
    if(!email){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "Chưa nhập email"
      });
    }

    data = await userService.regUserViaGoogle(access_token, email, google_id);
    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": {
        accessToken: "",
        email: email,
        id: 0,
        is_update_info: 0, 
        contact_phone: "",
        contact_phone_authened: 0
      }
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message;
      reqData.data = data.data;
    }
    else{
      reqData.data.accessToken = data.data.accessToken;
      reqData.data.email = data.data.email
      reqData.data.id = data.data.id;
      reqData.data.is_update_info = data.data.is_update_info || 0;
      reqData.data.contact_phone = data.data.contact_phone || "";
      reqData.data.contact_phone_authened = data.data.contact_phone_authened || 0;
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });
    
    //next(e)
  }
}

//facebook's function
const regUserByFacebookHandler = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const { access_token, email, facebook_id } = req.body;
    let data = null;

    if(!access_token){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "access_token rỗng"
      });
    }
    
    // if(!email){
    //   return res.status(200).json({
    //     "status": 200,
    //     "error_code": 1,
    //     "message": "email rỗng"
    //   });
    // }

    if(!facebook_id){
      return res.status(200).json({
        "status": 200,
        "error_code": 1,
        "message": "Chưa nhập facebook_id"
      });
    }

    data = await userService.regUserViaFacebook(access_token, email, facebook_id);
    
    const reqData = {
      "status": 200,
      "error_code": 0,
      "message": "success",
      "data": {
        accessToken: "",
        email: email,
        id: 0,
        is_update_info: 0, 
        contact_phone: "",
        contact_phone_authened: 0
      }
    };

    if(data.error_code != 0){
      reqData.error_code = data.error_code;
      reqData.message = data.message;
      reqData.data = data.data;
    }
    else{
      reqData.data.accessToken = data.data.accessToken;
      reqData.data.email = data.data.email;
      reqData.data.id = data.data.id      
      reqData.data.is_update_info = data.data.is_update_info;
      reqData.data.contact_phone = data.data.contact_phone;
      reqData.data.contact_phone_authened = data.data.contact_phone_authened;
    }

    return res.status(200).json(reqData)
  } catch (e) {
    return res.status(200).json({
      "status": 200,
      "error_code": 1,
      "message": e
    });
    
    //next(e)
  }
}

export { getInfo, updateInfo, 
  updateUser, updateUserInfo,
  regUserByEmailHandler, verifyOtpViaEmailHandler,
  validOtpHandler,
  verifyOtpViaEmailPostHandler, changePasswordByEmailHandler, 
  recoverPasswordByEmailHandler, resetPasswordByEmailHandler,
  regUserByPhoneHandler,reqVerifyPhoneContactHandler, verifyOtpViaPhoneContactHandler,
  recoverPasswordByPhoneHandler, resetPasswordByPhoneHandler,
  regUserByGoogleHandler, regUserByFacebookHandler,
  verifyOtpViaPhoneHandler, reloadZaloAuthenHandler, testSendSmsByZalo
}
