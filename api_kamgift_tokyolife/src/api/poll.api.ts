import express from 'express'
import { pollService } from '@/services'
import { AuthorizedUserRequest } from '@/middlewares/auth'

const getPollGuessHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {    
    const data = await pollService.getPollGuess(req);

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: data
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getPollUserHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {    
    const data = await pollService.getPollUser(req);

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: data
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const doPollGuessHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {    
    const data = await pollService.doPollGuess(req);

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: data
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}


const doPollUserHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {    
    const data = await pollService.doPollUser(req);

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: data
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getAllPollHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {    
    const data = await pollService.getAllPoll(req);

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: data
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getAllPollAnswerByPollIdHander = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const result = await pollService.getAllPollAnswerByPollId(req);

    return res.status(200).json({ status: 200, error_code: 0, data: result })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const doPollHander = async (req: AuthorizedUserRequest, res: express.Response) => {
   try {
    await pollService.doPoll(req);

    return res.status(200).json({ status: 200, error_code: 0, data: {} })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const didPollHander = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const rewardCodes = await pollService.didPoll(req)

    return res.status(200).json({ 
      status: 200, 
      error_code: 0, 
      data: { reward_codes: rewardCodes } 
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}


export {
  getPollGuessHandler, doPollGuessHandler, getPollUserHandler, doPollUserHandler,
  getAllPollHandler,
  getAllPollAnswerByPollIdHander,
  doPollHander,
  didPollHander
} 