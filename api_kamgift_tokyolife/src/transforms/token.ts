import { JWT } from '@/config/config'
import { AuthenticationToken, ResourceProvider, ResourceToken, TokenType } from '@/types/models/token'
import { JwtPayload } from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'

/**
 * Resource Token
 */
export const resourceToken = (path: string, provider: ResourceProvider) => {
  const now = Math.floor(Date.now() / 1000)
  const token: ResourceToken = {
    typ: TokenType.ResourceToken,
    path,
    provider,
    exp: now + JWT.RESOURCE_TOKEN_EXPIRE
  }
  return token
}

export const jwtPayloadToResourceToken = (payload: JwtPayload) => {
  if (!payload.typ || !payload.provider || !payload.path) {
    return null
  }
  const token: ResourceToken = {
    ...payload,
    typ: payload.typ,
    provider: payload.provider,
    path: payload.path
  }
  return token
}

/**
 * Authentication Token
 */
export const accessToken = (id: number, role: number) => {
  const now = Math.floor(Date.now() / 1000)
  const token: AuthenticationToken = {
    jti: uuidv4(),
    typ: TokenType.AccessToken,
    id,
    role,
    iss: "user-service",
    exp: now + JWT.ACCESS_TOKEN_EXPIRE
  }
  return token
}

export const refreshToken = (id: number, role: number) => {
  const now = Math.floor(Date.now() / 1000)
  const token: AuthenticationToken = {
    jti: uuidv4(),
    typ: TokenType.RefreshToken,
    id,
    role,
    iss: "user-service",
    nbf: now + JWT.ACCESS_TOKEN_EXPIRE - 60,
    exp: now + JWT.ACCESS_TOKEN_EXPIRE + JWT.REFRESH_TOKEN_EXPIRE
  }
  return token
}

export const jwtPayloadToAuthenticationToken = (payload: JwtPayload) => {
  if (!payload.id || !payload.role) {
    return null
  }
  const token: AuthenticationToken = {
    ...payload,
    typ: payload.typ,
    id: payload.id,
    role: payload.role
  }
  return token
}
