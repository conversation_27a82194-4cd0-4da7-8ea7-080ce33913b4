import { jwtService } from "@/services";
import { TokenType } from "@/types/models/token";
import { Socket } from "socket.io";

export const authSocketMiddleware = async (socket: Socket, next) => {
  // since you are sending the token with the query
  const token = socket.handshake.auth?.token as string;

  try {
    if (token === null) {
      // token empty
      return next(new Error("NOT AUTHORIZED"));
    }
    // token verify
    const payload = jwtService.verifyAuthenticationToken(token)
    if (!payload) {
      return next(new Error("NOT AUTHORIZED"));
    }
    if (payload.typ !== TokenType.AccessToken) {
      // Token type error
      return next(new Error("NOT AUTHORIZED"));
    }
    const isRevoked = await jwtService.isRevokedAuthenticationToken(payload.jti)
    if (isRevoked) {
      return next(new Error("NOT AUTHORIZED"));
    }
    socket.data.user = payload;
    next()
  } catch (e) {
    return next(new Error("NOT AUTHORIZED"));
  }
};
