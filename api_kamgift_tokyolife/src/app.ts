import express from 'express'
import helmet from 'helmet'
import compression from 'compression'
import cors, { CorsOptions } from 'cors'
import { errorConverter, errorHandler } from './middlewares/error'
import { morganSuccess<PERSON><PERSON><PERSON>, morganServerError<PERSON><PERSON>ler, morganClientErrorHandler } from '@/config/morgan'
import { IS_PRODUCTION } from '@/config/config'
import { rateLimit } from 'express-rate-limit'

import 'reflect-metadata'
import router from './routes'

const app = express()

// morgan
app.use(morganServerErrorHandler);
if (!IS_PRODUCTION) {
  app.use(morganSuccessHandler)
  app.use(morganClientErrorHandler)
}

// set security HTTP headers
app.use(helmet())

//use limit request
const limiter = rateLimit({
	windowMs: 5 * 60 * 1000, // 5 minutes
	limit: 10000, // Limit each IP to 100 requests per `window` (here, per 5 minutes).
	standardHeaders: 'draft-8', // draft-6: `RateLimit-*` headers; draft-7 & draft-8: combined `RateLimit` header
	legacyHeaders: false, // Disable the `X-RateLimit-*` headers.
	// store: ... , // Redis, Memcached, etc. See below.
});

app.use(limiter)

// parse json request body
app.use(express.json({ limit: '10mb' }))

// parse urlencoded request body
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// gzip compression
app.use(compression())

app.use('/images',express.static(__dirname + '../../images'));

// CORS
const corsOption: CorsOptions = {
  origin: '*',
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  preflightContinue: false,
  optionsSuccessStatus: 204,
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['Content-Disposition']
}
app.use(cors(corsOption));

app.use(router)

// send back a 404 error for any unknown api request
app.use((_req, _res, next) => {
  return _res.status(200).json({status: 200, code: "E404", error_code:"2", message: "Route not found"});

  //next(new ApiError(httpStatus.NOT_FOUND, 'E404'))
})

// convert error to ApiError, if needed
app.use(errorConverter)

// handle error
app.use(errorHandler)

export default app
