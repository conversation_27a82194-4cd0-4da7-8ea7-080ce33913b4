DROP TABLE IF EXISTS `tbl_users`;
CREATE TABLE `tbl_users` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) UNIQUE NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` ENUM ('ADMIN', 'NORMAL', 'TRADER'),
  `bio` text COMMENT 'about me',
  `avatar` varchar(1024),
  `referral_code` varchar(100),
  `referral` varchar(100),
  `status` ENUM ('ACTIVE', 'INACTIVE'),
  `last_login_time` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

DROP TABLE IF EXISTS `tbl_posts`;
CREATE TABLE `tbl_posts` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `title` varchar(255),
  `body` text COMMENT 'Content of the post',
  `user_id` integer,
  `status` varchar(255),
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

DROP TABLE IF EXISTS `tbl_password_resets`;
CREATE TABLE `tbl_password_resets` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

DROP TABLE IF EXISTS `tbl_factor_auth`;
CREATE TABLE `tbl_factor_auth` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `user_id` integer NOT NULL,
  `key` varchar(255) NOT NULL,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

DROP TABLE IF EXISTS `tbl_exchangers`;
CREATE TABLE `tbl_exchangers` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `user_id` int NOT NULL,
  `mst_exchanges_id` int NOT NULL,
  `trader_id` int DEFAULT NULL,
  `name_of_account` varchar(255) COLLATE NOT NULL,
  `api_key` varchar(255) COLLATE NOT NULL,
  `secret_key` varchar(255) COLLATE NOT NULL,
  `password` varchar(255) COLLATE DEFAULT NULL,
  `balances` json DEFAULT NULL,
  `trade_mode` enum('one_way_mode','hedge_mode') COLLATE DEFAULT NULL,
  `margin_mode` enum('isolated_mode','cross_mode') COLLATE DEFAULT NULL,
  `status` enum('ERROR','INACTIVE','ACTIVE','NO_ASSET') CHARACTER SET utf8mb4 COLLATE DEFAULT NULL,
  `enabled` tinyint(1) DEFAULT NULL,
  `favourite` tinyint(1) DEFAULT NULL,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` int DEFAULT NULL,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` int DEFAULT NULL,
  `portfolio_value` decimal(24,8) DEFAULT NULL
);

DROP TABLE IF EXISTS `tbl_bots`;
CREATE TABLE `tbl_bots` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `user_id` integer NOT NULL,
  `exchanger_id` integer,
  `trader_id` integer NOT NULL,
  `name` varchar(255),
  `about` varchar(1024),
  `time` varchar(20),
  `pairs` varchar(1024),
  `signalSource` ENUM ('trading_view', 'custom', 'runbot'),
  `order_type` ENUM ('market', 'limit'),
  `price_type` ENUM ('last', 'ask', 'bid'),
  `limit_order_type_percentage` float,
  `limit_order_type_amout` float,
  `time_to_live` integer,
  `tradingAmountType` ENUM ('fixed', 'percent'),
  `portfolio` varchar(100),
  `max_open_positions` integer,
  `fixed_order_amount` integer,
  `multiple_entries` boolean,
  `global_exit` boolean,
  `swing_trade` boolean,
  `reduce_only` boolean,
  `place_conditional_orders_on_exchange` boolean,
  `profit` json,
  `loss_percent` boolean,
  `stop_loss` float,
  `loss_move_percent` boolean,
  `stop_move_loss` float,
  `fixed_order_amount_symbol` varchar(20),
  `trailing_stop` boolean,
  `trailing_stop_activation` float,
  `trailing_stop_execute` float,
  `dca` boolean,
  `take_profit_base_on` ENUM ('average_price', 'entry_order'),
  `stop_loss_base_on` ENUM ('average_price', 'entry_order'),
  `extra_order_count` integer,
  `extra_order_deviation` integer,
  `extra_order_volume_multiplier` float,
  `extra_order_deviation_multiplier` float,
  `simulate_signal` boolean,
  `signal_code` varchar(255),
  `enabled` boolean DEFAULT false,
  `started` boolean DEFAULT false,
  `is_follow` boolean DEFAULT false,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

DROP TABLE IF EXISTS `tbl_bot_follows`;
CREATE TABLE `tbl_bot_follows` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `exchanger_id` integer,
  `bot_id` integer,
  `status` ENUM ('ACTIVE', 'INACTIVE'),
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

DROP TABLE IF EXISTS `tbl_traders`;
CREATE TABLE `tbl_traders` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `user_id` integer NOT NULL,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer,
  `name` varchar(255) NOT NULL,
  `followers` int DEFAULT NULL,
  `min_capital_requirement` float DEFAULT NULL,
  `follow_paid` bit(1) DEFAULT NULL,
  `follow_price` float DEFAULT NULL,
  `description` varchar(500) DEFAULT NULL,
  `pairs` varchar(500) DEFAULT NULL,
  `follow` bit(1) DEFAULT 0 NULL
);

DROP TABLE IF EXISTS `tbl_trader_follows`;
CREATE TABLE `tbl_trader_follows` (
  `id` int NOT NULL AUTO_INCREMENT,
  `exchanger_id` int DEFAULT NULL,
  `trader_id` int DEFAULT NULL,
  `status` enum('ACTIVE','INACTIVE') COLLATE DEFAULT NULL,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` int DEFAULT NULL,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` int DEFAULT NULL,
  `following_type` enum('all_actions','entry_exit_signals') COLLATE DEFAULT NULL,
  `following_setting` enum('trader','custom') COLLATE DEFAULT NULL,
  `trading_amount_type` enum('percent','fixed') COLLATE DEFAULT NULL,
  `portfolio` decimal(5,2) DEFAULT NULL,
  `fixed_order_amount` decimal(24,8) DEFAULT NULL,
  `leverage` int DEFAULT NULL,
  PRIMARY KEY (`id`)
);

-- DROP TABLE IF EXISTS `tbl_trader_bots`;
-- CREATE TABLE `tbl_trader_bots` (
--   `id` integer AUTO_INCREMENT PRIMARY KEY,
--   `trader_id` integer NOT NULL,
--   `exchanger_id` integer,
--   `name` varchar(255),
--   `about` varchar(1024),
--   `time` varchar(20),
--   `pairs` varchar(1024),
--   `signalSource` ENUM ('trading_view', 'custom', 'runbot'),
--   `order_type` ENUM ('market', 'limit'),
--   `price_type` ENUM ('last', 'ask', 'bid'),
--   `limit_order_type_percentage` float,
--   `limit_order_type_amout` float,
--   `time_to_live` integer,
--   `tradingAmountType` ENUM ('fixed', 'percent'),
--   `portfolio` varchar(100),
--   `max_open_positions` integer,
--   `fixed_order_amount` integer,
--   `multiple_entries` boolean,
--   `global_exit` boolean,
--   `swing_trade` boolean,
--   `reduce_only` boolean,
--   `place_conditional_orders_on_exchange` boolean,
--   `profit` json,
--   `loss_percent` boolean,
--   `stop_loss` float,
--   `loss_move_percent` boolean,
--   `stop_move_loss` float,
--   `fixed_order_amount_symbol` varchar(20),
--   `trailing_stop` boolean,
--   `trailing_stop_activation` float,
--   `trailing_stop_execute` float,
--   `dca` boolean,
--   `take_profit_base_on` ENUM ('average_price', 'entry_order'),
--   `stop_loss_base_on` ENUM ('average_price', 'entry_order'),
--   `extra_order_count` integer,
--   `extra_order_deviation` integer,
--   `extra_order_volume_multiplier` float,
--   `extra_order_deviation_multiplier` float,
--   `simulate_signal` boolean,
--   `signal_code` varchar(255),
--   `deleted_at` datetime(6) DEFAULT NULL,
--   `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
--   `created_by` integer,
--   `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
--   `updated_by` integer
-- );

DROP TABLE IF EXISTS `mst_exchanges`;
CREATE TABLE `mst_exchanges` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `code` varchar(255) NOT NULL,
  `type` ENUM ('spot', 'usdtm', 'coinm'),
  `referral_link` varchar(255),
  `ips` varchar(255) NOT NULL,
  `trade_mode_support` boolean,
  `margin_mode_support` boolean,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

DROP TABLE IF EXISTS `mst_pairs`;
CREATE TABLE `mst_pairs` (
  `id` integer AUTO_INCREMENT PRIMARY KEY,
  `symbol` varchar(255),
  `code` varchar(255) NOT NULL,
  `defaultCode` varchar(255) NOT NULL,
  `exchange` varchar(255) NOT NULL,
  `type` varchar(255),
  `refCurrency` varchar(255) NOT NULL,
  `baseCurrency` varchar(255) NOT NULL,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

CREATE TABLE `tbl_general_statistics` (
  `id` integer PRIMARY KEY AUTO_INCREMENT,
  `ref_id` integer,
  `type` ENUM ('TRADER', 'BOT'),
  `stats` ENUM ('ALL', 'MONTH', 'WEEK'),
  `statistic_day` date,
  `avg_pl` float,
  `roi` float,
  `max_loss_gain` float,
  `volume` float,
  `min_volume` float,
  `max_volume` float,
  `avg_volume` float,
  `trade_count` integer,
  `trade_avg_time` float,
  `profit_factor` float,
  `profit_volume` float,
  `percent_profitable` float,
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

CREATE TABLE `tbl_positions` (
  `id` integer PRIMARY KEY AUTO_INCREMENT,
  `user_id` integer,
  `trader_id` integer,
  `orderId` varchar(100),
  `symbol` varchar(30),
  `side` ENUM ('SELL', 'BUY'),
  `positionSide` varchar(20),
  `type` varchar(20),
  `timeInForce` varchar(20),
  `origQty` integer,
  `executedQty` integer,
  `price` decimal(24,8) DEFAULT "0.0000",
  `avgPrice` decimal(24,8) DEFAULT "0.0000",
  `reduceOnly` boolean,
  `stopPrice` decimal(24,8),
  `closePosition` boolean,
  `activationPrice` decimal(24,8) DEFAULT "0.0000",
  `callbackRate` varchar(30),
  `workingType` varchar(30),
  `status` varchar(30),
  `clientOrderId` varchar(100),
  `cumQuote` decimal(24,8),
  `priceProtect` boolean,
  `leverage` integer,
  `priceMatch` varchar(30),
  `selfTradePreventionMode` varchar(30),
  `goodTillDate` integer,
  `origType` varchar(20),
  `realizedPnl` varchar(20),
  `marginAsset` varchar(20),
  `quoteQty` decimal(24,8),
  `commission` decimal(24,8),
  `commissionAsset` varchar(20),
  `transaction_time` bigint,
  `updateTime` bigint,  
  `openTime` bigint,
  `profit` json,
  `exchanger_type` ENUM ('spot', 'usdtm', 'coinm'),
  `entryPrice` decimal(24,8),
  `breakEvenPrice` decimal(24,8),
  `markPrice` decimal(24,8),
  `liquidationPrice` decimal(24,8),
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

CREATE TABLE `tbl_live_positions` (
  `id` integer PRIMARY KEY AUTO_INCREMENT,
  `user_id` integer,
  `trader_id` integer,
  `orderId` varchar(100),
  `symbol` varchar(30),
  `side` ENUM ('SELL', 'BUY'),
  `positionSide` varchar(20),
  `type` varchar(20),
  `timeInForce` varchar(20),
  `origQty` integer,
  `executedQty` integer,
  `price` decimal(24,8) DEFAULT "0.0000",
  `avgPrice` decimal(24,8) DEFAULT "0.0000",
  `reduceOnly` boolean,
  `stopPrice` decimal(24,8),
  `closePosition` boolean,
  `activationPrice` decimal(24,8) DEFAULT "0.0000",
  `callbackRate` varchar(30),
  `workingType` varchar(30),
  `status` varchar(30),
  `clientOrderId` varchar(100),
  `cumQuote` decimal(24,8),
  `priceProtect` boolean,
  `leverage` integer,
  `priceMatch` varchar(30),
  `selfTradePreventionMode` varchar(30),
  `goodTillDate` integer,
  `origType` varchar(20),
  `realizedPnl` varchar(20),
  `marginAsset` varchar(20),
  `quoteQty` decimal(24,8),
  `commission` decimal(24,8),
  `commissionAsset` varchar(20),
  `transaction_time` bigint,
  `updateTime` bigint,  
  `openTime` bigint,
  `profit` json,
  `exchanger_type` ENUM ('spot', 'usdtm', 'coinm'),
  `entryPrice` decimal(24,8),
  `breakEvenPrice` decimal(24,8),
  `markPrice` decimal(24,8),
  `liquidationPrice` decimal(24,8),
  `live_status` ENUM ('ACTIVE', 'INACTIVE'),
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

CREATE TABLE `tbl_all_orders` (
  `id` integer PRIMARY KEY AUTO_INCREMENT,
  `user_id` integer,
  `trader_id` integer,
  `orderId` varchar(100),
  `symbol` varchar(30),
  `side` ENUM ('SELL', 'BUY'),
  `positionSide` varchar(20),
  `type` varchar(20),
  `timeInForce` varchar(20),
  `origQty` integer,
  `executedQty` integer,
  `price` decimal(24,8) DEFAULT "0.0000",
  `avgPrice` decimal(24,8) DEFAULT "0.0000",
  `reduceOnly` boolean,
  `stopPrice` decimal(24,8),
  `closePosition` boolean,
  `activationPrice` decimal(24,8) DEFAULT "0.0000",
  `callbackRate` varchar(30),
  `workingType` varchar(30),
  `status` varchar(30),
  `clientOrderId` varchar(100),
  `cumQuote` decimal(24,8),
  `priceProtect` boolean,
  `leverage` integer,
  `priceMatch` varchar(30),
  `selfTradePreventionMode` varchar(30),
  `goodTillDate` integer,
  `origType` varchar(20),
  `realizedPnl` varchar(20),
  `marginAsset` varchar(20),
  `quoteQty` decimal(24,8),
  `commission` decimal(24,8),
  `commissionAsset` varchar(20),
  `transaction_time` bigint,
  `updateTime` bigint,  
  `exchanger_type` ENUM ('spot', 'usdtm', 'coinm'),
  `entryPrice` decimal(24,8),
  `breakEvenPrice` decimal(24,8),
  `markPrice` decimal(24,8),
  `liquidationPrice` decimal(24,8),
  `deleted_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_by` integer,
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `updated_by` integer
);

ALTER TABLE `tbl_posts` ADD FOREIGN KEY (`user_id`) REFERENCES `tbl_users` (`id`);

ALTER TABLE `tbl_factor_auth` ADD FOREIGN KEY (`user_id`) REFERENCES `tbl_users` (`id`);

ALTER TABLE `tbl_exchangers` ADD FOREIGN KEY (`user_id`) REFERENCES `tbl_users` (`id`);

ALTER TABLE `tbl_bots` ADD FOREIGN KEY (`exchanger_id`) REFERENCES `tbl_exchangers` (`id`);

ALTER TABLE `tbl_bot_follows` ADD FOREIGN KEY (`exchanger_id`) REFERENCES `tbl_exchangers` (`id`);

ALTER TABLE `tbl_bot_follows` ADD FOREIGN KEY (`bot_id`) REFERENCES `tbl_bots` (`id`);
