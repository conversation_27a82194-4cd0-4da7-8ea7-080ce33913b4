import { JwtPayload } from 'jsonwebtoken'

export enum TokenType {
  AccessToken = 'access_token',
  RefreshToken = 'refresh_token',
  ResourceToken = 'resource_token'
}

export enum ResourceProvider {
  S3 = 's3'
}

export interface ResourceToken extends JwtPayload {
  typ: TokenType
  provider: ResourceProvider
  path: string
}
export interface AuthenticationToken extends JwtPayload {
  typ: TokenType
  id: number
  role: number
}
