export {};

declare global {
  namespace NodeJS {
    interface Global {
      ZaloAuthenInfo: {
        secretKey: string;
        appId: string;
        enableSmsZaloNotExisted: string;
        loginTemplateId: string;
        refreshToken: string;
        accessToken: string;
        timeRefreshToken: number;
        lastTimeDoRefreshToken: number;
        isReloadAuthenInfo: number;
        reloadToken: number
      };
    }
  }
}