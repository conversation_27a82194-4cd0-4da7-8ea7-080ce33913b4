import { UserRole, UserStatus } from '@/entities/TblUsers'
import { DataBaseResponse } from './base'
import { PagedData } from '../models/pagination'

export interface UserResponse {
  message: string
  userId: number
}

export interface CreateUserSuccessResponse extends DataBaseResponse<UserResponse> {
  data: UserResponse
}

export interface UpdateUserSuccessResponse extends DataBaseResponse<UserResponse> {
  data: UserResponse
}

export interface ActiveUserSuccessResponse extends DataBaseResponse<UserResponse> {
  data: UserResponse
}

export interface DeactiveUserSuccessResponse extends DataBaseResponse<UserResponse> {
  data: UserResponse
}

export interface UserDetailResponse {
  id: number
  email: string
  name: string
  role: UserRole | null
  status: UserStatus | null
  avatar: string
  createdAt: Date
  updatedAt: Date
}

export interface UserSearchDetailResponse {
  id: number
  email: string
  name: string
  role: UserRole | null
  status: UserStatus | null
  avatar: string | null
}

export interface SearchUserDetailResponse extends DataBaseResponse<PagedData<UserSearchDetailResponse>> {
  data: PagedData<UserSearchDetailResponse>
}
