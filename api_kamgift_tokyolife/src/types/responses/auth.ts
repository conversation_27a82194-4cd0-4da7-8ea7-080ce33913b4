import { DataBaseResponse } from './base'

export interface UserCredentialsData {
  access_token: string
  refresh_token: string
  user: UserData
}

export interface UserData {
  id: number
  name: string
  email: string
  phone: string
  roleId: number
  active: number
  avatar: string
  createdAt: Date
  updatedAt: Date
  accessToken: string,
  is_update_info: number,
  contact_phone: string,
  contact_phone_authened: number
}

export interface LoginSuccessResponse extends DataBaseResponse<UserCredentialsData> {
  data: UserCredentialsData
}

export interface MeSuccessResponse extends DataBaseResponse<UserData> {
  data: UserData
}

export type RefreshSuccessResponse = LoginSuccessResponse
