import { AuthorizedUserRequest } from '@/middlewares/auth'
import { BaseSearchRequest } from './base'


export interface CreateRoleRequest extends AuthorizedUserRequest {
  name: string
  permissions: number[]
}

export interface UpdateRoleRequest extends AuthorizedUserRequest {
  id: number
  name: string
  permissions: number[]
}

export interface RoleRequest extends AuthorizedUserRequest {
  id: number
}

export interface RoleSearchRequest extends BaseSearchRequest {
  name: string
}
