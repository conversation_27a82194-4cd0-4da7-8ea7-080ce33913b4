import { UserRole, UserStatus } from '@/entities/TblUsers'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { BaseSearchRequest } from './base'

export interface CreateUserRequest extends AuthorizedUserRequest {
  email: string
  password: string
  confirmPassword: string
  name: string
  role: UserRole | null
  status: UserStatus | null
  groups: string[]
}

export interface UpdateUserRequest extends AuthorizedUserRequest {
  id: number
  name: string
  role: UserRole | null
  status: UserStatus | null
  groups: string[]
}

export interface ActiveUserRequest extends AuthorizedUserRequest {
  id: number
  status: UserStatus | null
}

export interface ChangePasswordRequest extends AuthorizedUserRequest {
  id: number
  oldPassword: string
  newPassword: string
}

export interface SetPasswordRequest extends AuthorizedUserRequest {
  id: number
  password: string
}

export interface DeleteUserRequest extends AuthorizedUserRequest {
  id: number
}

export interface UserSearchRequest extends BaseSearchRequest {
  email: string
  name: string
  roles?: string
  query?: string
}

export interface UserAddOneVoucher extends AuthorizedUserRequest {
  user_id: number
  storage_id: number
}

export interface UserUpdateAddressbook extends AuthorizedUserRequest {
  id: number
  name: string
  city_id: number
  district_id: number
  commune_id: number
  default: number
  address: string
  phone: string
}