import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('polls')
export class TblPolls extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('varchar', { name: 'description'})
  description: string

  @Column('varchar', { name: 'success_text' })
  successText: string

  @Column('tinyint', { name: 'status' })
  status: number

  @Column('int', { name: 'display_order' })
  displayOrder: number

  @Column('timestamp', { name: 'start_time' })
  startTime: Date

  @Column('timestamp', { name: 'end_time' })
  endTime: Date

  @Column('int', { name: 'type' })
  type: number
}