import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('game_files', { schema: 'events' })
export class TblGameFiles extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'file_name', length: 255 })
  fileName: string

  @Column('varchar', { name: 'file_path', length: 500 })
  filePath: string

  @Column('longtext', { name: 'content' })
  content: string

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'file_type' })
  fileType: number
}
