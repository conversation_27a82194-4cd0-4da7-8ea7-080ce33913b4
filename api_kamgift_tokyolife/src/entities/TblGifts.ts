import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('gifts', { schema: 'events' })
export class TblGifts extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('int', { name: 'total' })
  total: number

  @Column('int', { name: 'remain' })
  remain: number

  @Column('int', { name: 'event_id' })
  eventId: number

  @Column('varchar', { name: 'price', length: 255 })
  price: string

  @Column('varchar', { name: 'image', length: 255 })
  image: string

  @Column('float', { name: 'probability' })
  probability: number

  @Column('int', { name: 'type' })
  type: number
}
