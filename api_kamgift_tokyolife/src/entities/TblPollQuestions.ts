import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('poll_questions')
export class TblPollQuestions extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'poll_id' })
  pollId: number

  @Column('varchar', { name: 'question' })
  question: string

  @Column('varchar', { name: 'description'})
  description: string

  @Column('tinyint', { name: 'is_required' })
  isRequired: number

  @Column('tinyint', { name: 'status' })
  status: number

  @Column('int', { name: 'display_order' })
  displayOrder: number

  @Column('tinyint', { name: 'is_multiple' })
  isMultiple: number
}