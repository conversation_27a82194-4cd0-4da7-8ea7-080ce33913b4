import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('zalo_authen')
export class TblZaloAuthens extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'secret_key', length: 3000 })
  secretKey: string

  @Column('varchar', { name: 'app_id', length: 50 })
  appId: string

  @Column('varchar', { name: 'login_template_id', length: 50 })
  loginTemplateId: string

  @Column('varchar', { name: 'refresh_token', length: 3000 })
  refreshToken: string

  @Column('varchar', { name: 'access_token', length: 3000 })
  accessToken: string

  @Column('int', { name: 'time_refresh_token' })
  timeRefreshToken: number

  @Column('bigint', { name: 'last_time_refresh_token' })
  lastTimeDoRefreshToken: number

  @Column('int', { name: 'enable_sms_zalo_not_existed' })
  enableSmsZaloNotExisted: number

  @Column('int', { name: 'reloadToken' })
  reloadToken: number
} 