import { TblArticleLink } from './TblArticleLink'
import { Column, Entity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUsers } from './TblUsers'

@Entity('user_article_link', { schema: 'events' })
export class TblUserArticleLink extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id', nullable: false })
  userId: number

  @Column('int', { name: 'article_link_id', nullable: false })
  articleLinkId: number

  @Column('int', { name: 'read', nullable: false })
  read: number

  // @ManyToOne(() => TblArticleLink, (articleLink) => articleLink.userArticleLinks)
  // @JoinColumn({ name: 'campaign_id' })
  // articleLink: TblArticleLink

  // @ManyToOne(() => TblUsers, (user) => user.userArticleLinks)
  // @JoinColumn({ name: 'game_id' })
  // usesr: TblUsers

  @ManyToOne(() => TblArticleLink, (articleLink) => articleLink.userArticleLinks)
  @JoinColumn({ name: 'article_link_id' })
  articleLink: TblArticleLink

  @ManyToOne(() => TblUsers, (user) => user.userArticleLinks)
  @JoinColumn({ name: 'user_id' })
  usesr: TblUsers
}
