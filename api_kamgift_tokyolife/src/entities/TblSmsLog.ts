import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('sms_log')
export class TblSmsLog extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'phone', length: 255 })
  phone: string

  @Column('int', { name: 'type' })
  type: number

  @Column('int', { name: 'status' })
  status: number

  @Column('varchar', { name: 'param' })
  param: string

  @Column('varchar', { name: 'result'})
  result: string
} 