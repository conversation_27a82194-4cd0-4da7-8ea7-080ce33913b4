import { Column, Entity, PrimaryGeneratedColumn} from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('city', { schema: 'events' })
export class TblCities extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('varchar', { name: 'slug', length: 500 })
  slug: string
  
  //created_at

  //updated_at
}
