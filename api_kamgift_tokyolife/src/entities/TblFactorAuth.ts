import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { TblUsers } from './TblUsers'
import { BaseEntity } from './base/BaseEntity'

@Index('user_id', ['userId'], {})
@Entity('factor_auth', { schema: 'events' })
export class TblFactorAuth extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('varchar', { name: 'key', length: 255 })
  key: string

  @ManyToOne(() => TblUsers, (tblUsers) => tblUsers.tblFactorAuths, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION'
  })
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: TblUsers
}
