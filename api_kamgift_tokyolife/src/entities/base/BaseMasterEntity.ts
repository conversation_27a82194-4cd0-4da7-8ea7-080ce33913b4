import { Column, CreateDateColumn, VersionColumn } from 'typeorm'

export abstract class BaseMasterEntity {
  @CreateDateColumn({
    name: 'created_at',
    comment: 'created_at'
  })
  createdAt: Date

  @Column('int', {
    name: 'created_by',
    comment: 'created_by',
    nullable: true
  })
  createdBy: number | null

  @Column('datetime', {
    name: 'deleted_at',
    nullable: true,
    comment: 'deleted_at'
  })
  deletedAt?: Date | null
}
