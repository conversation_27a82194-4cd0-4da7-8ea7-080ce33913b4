import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('flowers', { schema: 'events' })
export class TblFlower extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'petals' })
  petals: number

  @Column('int', { name: 'status' })
  status: number

  @Column('date', { name: 'last_watered' })
  last_watered: Date

  @Column('int', { name: 'streak_days' })
  streakDays: number
}
