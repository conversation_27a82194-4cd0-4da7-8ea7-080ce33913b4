import { Column, Entity, PrimaryGeneratedColumn, ManyToMany, OneToMany } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('games', { schema: 'events' })
export class TblGames extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('varchar', { name: 'slug', length: 255 })
  slug: string

  @Column('varchar', { name: 'thumb', length: 255 })
  thumb: string

  @Column('varchar', { name: 'image', length: 255 })
  image: string

  @Column('varchar', { name: 'preview_image', length: 255 })
  previewImage: string

  @Column('varchar', { name: 'image_list', length: 2000 })
  image_list: string

  @Column('varchar', { name: 'link_preview', length: 2000 })
  linkPreview: string

  @Column('varchar', { name: 'video', length: 500 })
  video: string

  @Column('int', { name: 'active' })
  active: number

  @Column('int', { name: 'gameConfig_id' })
  gameConfigId: number

  @Column('int', { name: 'external', nullable: false })
  external: number

  @Column('int', { name: 'external_id', nullable: false })
  externalId: number

  @Column('int', { name: 'status', nullable: false })
  status: number

  @Column('int', { name: 'is_feature', nullable: false })
  is_feature: number

  @Column('int', { name: 'is_hot', nullable: false })
  is_hot: number

  @Column('varchar', { name: 'short_desc', length: 2000 })
  short_desc: string

  @Column('varchar', { name: 'full_desc', length: 2000 })
  full_desc: string

  @Column('longtext', { name: 'config_fields' })
  configFields: string

  @Column('longtext', { name: 'preview_html' })
  previewHtml: string

  @Column('datetime', { name: 'start_date' })
  start_date: Date

  @Column('datetime', { name: 'end_date' })
  end_date: Date

  @Column('int', { name: 'time_number', nullable: true })
  time_number: number

  @Column('tinyint', { name: 'type'})
  type: number
}
