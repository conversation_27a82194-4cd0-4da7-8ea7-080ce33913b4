import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('game_logs', { schema: 'events' })
export class TblGameLog extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'gift_remain' })
  giftRemain: number

  @Column('int', { name: 'gift_given' })
  giftGiven: number

  @Column('int', { name: 'voucher_given' })
  voucherGiven: number

  @Column('int', { name: 'plays' })
  plays: number

  @Column('int', { name: 'game_id' })
  gameId: number
}
