import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('water_rewards', { schema: 'events' })
export class TblWaterReward extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'milestone' })
  milestone: number

  @Column('varchar', { name: 'reward_code', length: 100 })
  rewardCode: string
}
