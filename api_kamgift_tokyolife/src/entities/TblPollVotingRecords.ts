import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('poll_voting_record')
export class TblPollVotingRecords extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'poll_answer_id' })
  pollAnswerId: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'is_upvote' })
  isUpvote: number
}