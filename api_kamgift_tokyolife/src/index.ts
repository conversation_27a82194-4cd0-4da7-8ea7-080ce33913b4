import app from './app'
import { APP_PORT, AppDataSource, DB, ENABLED_SOCKET } from '@/config/config'
import logger from '@/config/logger'
import * as http from 'http';
import e = require('express');

const Config = async () => {}

const key = process.env.APIKEY || 'XC8QhR9k6TV6IHFQEg6NQ2hA66TKxSvY5z0Sy3JyjfMj9Y6x3WhfKPgR4yI6mv1x'
const secret = process.env.APISECRET || 'rgswNCZi5HR8FkmC6tSSp8qdkbZik8mCfcrMFcWmU19xLJm45iCzfpiLUtdcCEVj'

global.traderRunning = new Map([])
global.userExchangerRunning = new Map([])
global.userServerSocket = new Map([])

// initialize a simple http server
const server = http.createServer(app);

const RunApp = () => {
  logger.info('Connecting to database...')
  console.log('Database config: ', JSON.stringify(DB))

  AppDataSource.initialize()
    .then((conn) => {
      logger.info('Connect database ok!')
      server.listen(APP_PORT, () => {
        logger.info(`Server listening on ${APP_PORT}`)
        
      })

      process.on('SIGINT', () => {
        logger.info('App exiting...')
        process.exit(0)
      })
    })
    .catch((error) => {
      logger.info('Database connection error.')
      logger.error(error)
      process.exit(1)
    })

  process.on('uncaughtException', (err) => {
    logger.error('Uncaught Exception: ' + err)
    process.exit(1)
  })
}

logger.info('Content AI backend starting...')
Config().then(RunApp)
