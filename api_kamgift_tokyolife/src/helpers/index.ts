import { SortType } from '@/types/models/sortType'

export function isJsonString(str) {
  try {
    JSON.parse(str)
  } catch (e) {
    return false
  }

  return true
}

module.exports.htmlEntities = (str) => {
  str = str.replace(/&/g, '&amp')
  str = str.replace(/>/g, '&gt')
  str = str.replace(/</g, '&lt')
  str = str.replace(/"/g, '&#039')
  str = str.replace(/'/g, '&quot')

  return str
}

export const tagify = (text, replaceChar = '') => {
  //Đổi chữ hoa thành chữ thường
  let slug = text.toLowerCase()
  //Đổi ký tự có dấu thành không dấu
  slug = slug.replace(/á|à|ả|ạ|ã|ă|ắ|ằ|ẳ|ẵ|ặ|â|ấ|ầ|ẩ|ẫ|ậ/gi, 'a')
  slug = slug.replace(/é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ/gi, 'e')
  slug = slug.replace(/i|í|ì|ỉ|ĩ|ị/gi, 'i')
  slug = slug.replace(/ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ/gi, 'o')
  slug = slug.replace(/ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự/gi, 'u')
  slug = slug.replace(/ý|ỳ|ỷ|ỹ|ỵ/gi, 'y')
  slug = slug.replace(/đ/gi, 'd')
  //Xóa các ký tự đặt biệt
  slug = slug.replace(/\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\|_/gi, '')
  //Đổi khoảng trắng thành ký tự gạch ngang
  slug = slug.replace(/ /gi, replaceChar)
  //Đổi nhiều ký tự gạch ngang liên tiếp thành 1 ký tự gạch ngang
  //Phòng trường hợp người nhập vào quá nhiều ký tự trắng
  slug = slug.replace(/\-\-\-\-\-/gi, '-')
  slug = slug.replace(/\-\-\-\-/gi, '-')
  slug = slug.replace(/\-\-\-/gi, '-')
  slug = slug.replace(/\-\-/gi, '-')
  //Xóa các ký tự gạch ngang ở đầu và cuối
  slug = '@' + slug + '@'
  slug = slug.replace(/\@\-|\-\@|\@/gi, '')
  //In slug ra textbox có id “slug”
  return slug
}

export function sortAndPagingParams(page_number: number | null, page_size: number | null, order_by: string | null, order_type: string | null) {
  order_by = order_by || 'id'
  order_type = (order_type || 'desc').toLowerCase() == 'desc' ? SortType.DESC : SortType.ASC
  page_number = page_number >= 1 ? page_number - 1 : 0

  const limit = page_size > 0 ? page_size : 20
  const offset = page_number * limit

  return {
    order_by,
    order_type,
    limit,
    offset,
    page_number
  }
}

export function sortAndPaging(req: any) {
  let { page_number, page_size, order_by, order_type } = req
  return sortAndPagingParams(page_number, page_size, order_by, order_type)
}

export const subtractDays = (days: number, inputDate: Date = new Date()) => {
  var today = new Date(inputDate)
  var date = new Date(today.getFullYear(), today.getMonth(), today.getDate() - days)
  return date
}

export const getLastWeek = () => {
  return subtractDays(7)
}

export const getLastMonth = () => {
  return subtractDays(30)
}

export const getLastDay = () => {
  return subtractDays(1)
}

export const getStartDayOfMonth = () => {
  var today = new Date()
  var date = new Date(today.getFullYear(), today.getMonth(), 1)
  return date
}

export const getStartDayOfAgoMonth = () => {
  var today = new Date()
  var date = new Date(today.getFullYear(), today.getMonth() - 1, 1)
  return date
}

export const formatDate = (date) => {
  let d = new Date(date),
    month = '' + (d.getMonth() + 1),
    day = '' + d.getDate(),
    year = d.getFullYear()

  if (month.length < 2) month = '0' + month

  if (day.length < 2) day = '0' + day

  return [year, month, day].join('-')
}

export const millisecondsToTime = (input) => {
  const duration = input ? input : 0
  let milliseconds = Math.floor((duration % 1000) / 100),
    seconds = Math.floor((duration / 1000) % 60),
    minutes = Math.floor((duration / (1000 * 60)) % 60),
    hours = Math.floor((duration / (1000 * 60 * 60)) % 24),
    days = Math.floor(duration / (1000 * 60 * 60 * 24))

  let hours_str = hours < 10 ? '0' + hours : hours
  let minutes_str = minutes < 10 ? '0' + minutes : minutes
  let seconds_str = seconds < 10 ? '0' + seconds : seconds

  return days + 'd. ' + hours_str + 'h. ' + minutes_str + 'min.' + seconds_str + 'sec.' // + milliseconds
}
