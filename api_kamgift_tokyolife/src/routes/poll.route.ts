import express from 'express'
import { pollAPI } from '@/api'
import { authenticate, noAuthenticate } from '@/middlewares/auth'

const router = express.Router()
router.get('/polls-guest/', noAuthenticate, pollAPI.getPollGuessHandler)
router.post('/polls-guest/', noAuthenticate, pollAPI.doPollGuessHandler)
router.get('/polls-user/', authenticate, pollAPI.getPollUserHandler)
router.post('/polls-user/', authenticate, pollAPI.doPollUserHandler)

router.get('/polls/', authenticate, pollAPI.getAllPollHandler)
router.get('/polls/:id', authenticate, pollAPI.getAllPollAnswerByPollIdHander)
router.get('/polls/:id/didpoll', authenticate, pollAPI.didPollHander)
router.post('/polls/:id', authenticate, pollAPI.doPollHander)

export default router 