import express from 'express'
import { luckyWheelAPI } from '@/api'
import { authenticate } from '@/middlewares/auth'

const router = express.Router()
router.post('/lucky-wheel/spin', authenticate, luckyWheelAPI.spinLuckyWheelHandler)
router.get('/lucky-wheel/prizes', luckyWheelAPI.getActivePrizesHandler)
router.post('/lucky-wheel/add-spin', authenticate, luckyWheelAPI.addSpinCountHandler)
router.get('/lucky-wheel/history', authenticate, luckyWheelAPI.getSpinHistoryHandler)
router.get('/lucky-wheel/stats/daily', luckyWheelAPI.getDailyStatsController)
export default router 