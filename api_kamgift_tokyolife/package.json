{"name": "events-be", "version": "1.0.0", "description": "", "private": true, "scripts": {"start": "node --max-semi-space-size=128 --max-old-space-size=3072 --expose-gc -r tsconfig-paths/register build/index.js", "start:dev": "ts-node-dev --max-semi-space-size=128 --max-old-space-size=3072 --expose-gc -r tsconfig-paths/register src/index.ts --files", "build": "rm -rf ./build/ && npx tsc", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write 'src/**/*.{ts,js}'", "typeorm": "ts-node-dev -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "typeorm:revert": "yarn typeorm migration:revert", "typeorm:generate": "yarn typeorm migration:generate -n", "typeorm:create": "yarn typeorm migration:create", "typeorm:migrate": "yarn typeorm migration:run", "prepare": "husky install", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@types/pino": "^7.0.5", "@types/socket.io": "^3.0.2", "async-file": "^2.0.2", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "big.js": "^6.2.1", "compression": "^1.7.4", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "date-fns": "^2.28.0", "date-fns-tz": "^1.3.4", "debug": "~2.6.9", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^6.14.0", "form-data": "^4.0.0", "formidable": "^3.5.1", "googleapis": "^144.0.0", "handlebars": "^4.7.7", "helmet": "^5.0.2", "http-errors": "~1.6.3", "http-status": "^1.5.0", "iconv-lite": "^0.6.3", "ioredis": "^4.28.5", "is-image": "^4.0.0", "jshint": "^2.13.4", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.0", "lodash": "^4.17.21", "moment": "^2.30.1", "morgan": "~1.9.1", "multer": "1.4.5-lts.1", "mysql2": "^3.12.0", "node-cron": "^3.0.2", "node-fetch": "^2.6.1", "node-gyp": "^9.0.0", "nodemailer": "^6.9.14", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "path": "^0.12.7", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "redis": "^4.2.0", "reflect-metadata": "^0.1.13", "request": "^2.88.2", "socket.io": "^4.7.2", "typeorm": "^0.3.17", "uuid": "^8.3.2", "winston": "^3.6.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/debug": "^4.1.7", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/ioredis": "^4.28.8", "@types/jsonwebtoken": "^8.5.8", "@types/lodash": "^4.14.178", "@types/multer": "^1.4.7", "@types/node": "^16.18.122", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "eslint": "^8.7.0", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "lint-staged": "^12.3.4", "patch-package": "^6.4.7", "prettier": "^2.6.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^3.12.0", "typescript": "^4.9.5"}, "keywords": [], "author": "dungha", "license": "ISC"}