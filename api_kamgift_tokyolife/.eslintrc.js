// ⛔️ Parsing error: "parserOptions.project" has been set for @typescript-eslint/parser.
// The file does not match your project config: .eslintrc.js.
// The file must be included in at least one of the projects provided.eslint
module.exports = {
  env: {
    es6: true,
    node: true
  },
  settings: {
    'import/resolver': {
      typescript: {}
    }
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    sourceType: 'module',
    ecmaVersion: 2021,
    tsconfigRootDir: __dirname,
    project: './tsconfig.json'
  },
  plugins: ['@typescript-eslint', 'import'],
  extends: ['airbnb-typescript/base', 'eslint:recommended', 'plugin:@typescript-eslint/recommended', 'prettier'],
  rules: {
    "no-console": 1,
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never",
        "mjs": "never"
      }
    ]
  },
  ignorePatterns: ['.eslintrc.js', 'prettier.config.js']
}
