{"name": "Order Plan Processing Workflow - Complete", "version": 1, "nodes": [{"id": "1", "name": "<PERSON><PERSON> - New Order", "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1, "parameters": {"triggerOn": "newEmail", "filters": {"label": "Order-Plan"}}, "position": [250, 300], "webhookId": "gmail-trigger"}, {"id": "2", "name": "Extract Order Information", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Trích xuất thông tin từ email order\nconst email = $input.first().json;\nconst subject = email.subject || '';\nconst body = email.textPlain || email.textHtml || '';\n\n// Regex patterns để trích xuất thông tin\nconst patterns = {\n  client: /(?:client|khách hàng|brand)[:：]\\s*([^\\n\\r]+)/i,\n  product: /(?:sản phẩm|product|link web)[:：]\\s*([^\\n\\r]+)/i,\n  budget: /(?:ngân sách|budget|chi phí)[:：]\\s*([^\\n\\r]+)/i,\n  duration: /(?:thời gian|duration|thời hạn)[:：]\\s*([^\\n\\r]+)/i,\n  kpi: /(?:kpi|mục tiêu|target)[:：]\\s*([^\\n\\r]+)/i,\n  deadline: /(?:deadline|hạn chót)[:：]\\s*([^\\n\\r]+)/i,\n  industry: /(?:ngành|industry|lĩnh vực)[:：]\\s*([^\\n\\r]+)/i,\n  targetAudience: /(?:target audience|đối tượng)[:：]\\s*([^\\n\\r]+)/i\n};\n\n// Trích xuất thông tin\nconst extractedData = {};\nfor (const [key, pattern] of Object.entries(patterns)) {\n  const match = body.match(pattern);\n  extractedData[key] = match ? match[1].trim() : '';\n}\n\n// Tạo ID duy nhất cho order\nconst orderId = 'ORD-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);\n\n// Chuẩn hóa dữ liệu\nconst orderData = {\n  orderId: orderId,\n  emailId: email.id,\n  threadId: email.threadId,\n  sender: email.from,\n  subject: subject,\n  receivedAt: new Date().toISOString(),\n  status: 'New Order',\n  client: extractedData.client,\n  product: extractedData.product,\n  budget: extractedData.budget,\n  duration: extractedData.duration,\n  kpi: extractedData.kpi,\n  deadline: extractedData.deadline,\n  industry: extractedData.industry,\n  targetAudience: extractedData.targetAudience,\n  rawEmail: body,\n  assignedTo: '',\n  assignedAt: '',\n  informationStatus: 'Pending Review',\n  planDraft: '',\n  planFinal: '',\n  clientFeedback: '',\n  revisionCount: 0,\n  finalApproved: false,\n  storagePath: ''\n};\n\nreturn [{ json: orderData }];"}, "position": [500, 300]}, {"id": "3", "name": "Save to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "append", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "columns": {"mappingMode": "defineBelow", "value": {"A": "=orderId", "B": "=emailId", "C": "=threadId", "D": "=sender", "E": "=subject", "F": "=receivedAt", "G": "=status", "H": "=client", "I": "=product", "J": "=budget", "K": "=duration", "L": "=kpi", "M": "=deadline", "N": "=industry", "O": "=targetAudience", "P": "=assignedTo", "Q": "=assignedAt", "R": "=informationStatus", "S": "=planDraft", "T": "=planFinal", "U": "=clientFeedback", "V": "=revisionCount", "W": "=finalApproved", "X": "=storagePath"}}}, "position": [750, 300]}, {"id": "4", "name": "Notify Leader", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}}", "subject": "[URGENT] New Order Plan - {{$json.client}}", "message": "<PERSON><PERSON><PERSON> Leader,\n\n<PERSON><PERSON>t order plan mới cần đ<PERSON><PERSON><PERSON> xử lý:\n\n- Order ID: {{$json.orderId}}\n- <PERSON><PERSON><PERSON><PERSON> hàng: {{$json.client}}\n- <PERSON><PERSON><PERSON> phẩm: {{$json.product}}\n- <PERSON><PERSON> sách: {{$json.budget}}\n- Deadline: {{$json.deadline}}\n- Th<PERSON><PERSON> gian nhận: {{$json.receivedAt}}\n\n<PERSON><PERSON> lòng truy cập Google Sheets để assign người phụ trách trong vòng 30 phút.\n\nTr<PERSON> trọng,\n<PERSON><PERSON> thống tự động"}, "position": [1000, 300]}, {"id": "5", "name": "Google Sheets Trigger - Status Update", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "triggerOn": "update"}, "position": [250, 600], "webhookId": "sheets-trigger"}, {"id": "6", "name": "Check Status and Route", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// <PERSON><PERSON><PERSON> tra trạng thái và điều hướng xử lý\nconst data = $input.first().json;\nconst status = data.status;\nconst informationStatus = data.informationStatus;\n\n// <PERSON><PERSON><PERSON> nghĩa các luồng xử lý\nconst routes = {\n  'Assigned': 'step3', // Bước 3: Account ti<PERSON><PERSON> nh<PERSON>n & đ<PERSON><PERSON> gi<PERSON>\n  'Information Sufficient': 'step4', // Bước 4: <PERSON><PERSON><PERSON> tra tồn kho\n  'Draft Created': 'step5', // Bước 5: <PERSON><PERSON><PERSON> hợ<PERSON> với BP sản phẩm\n  'Internal Review Complete': 'step6', // Bước 6: Review & hoàn thiện\n  'Ready for Client': 'step7', // Bước 7: <PERSON><PERSON><PERSON> cho kh<PERSON>ch hàng\n  'Client Feedback': 'step8', // Bước 8: <PERSON><PERSON> lý phản hồi\n  'Approved': 'step9' // Bước 9: <PERSON><PERSON><PERSON> tr<PERSON> & b<PERSON><PERSON> giao\n};\n\n// <PERSON><PERSON><PERSON> định luồng xử lý\nlet nextStep = 'unknown';\nif (routes[status]) {\n  nextStep = routes[status];\n} else if (informationStatus === 'Insufficient') {\n  nextStep = 'request_info';\n}\n\nreturn [{ json: { ...data, nextStep } }];"}, "position": [500, 600]}, {"id": "7", "name": "Switch - Route Processing", "type": "n8n-nodes-base.switch", "typeVersion": 1, "parameters": {"rules": {"rules": [{"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "step3"}]}}, {"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "request_info"}]}}, {"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "step4"}]}}, {"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "step5"}]}}, {"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "step6"}]}}, {"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "step7"}]}}, {"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "step8"}]}}, {"conditions": {"string": [{"value1": "={{$json.nextStep}}", "operation": "equal", "value2": "step9"}]}}]}}, "position": [750, 600]}, {"id": "8", "name": "Step 3 - Check Information", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Bước 3: <PERSON><PERSON><PERSON> tra thông tin đầu vào\nconst data = $input.first().json;\n\n// Checklist thông tin bắt buộc\nconst requiredFields = [\n  'client', 'product', 'budget', 'duration', \n  'kpi', 'deadline', 'industry', 'targetAudience'\n];\n\n// Kiểm tra thông tin thiếu\nconst missingFields = [];\nfor (const field of requiredFields) {\n  if (!data[field] || data[field].trim() === '') {\n    missingFields.push(field);\n  }\n}\n\n// Cập nhật trạng thái\nlet newStatus, newInformationStatus;\nif (missingFields.length === 0) {\n  newStatus = 'Information Sufficient';\n  newInformationStatus = 'Complete';\n} else {\n  newStatus = 'Pending Information';\n  newInformationStatus = 'Insufficient';\n}\n\nreturn [{ json: {\n  ...data,\n  status: newStatus,\n  informationStatus: newInformationStatus,\n  missingFields: missingFields\n}}];"}, "position": [1000, 500]}, {"id": "9", "name": "Request Missing Information", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.sender}}", "subject": "Re: {{$json.subject}}", "message": "<PERSON><PERSON><PERSON> b<PERSON>,\n\n<PERSON><PERSON><PERSON> là {{$json.assignedTo}}, ngư<PERSON>i phụ trách plan cho khách hàng {{$json.client}}.\n\n<PERSON><PERSON> tiến hành xây dựng kế hoạch chi tiết và hiệu quả nhất, tôi cần bạn hỗ trợ bổ sung các thông tin sau:\n\n{{$json.missingFields.map(field => `- ${field}: [Thông tin cần bổ sung]`).join('\\n')}}\n\nVui lòng phản hồi trong luồng email này. Cảm ơn bạn!\n\nTrân trọng,\n{{$json.assignedTo}}"}, "position": [1250, 500]}, {"id": "10", "name": "Step 4 - Check Inventory & Budget", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Bước 4: <PERSON><PERSON><PERSON> tra tồn kho và phân bổ ngân sách\nconst data = $input.first().json;\n\n// Logic kiểm tra tồn kho (g<PERSON><PERSON> lập)\nconst inventoryCheck = {\n  available: true,\n  formats: ['Banner', 'Video', 'Native'],\n  capacity: 1000000,\n  estimatedReach: 500000\n};\n\n// Logic phân bổ ngân sách\nconst budget = parseFloat(data.budget.replace(/[^0-9.]/g, '')) || 0;\nconst budgetAllocation = {\n  banner: Math.floor(budget * 0.4),\n  video: Math.floor(budget * 0.4),\n  native: Math.floor(budget * 0.2)\n};\n\n// Ước tính KPI\nconst estimatedKPI = {\n  impressions: Math.floor(budget * 1000),\n  clicks: Math.floor(budget * 50),\n  conversions: Math.floor(budget * 5),\n  ctr: 5.0,\n  cpc: budget / 50\n};\n\n// Tạo bản nháp plan\nconst planDraft = {\n  orderId: data.orderId,\n  client: data.client,\n  budget: budget,\n  duration: data.duration,\n  formats: inventoryCheck.formats,\n  budgetAllocation: budgetAllocation,\n  estimatedKPI: estimatedKPI,\n  inventoryStatus: inventoryCheck.available ? 'Available' : 'Limited',\n  createdAt: new Date().toISOString(),\n  version: '1.0'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Draft Created',\n  planDraft: JSON.stringify(planDraft)\n}}];"}, "position": [1000, 600]}, {"id": "11", "name": "Step 5 - Notify Product Team", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.PRODUCT_TEAM_EMAIL}}", "subject": "[Review Required] Plan Draft - {{$json.client}}", "message": "Chào team s<PERSON><PERSON> ph<PERSON>,\n\n<PERSON><PERSON> bản nh<PERSON>p plan cần review:\n\n- Order ID: {{$json.orderId}}\n- <PERSON><PERSON><PERSON><PERSON> hàng: {{$json.client}}\n- <PERSON><PERSON> sách: {{$json.budget}}\n- <PERSON><PERSON><PERSON><PERSON> gian: {{$json.duration}}\n\nB<PERSON><PERSON> nháp chi tiết:\n{{$json.planDraft}}\n\nVui lòng review và phản hồi trong vòng 24h.\n\n<PERSON><PERSON><PERSON> trọ<PERSON>,\n<PERSON><PERSON> thống tự động"}, "position": [1000, 700]}, {"id": "12", "name": "Step 6 - Finalize Plan", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Bước 6: <PERSON><PERSON><PERSON> thiện plan sau khi có feedback\nconst data = $input.first().json;\n\n// Parse plan draft\nconst planDraft = JSON.parse(data.planDraft || '{}');\n\n// Thêm thông tin hoàn thiện\nconst finalPlan = {\n  ...planDraft,\n  productTeamApproved: true,\n  inventoryTeamApproved: true,\n  finalReviewDate: new Date().toISOString(),\n  version: '1.1',\n  status: 'Ready for Client'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Ready for Client',\n  planFinal: JSON.stringify(finalPlan)\n}}];"}, "position": [1000, 800]}, {"id": "13", "name": "Step 7 - Send to Client", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.sender}}", "subject": "<PERSON><PERSON> hoạch quảng cáo - {{$json.client}}", "message": "<PERSON><PERSON><PERSON>,\n\n<PERSON><PERSON><PERSON> xin gửi kế hoạch quảng cáo chi tiết cho {{$json.client}}:\n\n{{$json.planFinal}}\n\nVui lòng xem xét và phản hồi trong vòng 48h.\n\n<PERSON><PERSON><PERSON> trọng,\n{{$json.assignedTo}}"}, "position": [1000, 900]}, {"id": "14", "name": "Step 8 - Process Client Feedback", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Bước 8: <PERSON><PERSON> lý phản hồi từ khách hàng\nconst data = $input.first().json;\nconst feedback = data.clientFeedback || '';\n\n// Phân loại phản hồi (g<PERSON><PERSON> lập logic phân tích)\nlet feedbackType = 'unknown';\nif (feedback.toLowerCase().includes('đồng ý') || feedback.toLowerCase().includes('ok') || feedback.toLowerCase().includes('chốt')) {\n  feedbackType = 'approved';\n} else if (feedback.toLowerCase().includes('sửa') || feedback.toLowerCase().includes('thay đổi') || feedback.toLowerCase().includes('revise')) {\n  feedbackType = 'revision';\n} else if (feedback.toLowerCase().includes('từ chối') || feedback.toLowerCase().includes('không')) {\n  feedbackType = 'rejected';\n}\n\n// Cập nhật trạng thái dựa trên phả<PERSON> hồi\nlet newStatus, revisionCount;\nif (feedbackType === 'approved') {\n  newStatus = 'Approved';\n  revisionCount = data.revisionCount || 0;\n} else if (feedbackType === 'revision') {\n  newStatus = 'Information Sufficient'; // Quay lại bước 4\n  revisionCount = (data.revisionCount || 0) + 1;\n} else if (feedbackType === 'rejected') {\n  newStatus = 'Closed - Not Won';\n  revisionCount = data.revisionCount || 0;\n}\n\nreturn [{ json: {\n  ...data,\n  status: newStatus,\n  revisionCount: revisionCount,\n  feedbackType: feedbackType\n}}];"}, "position": [1000, 1000]}, {"id": "15", "name": "Step 9 - Final Storage", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Bước 9: <PERSON><PERSON><PERSON> trữ và bàn giao kết quả\nconst data = $input.first().json;\n\n// Tạo đường dẫn lưu trữ\nconst storagePath = `Plans/${data.client}/${data.orderId}_${new Date().toISOString().split('T')[0]}.json`;\n\n// Tạo báo cáo cuối cùng\nconst finalReport = {\n  orderId: data.orderId,\n  client: data.client,\n  product: data.product,\n  budget: data.budget,\n  duration: data.duration,\n  finalPlan: JSON.parse(data.planFinal || '{}'),\n  revisionCount: data.revisionCount || 0,\n  completedAt: new Date().toISOString(),\n  assignedTo: data.assignedTo,\n  status: 'Completed'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Completed',\n  finalApproved: true,\n  storagePath: storagePath,\n  finalReport: JSON.stringify(finalReport)\n}}];"}, "position": [1000, 1100]}, {"id": "16", "name": "Update Google Sheets Status", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "columnToMatchOn": "A", "valueToMatchOn": "={{$json.orderId}}", "columns": {"mappingMode": "defineBelow", "value": {"G": "={{$json.status}}", "Q": "={{$json.assignedAt}}", "R": "={{$json.informationStatus}}", "S": "={{$json.planDraft}}", "T": "={{$json.planFinal}}", "U": "={{$json.clientFeedback}}", "V": "={{$json.revisionCount}}", "W": "={{$json.finalApproved}}", "X": "={{$json.storagePath}}"}}}, "position": [1250, 1100]}, {"id": "17", "name": "Notify Completion", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}},{{$json.sender}}", "subject": "[Completed] Order Plan - {{$json.client}}", "message": "Chào team,\n\nOrder plan đã được hoàn thành:\n\n- Order ID: {{$json.orderId}}\n- <PERSON><PERSON><PERSON><PERSON> hàng: {{$json.client}}\n- Tr<PERSON><PERSON> thái: {{$json.status}}\n- <PERSON><PERSON><PERSON><PERSON> phụ trách: {{$json.assignedTo}}\n- <PERSON><PERSON><PERSON><PERSON> dẫn lưu trữ: {{$json.storagePath}}\n\nT<PERSON><PERSON> trọng,\n<PERSON><PERSON> thống tự động"}, "position": [1500, 1100]}], "connections": {"Gmail Trigger - New Order": {"main": [[{"node": "Extract Order Information", "type": "main", "index": 0}]]}, "Extract Order Information": {"main": [[{"node": "Save to Google Sheets", "type": "main", "index": 0}]]}, "Save to Google Sheets": {"main": [[{"node": "Notify Leader", "type": "main", "index": 0}]]}, "Google Sheets Trigger - Status Update": {"main": [[{"node": "Check Status and Route", "type": "main", "index": 0}]]}, "Check Status and Route": {"main": [[{"node": "Switch - Route Processing", "type": "main", "index": 0}]]}, "Switch - Route Processing": {"main": [[{"node": "Step 3 - Check Information", "type": "main", "index": 0}], [{"node": "Request Missing Information", "type": "main", "index": 0}], [{"node": "Step 4 - Check Inventory & Budget", "type": "main", "index": 0}], [{"node": "Step 5 - Notify Product Team", "type": "main", "index": 0}], [{"node": "Step 6 - Finalize Plan", "type": "main", "index": 0}], [{"node": "Step 7 - Send to Client", "type": "main", "index": 0}], [{"node": "Step 8 - Process Client Feedback", "type": "main", "index": 0}], [{"node": "Step 9 - Final Storage", "type": "main", "index": 0}]]}, "Step 3 - Check Information": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}]]}, "Request Missing Information": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}]]}, "Step 4 - Check Inventory & Budget": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}]]}, "Step 5 - Notify Product Team": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}]]}, "Step 6 - Finalize Plan": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}]]}, "Step 7 - Send to Client": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}], [{"node": "Step 8 - Process Client Feedback", "type": "main", "index": 0}]]}, "Step 8 - Process Client Feedback": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}], [{"node": "Step 9 - Final Storage", "type": "main", "index": 0}]]}, "Step 9 - Final Storage": {"main": [[{"node": "Update Google Sheets Status", "type": "main", "index": 0}]]}, "Update Google Sheets Status": {"main": [[{"node": "Notify Completion", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 2, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}