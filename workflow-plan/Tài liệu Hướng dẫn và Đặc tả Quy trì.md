# **Tài liệu Hướng dẫn và Đặc tả Quy trình Xử lý Order Plan Quảng cáo (Phiên bản cập nhật)**

## **I. Giới thiệu**

Tài liệu này mô tả chi tiết quy trình 9 bước để tiế<PERSON> nh<PERSON>, <PERSON><PERSON> lý, và hoàn thiện một kế hoạch (plan) quảng cáo cho khách hàng, dựa trên sơ đồ quy trình chuẩn. M<PERSON>c tiêu là chuẩn hóa luồng công việc, làm rõ vai trò của từng bộ phận và cung cấp một bản đặc tả chi tiết để xây dựng các kịch bản tự động hóa.

## **II. Vai Trò Các Bên Liên <PERSON>**

* **SALE / ACCOUNT PLAN:** <PERSON><PERSON><PERSON> mối làm việc vớ<PERSON>h<PERSON> h<PERSON>, gử<PERSON> yê<PERSON> cầ<PERSON> (order) làm plan, cung cấp thông tin và chốt plan.
* **ACCOUNT SERVING:** Chịu trách nhiệm chính xử lý plan, đánh giá thông tin, điều phối các bộ phận liên quan.
* **BỘ PHẬN SẢN PHẨM (BP SẢN PHẨM):** Tư vấn chuyên môn về sản phẩm, nội dung, KPI và tính khả thi.
* **INVENTORY TEAM:** Quản lý và kiểm soát nguồn tài nguyên quảng cáo (tồn kho), cung cấp số liệu về định mức.

---

## **III. Chi Tiết Quy Trình 9 Bước**

### **Bước 1: NHẬN ORDER PLAN (TỪ SALE/AGENCY)**

* **Mục tiêu:** Tự động hóa việc tiếp nhận và chuẩn hóa dữ liệu đầu vào.
* **Hệ thống kích hoạt (Trigger):** Một email mới được gửi đến địa chỉ `<EMAIL>` với tiêu đề/nhãn "Order Plan".

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Gmail → New email received**
2.  **Node: OpenAI → Extract structured information** (Trích xuất các thông tin như: client, sản phẩm, ngân sách, thời gian, KPI, deadline...).
3.  **Node: Set → Format Data** (Chuẩn hóa dữ liệu ngày tháng, số liệu).
4.  **Node: Notion/Airtable → Create a new record**
    * Ghi các thông tin đã trích xuất vào bảng `Plan Requests`.
    * **Quan trọng: Lưu lại `threadId` (ID luồng email) của email gốc để sử dụng ở Bước 3.**
    * Gán trạng thái ban đầu là `New Order`.
5.  **Node: Email → Notify the Leader** (Thông báo cho Leader có plan mới).

---

### **Bước 2: ASSIGN LEADER PHỤ TRÁCH**

* **Mục tiêu:** Đảm bảo mọi yêu cầu mới đều được phân công người xử lý trong thời gian quy định (ví dụ: 30 phút).
* **Người thực hiện:** Leader team Account Serving.

#### **Mô tả quy trình:**
1.  Leader nhận email thông báo và truy cập hệ thống để gán người phụ trách (Assignee).
2.  Nếu quá 30 phút yêu cầu vẫn ở trạng thái `New Order`, hệ thống tự động gửi email nhắc nhở.
3.  Người được phân công nhận thông báo và xác nhận tiếp nhận.

#### **Gợi ý Tự động hóa:**
1.  **Trigger:** Bản ghi mới được tạo trong `Plan Requests`.
2.  **Node: IF/Set →** Tự động đề xuất người phụ trách dựa trên ngành hàng/khách hàng.
3.  **Node: Chat/Email →** Gửi thông báo cho người được gán.
4.  **Node: Google Sheets/Airtable →** Cập nhật trạng thái thành `Assigned`.

---

### **Bước 3: ACCOUNT TIẾP NHẬN & ĐÁNH GIÁ ĐẦU VÀO (ĐÃ CẬP NHẬT)**

* **Mục tiêu:** Đảm bảo Account phụ trách có đầy đủ thông tin. **Nếu thiếu, sẽ yêu cầu bổ sung ngay trong luồng email gốc.**
* **Người thực hiện:** Account phụ trách.

#### **Mô tả quy trình:**
Account sau khi nhận việc sẽ kiểm tra toàn bộ thông tin trong order.
* **Nếu thông tin đầy đủ:** Chuyển trạng thái sang `Information Sufficient` để bắt đầu xử lý ở Bước 4.
* **Nếu thông tin chưa đủ:** **Account sẽ gửi email yêu cầu bổ sung cho Sale/Agency. Email này phải là một email trả lời (reply) trong luồng email gốc ở Bước 1 để giữ tính liên tục.**

#### **Gợi ý Tự động hóa:**

1.  **Trigger:** Khi trạng thái bản ghi đổi thành `Assigned`.
2.  **Node: OpenAI → Compare data with a checklist** (So sánh thông tin hiện có với checklist các trường bắt buộc).
3.  **Node: IF/Router → Kiểm tra kết quả từ OpenAI.**
    * **Luồng 1: Nếu thông tin ĐẦY ĐỦ:**
        * **Node: Google Sheets/Airtable → Update Status**
            * Cập nhật trạng thái của plan thành `Information Sufficient`.
    * **Luồng 2: Nếu thông tin THIẾU:**
        * **Node: Gmail API → Reply to an email**
            * **Hành động:** Sử dụng `threadId` đã lưu ở Bước 1 để tạo một email trả lời.
            * **Người nhận:** Tự động điền người gửi email gốc (Sale/Agency).
            * **Tiêu đề:** Giữ nguyên tiêu đề cũ với `Re:`.
            * **Nội dung (Template):**
                > "Chào bạn,
                >
                > Tôi là {{account_name}}, người phụ trách plan cho khách hàng {{client_name}}.
                >
                > Để tiến hành xây dựng kế hoạch chi tiết và hiệu quả nhất, tôi cần bạn hỗ trợ bổ sung các thông tin sau:
                > - **[Danh sách các trường bị thiếu do OpenAI xác định]**
                > - **[Có thể thêm các câu hỏi làm rõ khác nếu cần]**
                >
                > Vui lòng phản hồi trong luồng email này. Cảm ơn bạn!"
        * **Node: Google Sheets/Airtable → Update Status**
            * Cập nhật trạng thái của plan thành `Pending Information`.

---

### **Bước 4 đến 6: Xây dựng và Hoàn thiện Plan nội bộ**

(Các bước này về cơ bản giữ nguyên nhưng được làm rõ hơn theo sơ đồ)

#### **Bước 4: KIỂM TRA TỒN KHO & PHÂN BỔ NGÂN SÁCH**
* **Trigger:** Khi trạng thái là `Information Sufficient`.
* **Hành động:** Account xác định format chạy, check tồn kho với team Inventory, phân bổ ngân sách sơ bộ, và ước tính KPI.
* **Kết quả:** Bản kế hoạch nháp (Draft v1).

#### **Bước 5: PHỐI HỢP VỚI BP SẢN PHẨM & INVENTORY**
* **Hành động:** Account gửi bản nháp cho BP Sản phẩm và Inventory để review.
* **BP Sản phẩm review:** Hạng mục sản phẩm, KPI, chiết khấu, bonus.
* **Inventory review:** Các loại định dạng, tồn kho, các booking đặc biệt.
* **Kết quả:** Các góp ý được ghi nhận.

#### **Bước 6: REVIEW & HOÀN THIỆN PLAN**
* **Hành động:** Account tổng hợp các góp ý và hoàn thiện lại plan.
* **Kết quả:** Bản kế hoạch "Final Internal" sẵn sàng để gửi cho khách hàng.

---

### **Bước 7: GỬI PLAN CHO KHÁCH & NHẬN PHẢN HỒI**

* **Mục tiêu:** Gửi plan đã hoàn thiện cho khách hàng và xử lý phản hồi một cách có hệ thống.
* **Trigger:** Khi trạng thái plan đổi thành `Final Internal`.
* **Hành động:** Account/Sale gửi email kèm kế hoạch cho khách hàng.

---

### **Bước 8: QUY TRÌNH REVISION & CHỐT PLAN (ĐÃ CẬP NHẬT CHI TIẾT)**

* **Mục tiêu:** Xử lý các phản hồi từ khách hàng một cách có cấu trúc, bao gồm cả việc chỉnh sửa (revise) và ghi nhận lý do nếu không thành công.
* **Trigger:** Có phản hồi từ khách hàng (qua email, cuộc gọi...).

#### **Mô tả quy trình & Tự động hóa:**

1.  **Node: OpenAI → Tóm tắt & Phân loại Phản hồi của Khách hàng**
    * **Input:** Email/ghi chú cuộc gọi từ khách hàng.
    * **Prompt:** "Phân tích phản hồi sau của khách hàng. Phân loại thành một trong ba nhóm: 'Đồng ý', 'Yêu cầu chỉnh sửa', hoặc 'Từ chối'. Nếu là 'Yêu cầu chỉnh sửa', hãy liệt kê các điểm cụ thể cần thay đổi."

2.  **Node: IF/Router → Điều hướng xử lý dựa trên phân loại**

    * **Luồng 1: Khách hàng ĐỒNG Ý (CHỐT DEAL)**
        * **Hành động:**
            * Account cập nhật trạng thái plan thành `Approved`.
            * Chuyển sang **Bước 9** để bàn giao và lưu trữ.

    * **Luồng 2: Khách hàng YÊU CẦU CHỈNH SỬA (REVISE)**
        * **Hành động:**
            1.  **Ghi nhận yêu cầu:** Account cập nhật các yêu cầu cụ thể của khách vào hệ thống (`Client Feedback` field). Ví dụ:
                * *Thay đổi định mức, format.*
                * *Thay đổi ngân sách, thời gian chạy.*
                * *Thay đổi target audience, KPI.*
                * *Yêu cầu thêm chiết khấu...*
            2.  **Cập nhật trạng thái:** Đổi status thành `Revision vX` (v.d: `Revision v1.1`).
            3.  **QUAY VÒNG LẶP (LOOP):** Quy trình sẽ quay trở lại các bước xử lý nội bộ:
                * Quay lại **Bước 4 (Check Inventory)** nếu thay đổi liên quan đến format, ngân sách.
                * Quay lại **Bước 5 (Phối hợp)** nếu các thay đổi cần sự tư vấn của BP Sản phẩm.
                * Quay lại **Bước 6 (Hoàn thiện)** để tạo ra phiên bản plan mới.
                * Sau đó, quy trình lại tiếp tục đến **Bước 7** để gửi lại bản kế hoạch đã chỉnh sửa cho khách hàng. Vòng lặp này tiếp tục cho đến khi khách hàng Đồng ý hoặc Từ chối.

    * **Luồng 3: Khách hàng TỪ CHỐI / KHÔNG TRIỂN KHAI**
        * **Hành động:**
            1.  **Ghi nhận phản hồi:** Account/Sale có trách nhiệm tìm hiểu và ghi lại lý do khách hàng không chốt vào hệ thống. Ví dụ:
                * *Ngân sách không phù hợp.*
                * *Không đồng ý với định dạng đề xuất.*
                * *Chọn đối tác khác.*
                * *Thay đổi chiến lược nội bộ.*
            2.  **Cập nhật trạng thái:** Đổi status thành `Closed - Not Won`.
            3.  **Lưu trữ:** Lưu lại plan và các ghi chú để rút kinh nghiệm. Quy trình kết thúc.

---

### **Bước 9: TRẢ KẾT QUẢ & LƯU TRỮ**

* **Mục tiêu:** Bàn giao plan cuối cùng và lưu trữ tài liệu một cách khoa học.
* **Trigger:** Khi trạng thái plan là `Approved`.
* **Hành động:**
    * Xuất file plan ra định dạng PDF.
    * Tự động upload lên thư mục Google Drive của khách hàng theo cấu trúc chuẩn.
    * Gửi email thông báo cho tất cả các bên liên quan (Sale, Account, Leader...) kèm theo đường dẫn tới file kế hoạch cuối cùng.

Hy vọng phiên bản cập nhật chi tiết này đáp ứng đúng yêu cầu và giúp bạn chuẩn hóa quy trình hiệu quả hơn!