# **Tài liệu Hướng dẫn và Đặc tả Quy trình Xử lý Order Plan Quảng cáo - Phiên bản 2.0 Tối ưu**

## **I. Giới thiệu**

Tài liệu này mô tả chi tiết quy trình 9 bước được tối ưu hóa để tiếp nhận, x<PERSON> lý, và hoàn thiện một kế hoạch (plan) quảng cáo cho khách hàng. Phiên bản 2.0 này bao gồm các cải tiến quan trọng về AI integration, error handling, monitoring, và xử lý các trường hợp đặc biệt.

**🆕 Các cải tiến chính trong phiên bản 2.0:**
- **AI-Enhanced Processing:** Sử dụng OpenAI GPT-4 để trích xuất thông tin và phân tích phản hồi
- **Smart Retry Mechanism:** Xử lý thông minh cho trường hợp thiếu thông tin với escalation tự động
- **Advanced Feedback Analysis:** Phân tích chi tiết phản hồi khách hàng với sentiment analysis
- **Proactive Monitoring:** Hệ thống cảnh báo overdue và timeout tự động
- **Enhanced Error Handling:** Xử lý lỗi toàn diện với logging chi tiết

Tài liệu này được thiết kế để:

1.  **Hướng dẫn nhân sự mới:** Giúp nhanh chóng nắm bắt quy trình làm việc tối ưu.
2.  **Làm cơ sở cho tự động hóa:** Cung cấp thông tin đầu vào có cấu trúc cho các công cụ AI và nền tảng automation.
3.  **Đảm bảo chất lượng:** Giảm thiểu lỗi và tăng tỷ lệ thành công của các order plan.

## **II. Vai Trò Các Bên Liên Quan**

  * **SALE / ACCOUNT PLAN:**

      * **Vai trò:** Là đầu mối làm việc trực tiếp với khách hàng hoặc agency.
      * **Nhiệm vụ:** Gửi yêu cầu (order) làm plan, cung cấp đầy đủ thông tin ban đầu, đàm phán và chốt plan với khách hàng.

  * **ACCOUNT SERVING:**

      * **Vai trò:** Là người chịu trách nhiệm chính trong việc xử lý plan.
      * **Nhiệm vụ:** Tiếp nhận order, đánh giá thông tin, điều phối với các bộ phận liên quan (Sản phẩm, Inventory), xây dựng plan chi tiết và thực hiện các lần chỉnh sửa (revision).

  * **BỘ PHẬN SẢN PHẨM (BP SẢN PHẨM):**

      * **Vai trò:** Tư vấn chuyên môn về sản phẩm, nội dung và tính khả thi của KPI.
      * **Nhiệm vụ:** Đưa ra góp ý về các hạng mục trong plan, đề xuất nội dung phù hợp, tư vấn về mức chiết khấu và các gói bonus hợp lý.

  * **INVENTORY TEAM:**

      * **Vai trò:** Quản lý và kiểm soát nguồn tài nguyên quảng cáo (tồn kho).
      * **Nhiệm vụ:** Kiểm tra và xác nhận lượng tồn kho (inventory) theo các định dạng (format) quảng cáo, cung cấp định mức và số liệu cần thiết để plan có tính khả thi cao.

-----

## **III. Chi Tiết Quy Trình 9 Bước**

### **Bước 1: NHẬN ORDER PLAN (TỪ SALE/AGENCY)**

  * **Mục tiêu:** Tự động hóa việc tiếp nhận và chuẩn hóa dữ liệu đầu vào ngay khi có yêu cầu mới.
  * **Người thực hiện:** Sale / Agency.
  * **Hệ thống kích hoạt (Trigger):** Một email mới được gửi đến một địa chỉ email được chỉ định (ví dụ: `<EMAIL>`) với tiêu đề hoặc nhãn (label) cụ thể.

#### **Thông tin đầu vào (Input):**

Email từ Sale/Agency phải chứa các thông tin sau:

  * **Thông tin client:** Tên công ty, brand, người liên hệ.
  * **Sản phẩm/Dịch vụ:** Link website, app, hoặc mô tả chi tiết sản phẩm cần quảng cáo.
  * **Mục tiêu truyền thông:** Awareness, Traffic, Lead Generation, Sales, App Install...
  * **Thông tin chiến dịch:**
      * Ngành hàng (VD: FMCG, Bất động sản, Game, E-commerce).
      * Ngân sách tổng (VD: 500,000,000 VND).
      * Thời gian chạy (VD: 01/10/2025 - 31/10/2025).
  * **Target Audience (Đối tượng mục tiêu):** Mô tả chi tiết (tuổi, giới tính, sở thích, khu vực địa lý, hành vi...).
  * **KPI mong muốn:** Cung cấp các chỉ số cụ thể (VD: 10,000,000 Impressions, CTR \> 0.5%, 1,000 Leads, CPL \< 150,000 VND).
  * **Deadline cần trả plan:** Ngày giờ cụ thể.

#### **🚀 Gợi ý Tự động hóa (Automation Workflow v2.0):**

1.  **Trigger: Gmail → New email received (Enhanced)**
      * **Filter:** `labelIds: ["Order-Plan"]` với polling mỗi phút
      * **Cải tiến:** Bao gồm spam/trash filtering và real-time monitoring

2.  **🤖 Node: OpenAI GPT-4 → Extract structured information**
      * **Model:** `gpt-4o-mini` với temperature 0.1 để đảm bảo consistency
      * **System Prompt:**
        ```
        Bạn là một AI assistant chuyên trích xuất thông tin từ email order plan.
        Trích xuất thông tin và trả về JSON với các trường: client_name, product_link,
        campaign_goal, industry, budget (chỉ số), start_date, end_date, target_audience,
        desired_kpis, deadline, sender_email. Nếu không có thông tin, để giá trị null.
        ```
      * **Cải tiến:** Sử dụng AI thay vì regex để trích xuất chính xác hơn

3.  **Node: Function → Process Extracted Data (Enhanced)**
      * Tạo Order ID unique với timestamp
      * Thêm các trường mới: `priority`, `escalated`, `informationRequestCount`, `maxRevisions`
      * Error handling với try-catch
      * Validation dữ liệu đầu vào

4.  **Node: Google Sheets v5 → Create record**
      * **Cải tiến:** Sử dụng typeVersion 5 với cellFormat "USER_ENTERED"
      * Thêm các trường tracking: `lastActivity`, `escalated`, `informationRequestCount`
      * Ghi đối tượng JSON đã được xử lý vào bảng `Plan Requests`

5.  **📧 Node: Gmail → Enhanced Leader Notification**
      * **Tiêu đề:** `[🚨 URGENT] New Order Plan - {{client_name}}`
      * **Cải tiến:**
        - Format email với emoji và markdown-style
        - Bao gồm link trực tiếp đến Google Sheets
        - Hiển thị đầy đủ thông tin order với priority level

-----

### **Bước 2: ASSIGN LEADER PHỤ TRÁCH**

  * **Mục tiêu:** Đảm bảo mọi yêu cầu mới đều được phân công người xử lý trong thời gian quy định.
  * **Người thực hiện:** Leader team Account Serving & Account được phân công.

#### **Mô tả quy trình:**

1.  Leader nhận email thông báo.
2.  Trong vòng **30 phút**, Leader truy cập hệ thống (Notion/Airtable/Sheet) và chọn người phụ trách cho yêu cầu đó.
3.  Nếu sau 30 phút mà yêu cầu vẫn ở trạng thái `New Order`, hệ thống tự động gửi email nhắc nhở cho Leader.
4.  Người được phân công (Account) nhận thông báo và xác nhận đã tiếp nhận công việc.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Notion/Airtable → When a new record is created in `Plan Requests`**
2.  **Node: IF/Router → Check product type/brand**
      * **Logic:** Dựa trên một bảng mapping (có thể lưu trong Google Sheet hoặc Airtable) để tự động đề xuất người phụ trách.
          * `IF industry == "FMCG" THEN assign_to = "<EMAIL>"`
          * `IF client_name == "Big Client X" THEN assign_to = "<EMAIL>"`
          * `ELSE assign_to = "<EMAIL>"`
3.  **Node: Set → Assign 담당자**
      * Cập nhật trường `Assigned To` trong bản ghi Notion/Airtable với email đã xác định ở bước trên.
4.  **Node: Chat/Email → Send Notification**
      * **Gửi tới:** Email/Chat của người được gán.
      * **Nội dung:** "Bạn vừa được phân công phụ trách plan cho khách hàng **{{client\_name}}**. Deadline trả plan là **{{response\_deadline}}**. Vui lòng bấm vào link sau để xác nhận đã nhận việc." (Link có thể là một webhook để cập nhật trạng thái).
5.  **Node: Google Sheets → Update Status**
      * Ghi lại log: `Timestamp`, `Plan ID`, `Assigned To`, và cập nhật trạng thái thành `Assigned`.

-----

### **Bước 3: ACCOUNT TIẾP NHẬN & ĐÁNH GIÁ ĐẦU VÀO - ENHANCED**

  * **Mục tiêu:** Đảm bảo Account phụ trách có đầy đủ thông tin cần thiết để bắt đầu làm plan với hệ thống phân loại thông tin thông minh.
  * **Người thực hiện:** Account phụ trách.

#### **🆕 Mô tả quy trình cải tiến:**

Account sau khi nhận việc sẽ được hệ thống hỗ trợ kiểm tra thông tin với 3 mức độ ưu tiên:
- **🔴 Critical:** Thông tin bắt buộc (client, budget, deadline)
- **🟡 Important:** Thông tin quan trọng (product, campaign_goal, industry, target_audience)
- **⚪ Optional:** Thông tin tùy chọn (start_date, end_date, kpi)

#### **🚀 Gợi ý Tự động hóa v2.0:**

1.  **Trigger: Google Sheets → When assigneeConfirmed = TRUE**

2.  **🧠 Node: Function → Enhanced Information Check**
      * **Cải tiến:** Phân loại thông tin theo mức độ ưu tiên
      * **Logic:**
        ```javascript
        // Kiểm tra từng loại thông tin
        const criticalFields = ['client', 'budget', 'deadline'];
        const importantFields = ['product', 'campaignGoal', 'industry', 'targetAudience'];
        const optionalFields = ['startDate', 'endDate', 'kpi'];

        // Xác định priority và escalation level
        if (missingCritical.length > 0) {
          priority = 'High';
          informationStatus = 'Critical Missing';
        }
        ```

3.  **📧 Node: Gmail → Smart Information Request**
      * **Cải tiến:**
        - Email được format theo mức độ ưu tiên với emoji
        - Tracking số lần request (`informationRequestCount`)
        - Auto-escalation sau 2 lần request (CC Leader)
        - Deadline reminder trong email
      * **Tiêu đề:** `[ESCALATED] Re: {{subject}} - Cần bổ sung thông tin (Lần {{requestCount}})`

4.  **🔄 Node: Function → Update Request Counter**
      * Tăng `informationRequestCount`
      * Set `escalated = true` nếu >= 2 lần request
      * Update `lastActivity` timestamp

5.  **📊 Node: Google Sheets → Enhanced Status Update**
      * Cập nhật với các trường mới: `priority`, `escalated`, `informationRequestCount`
      * Tracking chi tiết cho reporting và analytics

-----

### **Bước 4: KIỂM TRA TỒN KHO & PHÂN BỔ NGÂN SÁCH**

  * **Mục tiêu:** Xây dựng một bản kế hoạch sơ bộ (draft) dựa trên dữ liệu tồn kho thực tế và mục tiêu của khách hàng.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Google Sheets/Airtable → When a record's status changes to `Information Sufficient`**
2.  **Node: Airtable API → Get inventory data**
      * **Action:** Gửi một API request đến bảng `Inventory` trong Airtable.
      * **Parameters:** Lọc theo `format_type` và `date_range` (dựa trên `start_date` và `end_date` của plan).
3.  **Node: Function/Code → Budget Allocation Logic**
      * Đây là một node tùy chỉnh để thực thi logic nghiệp vụ:
          * Chia ngân sách theo tỷ lệ phần trăm cho từng format quảng cáo (VD: Display 40%, Video 60%).
          * Dựa trên đơn giá (CPM/CPC/CPV) trung bình của ngành và dữ liệu tồn kho, tính toán các chỉ số ước tính (Impressions, Clicks, Views).
          * `Estimated Impressions = (Budget for Format X) / (CPM for Format X) * 1000`.
4.  **Node: OpenAI → Predict KPIs**
      * **Prompt mẫu:**
        ```
        Với một chiến dịch thuộc ngành hàng **{{industry}}**, ngân sách **{{budget}} VND**, chạy trong khoảng thời gian từ **{{start_date}}** đến **{{end_date}}**, mục tiêu là **{{campaign_goal}}**.

        Dưới đây là phân bổ ngân sách và định dạng dự kiến:
        - Format A: {{budget_A}} VND
        - Format B: {{budget_B}} VND

        Dựa trên dữ liệu thị trường, hãy dự đoán các chỉ số KPI sau ở mức "Tiêu chuẩn" và "Tối ưu":
        - Overall CTR (Tỷ lệ nhấp chuột)
        - Overall CVR (Tỷ lệ chuyển đổi) nếu mục tiêu là Sales/Leads.
        - Video View Rate (VVR) nếu có video.

        Trả về kết quả dưới dạng bảng Markdown.
        ```
5.  **Node: Google Sheets → Update Draft Plan**
      * Tạo một tab mới trong file Google Sheet quản lý chung hoặc một file riêng cho plan này.
      * Điền các dữ liệu đã tính toán: Phân bổ ngân sách, KPI ước tính, chi phí...
6.  **Node: Email/Chat → Notify for Review**
      * **Gửi tới:** Leader team Account Serving.
      * **Nội dung:** "Bản plan sơ bộ cho khách hàng **{{client\_name}}** đã được hoàn thành. Vui lòng truy cập [link Google Sheet] để review."

-----

### **Bước 5: PHỐI HỢP VỚI BP SẢN PHẨM & INVENTORY**

  * **Mục tiêu:** Lấy ý kiến chuyên môn từ các bộ phận liên quan để đảm bảo plan có tính khả thi và hấp dẫn.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi bản plan sơ bộ được Leader duyệt (có thể là một checkbox trong Sheet/Airtable).**
2.  **Node: Google Docs API → Create a document from a template**
      * **Action:** Sao chép một file Google Docs mẫu.
      * **Logic:** Tìm và thay thế các placeholder (VD: `{{CLIENT_NAME}}`, `{{BUDGET_TABLE}}`, `{{KPI_TABLE}}`) bằng dữ liệu từ Google Sheet.
3.  **Node: Email → Send for internal review**
      * **Người nhận:** Địa chỉ email của BP Sản phẩm, team Inventory.
      * **Tiêu đề:** `[Review Plan] Góp ý cho kế hoạch của khách hàng {{client_name}}`.
      * **Nội dung:** "Chào các team, vui lòng xem và cho góp ý trực tiếp trên file Google Docs sau cho plan của khách hàng **{{client\_name}}** trước [deadline nội bộ]. Link: [Link Google Docs vừa tạo]."
4.  **Node: Wait → Delay or Wait for Webhook**
      * Chờ trong 24 giờ hoặc chờ tín hiệu (ví dụ: một webhook được kích hoạt khi có comment trong Docs).
5.  **Node: Google Docs API → Get comments**
      * **Action:** Lấy tất cả các comment trong file Google Docs.
      * **Logic:** Tổng hợp các comment và ghi chú lại vào một trường `Internal Feedback` trong bản ghi Notion/Airtable.

-----

### **Bước 6: REVIEW & GÓP Ý PLAN**

  * **Mục tiêu:** Hoàn thiện plan dựa trên các góp ý từ các bộ phận nội bộ.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi có bản ghi mới trong trường `Internal Feedback`** hoặc sau khoảng thời gian `Wait` ở bước 5.
2.  **Node: OpenAI → Suggest revisions**
      * **Prompt mẫu:**
        ```
        Dưới đây là một bản plan quảng cáo và các góp ý từ team nội bộ. Hãy đề xuất các chỉnh sửa cụ thể cho bản plan.

        Bản plan gốc:
        """
        [Dán nội dung plan sơ bộ vào đây]
        """

        Góp ý nội bộ:
        """
        [Dán các comment đã tổng hợp từ Google Docs]
        """

        Ví dụ: Nếu góp ý là "Chiết khấu cho khách hàng này thấp quá, nên tăng thêm 5%", hãy đề xuất cập nhật lại bảng ngân sách với chiết khấu mới và tính lại số tiền thực trả.
        ```
3.  **Node: Google Sheets → Update the plan sheet**
      * Account phụ trách sẽ dựa vào gợi ý của AI và kinh nghiệm để cập nhật lại file Google Sheet.
4.  **Node: Chat/Email → Notify for Final Confirmation**
      * **Gửi tới:** Account phụ trách và Leader.
      * **Nội dung:** "Plan cho **{{client\_name}}** đã được cập nhật theo góp ý. Vui lòng kiểm tra lần cuối và xác nhận bản final để gửi khách hàng."

-----

### **Bước 7: GỬI PLAN CHO KHÁCH & NHẬN PHẢN HỒI - AI ENHANCED**

  * **Mục tiêu:** Gửi plan đã hoàn thiện cho khách hàng và xử lý phản hồi thông minh với AI analysis.
  * **Người thực hiện:** Account phụ trách / Sale.

#### **🚀 Gợi ý Tự động hóa v2.0:**

1.  **Trigger: Khi trạng thái plan = `Ready for Client`**

2.  **📧 Node: Gmail → Enhanced Client Email**
      * **Cải tiến:** Professional template với tracking
      * **Tiêu đề:** `[Proposal] Kế hoạch quảng cáo - {{client_name}}`
      * **Nội dung:** Bao gồm executive summary và call-to-action rõ ràng

3.  **⏰ Node: Wait → Smart Response Monitoring**
      * Chờ phản hồi với timeout 48-72h
      * Tích hợp email thread tracking

4.  **🤖 Node: OpenAI GPT-4 → Advanced Feedback Analysis**
      * **Model:** `gpt-4o-mini` với temperature 0.2
      * **System Prompt:**
        ```
        Bạn là AI chuyên phân tích phản hồi khách hàng về plan quảng cáo.
        Phân tích và trả về JSON với:
        - feedback_type (approved/revision_needed/rejected/no_response/unclear)
        - confidence_score (0-1)
        - specific_requests (array)
        - urgency_level (low/medium/high)
        - sentiment (positive/neutral/negative)
        - next_action (proceed/revise/escalate/close)
        ```

5.  **🧠 Node: Function → Process Feedback Analysis**
      * **Cải tiến:**
        - Kiểm tra `maxRevisions` limit (default: 3)
        - Auto-escalation khi đạt max revisions
        - Priority assignment dựa trên urgency và sentiment
        - Tracking revision history

6.  **📊 Node: Google Sheets → Enhanced Feedback Logging**
      * Ghi chi tiết: `feedbackAnalysis`, `revisionCount`, `maxRevisionsReached`
      * Update `priority` và `lastActivity`
      * Smart status routing: `Approved` / `Revision Required` / `Max Revisions Reached`

-----

### **Bước 8: REVISION & CHỐT PLAN**

  * **Mục tiêu:** Thực hiện các chỉnh sửa theo yêu cầu của khách hàng một cách nhanh chóng và chính xác.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi trạng thái plan là `Revision Needed`**.
2.  **Node: Google Docs API → Compare versions**
      * Sử dụng API để lấy nội dung text của phiên bản trước và các yêu cầu mới. (Bước này nâng cao, có thể bỏ qua và thực hiện thủ công).
3.  **Node: OpenAI → Suggest revised version**
      * **Prompt mẫu:**
        ```
        Khách hàng yêu cầu những thay đổi sau: "[Dán tóm tắt phản hồi của khách vào đây]".
        Dựa trên bản kế hoạch hiện tại dưới đây, hãy viết lại các phần bị ảnh hưởng (ví dụ: bảng ngân sách, KPI) để đáp ứng yêu cầu của khách.

        Bản kế hoạch hiện tại:
        """
        [Dán nội dung plan cần sửa]
        """
        ```
4.  **Node: Chat/Email → Send for final approval**
      * Gửi bản revise cho Sale/Account Plan để xác nhận lại với khách hàng.
5.  **Node: Google Sheets/Airtable → Update Status**
      * Khi khách hàng đã đồng ý, cập nhật trạng thái thành `Approved`.

-----

### **Bước 9: TRẢ KẾT QUẢ & LƯU TRỮ**

  * **Mục tiêu:** Bàn giao plan cuối cùng và lưu trữ tài liệu một cách khoa học để dễ dàng tra cứu sau này.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi trạng thái plan là `Approved`**.
2.  **Node: Google Drive API → Upload final file**
      * **Action:**
          * Tạo một phiên bản PDF của file Google Sheet/Docs.
          * Kiểm tra xem thư mục `Clients/{{CLIENT_NAME}}/Plans/{{YEAR}}` đã tồn tại chưa. Nếu chưa thì tạo mới.
          * Upload file PDF vào thư mục đó với tên chuẩn: `{{CLIENT_NAME}}_Plan_{{YYYYMMDD}}_Final.pdf`.
3.  **Node: Google Sheets/Airtable → Log the final link**
      * Lấy đường dẫn có thể chia sẻ của file đã upload.
      * Ghi đường dẫn này vào trường `Final Plan URL` trong bản ghi tương ứng. Cập nhật trạng thái thành `Completed`.
4.  **Node: Email → Send final plan to all stakeholders**
      * **Người nhận:** Sale, Leader, và các bên liên quan.
      * **Tiêu đề:** `[FINAL PLAN] Kế hoạch đã chốt cho khách hàng {{client_name}}`.
      * **Nội dung:** "Kế hoạch cho khách hàng **{{client\_name}}** đã được chốt. File cuối cùng được lưu tại đây: [Link file từ Google Drive]. Các bộ phận liên quan vui lòng chuẩn bị cho các bước tiếp theo. Cảm ơn team."

-----

## **🆕 X. HỆ THỐNG MONITORING VÀ CẢNH BÁO TỰ ĐỘNG**

### **Mục tiêu:** Đảm bảo không có order nào bị bỏ sót và phát hiện sớm các vấn đề trong quy trình.

#### **🚀 Tự động hóa Monitoring v2.0:**

1.  **⏰ Trigger: Cron → Scheduled Monitoring**
      * **Lịch chạy:** 9:00, 14:00, 17:00 hàng ngày
      * **Mục đích:** Kiểm tra định kỳ các order đang pending

2.  **📊 Node: Google Sheets → Check Overdue Orders**
      * **Filter:** Lấy tất cả orders không ở trạng thái `Completed` hoặc `Closed`
      * **Cải tiến:** Sử dụng typeVersion 5 với advanced filtering

3.  **🧠 Node: Function → Smart Overdue Detection**
      * **Logic thông minh:**
        ```javascript
        // Các điều kiện overdue
        if (status === 'New Order' && hoursSinceActivity > 2) {
          urgencyLevel = 'high';
        } else if (status === 'Pending Information' && hoursSinceActivity > 48) {
          urgencyLevel = informationRequestCount >= 2 ? 'critical' : 'high';
        } else if (status === 'Awaiting Client Response' && hoursSinceActivity > 72) {
          urgencyLevel = 'medium';
        }
        // Kiểm tra deadline approaching
        if (hoursToDeadline < 24 && hoursToDeadline > 0) {
          urgencyLevel = 'critical';
        }
        ```

4.  **🚨 Node: Gmail → Smart Alert System**
      * **Tiêu đề:** `[🚨 OVERDUE ALERT] {{urgencyLevel.toUpperCase()}} - Order {{orderId}}`
      * **Nội dung phân loại theo mức độ:**
        - **CRITICAL:** Liên hệ ngay với assignee, xem xét reassign
        - **HIGH:** Follow up với assignee, kiểm tra tiến độ
        - **MEDIUM:** Nhắc nhở assignee, cập nhật timeline

5.  **📈 Node: Analytics & Reporting**
      * Tracking metrics: Response time, completion rate, escalation rate
      * Weekly/Monthly reports tự động
      * Performance dashboard cho management

-----

## **📋 CHECKLIST TRIỂN KHAI WORKFLOW V2.0**

### **Environment Variables cần thiết:**
- `PLAN_SHEET_ID`: ID của Google Sheet chính
- `PLAN_SHEET_URL`: URL trực tiếp đến Google Sheet
- `LEADER_EMAIL`: Email của Leader team
- `PRODUCT_TEAM_EMAIL`: Email team sản phẩm
- `OPENAI_API_KEY`: API key cho OpenAI integration

### **Permissions cần thiết:**
- Gmail API: Read/Write/Send permissions
- Google Sheets API: Read/Write permissions
- Google Drive API: Read/Write permissions (cho file storage)
- OpenAI API: Chat completion permissions

### **Monitoring Dashboard:**
- Real-time order status tracking
- Performance metrics và KPIs
- Alert history và resolution tracking
- Team workload distribution